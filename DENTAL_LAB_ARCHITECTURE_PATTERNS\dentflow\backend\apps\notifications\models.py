"""
Notification Models for DentFlow
Multi-channel notification system for dental lab communication
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import uuid


class NotificationTemplate(models.Model):
    """
    Templates for different types of notifications
    """
    
    NOTIFICATION_TYPE_CHOICES = [
        ('case_created', 'Case Created'),
        ('case_updated', 'Case Updated'),
        ('case_completed', 'Case Completed'),
        ('case_overdue', 'Case Overdue'),
        ('payment_received', 'Payment Received'),
        ('payment_overdue', 'Payment Overdue'),
        ('appointment_reminder', 'Appointment Reminder'),
        ('quality_check_failed', 'Quality Check Failed'),
        ('system_maintenance', 'System Maintenance'),
    ]
    
    CHANNEL_CHOICES = [
        ('email', 'Email'),
        ('sms', 'SMS'),
        ('push', 'Push Notification'),
        ('in_app', 'In-App Notification'),
        ('slack', 'Slack'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='notification_templates')
    name = models.CharField(max_length=255)
    notification_type = models.CharField(max_length=50, choices=NOTIFICATION_TYPE_CHOICES)
    channel = models.CharField(max_length=20, choices=CHANNEL_CHOICES)
    
    # Template content
    subject = models.CharField(max_length=255, blank=True)  # For email/SMS
    body_template = models.TextField()  # Template with variables
    html_template = models.TextField(blank=True)  # HTML version for email
    
    # Template variables and settings
    variables = models.JSONField(default=list)  # Available template variables
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['tenant', 'notification_type', 'channel']),
            models.Index(fields=['is_active']),
        ]
        unique_together = ['tenant', 'notification_type', 'channel']
    
    def render_template(self, context):
        """Render template with provided context variables"""
        from django.template import Template, Context
        
        subject_template = Template(self.subject)
        body_template = Template(self.body_template)
        
        rendered_subject = subject_template.render(Context(context))
        rendered_body = body_template.render(Context(context))
        
        rendered_html = ''
        if self.html_template:
            html_template = Template(self.html_template)
            rendered_html = html_template.render(Context(context))
        
        return {
            'subject': rendered_subject,
            'body': rendered_body,
            'html': rendered_html
        }
    
    def __str__(self):
        return f'{self.name} ({self.get_channel_display()})'


class Notification(models.Model):
    """
    Individual notification instances
    """
    
    NOTIFICATION_TYPE_CHOICES = NotificationTemplate.NOTIFICATION_TYPE_CHOICES
    CHANNEL_CHOICES = NotificationTemplate.CHANNEL_CHOICES
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    
    # Recipient information
    recipient_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    recipient_email = models.EmailField(blank=True)
    
    # Related objects
    case = models.ForeignKey('cases.Case', on_delete=models.CASCADE, null=True, blank=True)
    
    # Notification content
    title = models.CharField(max_length=255)
    message = models.TextField()
    notification_type = models.CharField(max_length=50, choices=NOTIFICATION_TYPE_CHOICES)
    
    # Delivery settings
    channel = models.CharField(max_length=20, choices=CHANNEL_CHOICES)
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='normal')
    scheduled_for = models.DateTimeField(null=True, blank=True)  # For scheduled notifications
    
    # Status tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    is_read = models.BooleanField(default=False)
    sent_at = models.DateTimeField(null=True, blank=True)
    read_at = models.DateTimeField(null=True, blank=True)
    
    # Delivery tracking
    delivery_attempts = models.PositiveIntegerField(default=0)
    error_message = models.TextField(blank=True)
    metadata = models.JSONField(default=dict, blank=True)  # Additional data
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['recipient_user', 'is_read']),
            models.Index(fields=['status', 'scheduled_for']),
            models.Index(fields=['notification_type', 'created_at']),
            models.Index(fields=['case']),
        ]
        ordering = ['-created_at']
    
    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])
    
    def mark_as_sent(self):
        """Mark notification as sent"""
        self.status = 'sent'
        self.sent_at = timezone.now()
        self.save(update_fields=['status', 'sent_at'])
    
    def mark_as_failed(self, error_message):
        """Mark notification as failed with error message"""
        self.status = 'failed'
        self.error_message = error_message
        self.delivery_attempts += 1
        self.save(update_fields=['status', 'error_message', 'delivery_attempts'])
    
    def can_retry(self):
        """Check if notification can be retried"""
        return self.status == 'failed' and self.delivery_attempts < 3
    
    def __str__(self):
        recipient = self.recipient_user.username if self.recipient_user else self.recipient_email
        return f'{self.title} -> {recipient}'


class NotificationPreference(models.Model):
    """
    User preferences for different types of notifications
    """
    
    NOTIFICATION_TYPE_CHOICES = NotificationTemplate.NOTIFICATION_TYPE_CHOICES
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notification_preferences')
    notification_type = models.CharField(max_length=50, choices=NOTIFICATION_TYPE_CHOICES)
    
    # Channel preferences
    email_enabled = models.BooleanField(default=True)
    sms_enabled = models.BooleanField(default=False)
    push_enabled = models.BooleanField(default=True)
    in_app_enabled = models.BooleanField(default=True)
    
    # Additional settings
    quiet_hours = models.JSONField(default=dict, blank=True)  # {'start': '22:00', 'end': '08:00'}
    frequency_limit = models.PositiveIntegerField(default=0)  # Max notifications per day (0 = unlimited)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'notification_type']),
        ]
        unique_together = ['user', 'notification_type']
    
    def is_channel_enabled(self, channel):
        """Check if specific channel is enabled for this preference"""
        channel_mapping = {
            'email': self.email_enabled,
            'sms': self.sms_enabled,
            'push': self.push_enabled,
            'in_app': self.in_app_enabled,
        }
        return channel_mapping.get(channel, False)
    
    def is_in_quiet_hours(self):
        """Check if current time is within quiet hours"""
        if not self.quiet_hours or 'start' not in self.quiet_hours:
            return False
        
        from datetime import time
        import datetime
        
        try:
            start_time = datetime.datetime.strptime(self.quiet_hours['start'], '%H:%M').time()
            end_time = datetime.datetime.strptime(self.quiet_hours['end'], '%H:%M').time()
            current_time = timezone.now().time()
            
            if start_time <= end_time:
                return start_time <= current_time <= end_time
            else:  # Quiet hours span midnight
                return current_time >= start_time or current_time <= end_time
        except (ValueError, KeyError):
            return False
    
    def __str__(self):
        return f'{self.user.username} - {self.get_notification_type_display()}'


# Signal handlers for automatic notification creation
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender='cases.Case')
def create_case_notifications(sender, instance, created, **kwargs):
    """Create notifications when case is created or updated"""
    if created:
        # Create case created notification
        Notification.objects.create(
            recipient_email=instance.clinic.email,
            case=instance,
            title=f'New Case Created: {instance.id}',
            message=f'A new case for {instance.patient_name} has been created.',
            notification_type='case_created',
            channel='email'
        )
    else:
        # Create case updated notification if status changed
        if hasattr(instance, '_state') and instance._state.adding is False:
            Notification.objects.create(
                recipient_email=instance.clinic.email,
                case=instance,
                title=f'Case Updated: {instance.id}',
                message=f'Case for {instance.patient_name} has been updated to {instance.status}.',
                notification_type='case_updated',
                channel='email'
            )