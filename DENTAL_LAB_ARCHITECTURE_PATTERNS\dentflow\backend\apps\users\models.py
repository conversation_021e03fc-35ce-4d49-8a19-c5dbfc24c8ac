"""
Simplified User Models for DentFlow
Custom user model without complex tenant dependencies for initial setup
"""

from django.contrib.auth.models import AbstractUser
from django.db import models
import uuid


class User(AbstractUser):
    """
    Custom User model with basic fields
    """
    
    ROLE_CHOICES = [
        ('admin', 'Lab Admin'),
        ('receptionist', 'Receptionist'),
        ('technician', 'Technician'),
        ('dentist', 'Dentist'),
        ('clinic_admin', 'Clinic Admin'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    email = models.EmailField(unique=True)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='dentist')
    
    # Additional profile fields
    phone = models.CharField(max_length=20, blank=True)
    profile_picture = models.ImageField(upload_to='profiles/', blank=True, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'role']
    
    class Meta:
        indexes = [
            models.Index(fields=['role']),
            models.Index(fields=['email']),
        ]
    
    @property
    def is_lab_user(self):
        """Check if user belongs to lab (admin, receptionist, technician)"""
        return self.role in ['admin', 'receptionist', 'technician']
    
    @property
    def is_clinic_user(self):
        """Check if user belongs to clinic (dentist, clinic_admin)"""
        return self.role in ['dentist', 'clinic_admin']
    
    def __str__(self):
        return f"{self.email} ({self.role})"
