"""
Django Admin for Notifications App
Notification management and delivery for DentFlow
"""

from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from apps.notifications.models import Notification, NotificationTemplate, NotificationPreference


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Admin interface for Notifications"""
    list_display = [
        'title', 'recipient_display', 'notification_type', 'channel_display',
        'status_display', 'sent_at', 'created_at'
    ]
    list_filter = [
        'notification_type', 'channel', 'status', 'is_read', 'sent_at', 'created_at'
    ]
    search_fields = ['title', 'message', 'recipient_email', 'case__id']
    readonly_fields = [
        'id', 'sent_at', 'read_at', 'delivery_attempts', 'error_message',
        'created_at', 'updated_at'
    ]
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Notification Content', {
            'fields': ('title', 'message', 'notification_type')
        }),
        ('Recipient Information', {
            'fields': ('recipient_user', 'recipient_email', 'case')
        }),
        ('Delivery Settings', {
            'fields': ('channel', 'priority', 'scheduled_for')
        }),
        ('Status & Tracking', {
            'fields': ('status', 'is_read', 'sent_at', 'read_at')
        }),
        ('Delivery Details', {
            'fields': ('delivery_attempts', 'error_message', 'metadata'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def recipient_display(self, obj):
        """Display recipient information"""
        if obj.recipient_user:
            return f'{obj.recipient_user.username} ({obj.recipient_user.email})'
        elif obj.recipient_email:
            return obj.recipient_email
        return 'No recipient'
    recipient_display.short_description = 'Recipient'
    
    def channel_display(self, obj):
        """Display notification channel with icon"""
        icons = {
            'email': '📧',
            'sms': '📱',
            'push': '🔔',
            'in_app': '🔔',
            'slack': '💬'
        }
        icon = icons.get(obj.channel, '📢')
        return f'{icon} {obj.get_channel_display()}'
    channel_display.short_description = 'Channel'
    
    def status_display(self, obj):
        """Display status with color coding"""
        colors = {
            'pending': '#ff9800',
            'sent': '#4caf50',
            'failed': '#f44336',
            'delivered': '#2196f3'
        }
        color = colors.get(obj.status, '#666')
        
        status_text = obj.get_status_display()
        if obj.is_read and obj.status == 'sent':
            status_text = '✅ Read'
            color = '#4caf50'
        
        return format_html(
            '<span style="background: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 11px; font-weight: bold;">{}</span>',
            color, status_text
        )
    status_display.short_description = 'Status'
    
    actions = ['mark_as_read', 'retry_failed', 'mark_as_sent']
    
    def mark_as_read(self, request, queryset):
        """Mark selected notifications as read"""
        updated = queryset.update(is_read=True, read_at=timezone.now())
        self.message_user(request, f'{updated} notifications marked as read.')
    mark_as_read.short_description = 'Mark as read'
    
    def retry_failed(self, request, queryset):
        """Retry failed notifications"""
        failed_notifications = queryset.filter(status='failed')
        updated = failed_notifications.update(status='pending', error_message='')
        self.message_user(request, f'{updated} failed notifications queued for retry.')
    retry_failed.short_description = 'Retry failed notifications'
    
    def mark_as_sent(self, request, queryset):
        """Mark selected notifications as sent"""
        updated = queryset.update(status='sent', sent_at=timezone.now())
        self.message_user(request, f'{updated} notifications marked as sent.')
    mark_as_sent.short_description = 'Mark as sent'


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    """Admin interface for Notification Templates"""
    list_display = [
        'name', 'notification_type', 'tenant', 'channel', 'is_active',
        'usage_count', 'created_at'
    ]
    list_filter = ['notification_type', 'channel', 'is_active', 'tenant']
    search_fields = ['name', 'subject', 'body_template']
    readonly_fields = ['created_at', 'updated_at', 'usage_count_display']
    
    fieldsets = (
        ('Template Information', {
            'fields': ('tenant', 'name', 'notification_type', 'channel')
        }),
        ('Content Templates', {
            'fields': ('subject', 'body_template', 'html_template')
        }),
        ('Settings', {
            'fields': ('is_active', 'variables')
        }),
        ('Usage Statistics', {
            'fields': ('usage_count_display',),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def usage_count(self, obj):
        """Show how many times this template has been used"""
        # This would need to be tracked in the Notification model
        return 0  # Placeholder
    usage_count.short_description = 'Used'
    
    def usage_count_display(self, obj):
        """Display usage statistics"""
        return f'Template used {self.usage_count(obj)} times'
    usage_count_display.short_description = 'Usage Statistics'
    
    actions = ['activate_templates', 'deactivate_templates', 'duplicate_templates']
    
    def activate_templates(self, request, queryset):
        """Activate selected templates"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} templates activated.')
    activate_templates.short_description = 'Activate selected templates'
    
    def deactivate_templates(self, request, queryset):
        """Deactivate selected templates"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} templates deactivated.')
    deactivate_templates.short_description = 'Deactivate selected templates'
    
    def duplicate_templates(self, request, queryset):
        """Duplicate selected templates"""
        count = 0
        for template in queryset:
            template.pk = None
            template.name = f'{template.name} (Copy)'
            template.save()
            count += 1
        self.message_user(request, f'{count} templates duplicated.')
    duplicate_templates.short_description = 'Duplicate selected templates'


@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(admin.ModelAdmin):
    """Admin interface for Notification Preferences"""
    list_display = [
        'user', 'notification_type', 'email_enabled', 'sms_enabled',
        'push_enabled', 'updated_at'
    ]
    list_filter = [
        'notification_type', 'email_enabled', 'sms_enabled', 'push_enabled'
    ]
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('User Information', {
            'fields': ('user', 'notification_type')
        }),
        ('Channel Preferences', {
            'fields': ('email_enabled', 'sms_enabled', 'push_enabled', 'in_app_enabled')
        }),
        ('Additional Settings', {
            'fields': ('quiet_hours', 'frequency_limit'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['enable_all_channels', 'disable_all_channels', 'reset_to_defaults']
    
    def enable_all_channels(self, request, queryset):
        """Enable all notification channels"""
        updated = queryset.update(
            email_enabled=True,
            sms_enabled=True,
            push_enabled=True,
            in_app_enabled=True
        )
        self.message_user(request, f'Enabled all channels for {updated} preferences.')
    enable_all_channels.short_description = 'Enable all channels'
    
    def disable_all_channels(self, request, queryset):
        """Disable all notification channels"""
        updated = queryset.update(
            email_enabled=False,
            sms_enabled=False,
            push_enabled=False,
            in_app_enabled=False
        )
        self.message_user(request, f'Disabled all channels for {updated} preferences.')
    disable_all_channels.short_description = 'Disable all channels'
    
    def reset_to_defaults(self, request, queryset):
        """Reset preferences to default values"""
        updated = queryset.update(
            email_enabled=True,
            sms_enabled=False,
            push_enabled=True,
            in_app_enabled=True
        )
        self.message_user(request, f'Reset {updated} preferences to defaults.')
    reset_to_defaults.short_description = 'Reset to defaults'