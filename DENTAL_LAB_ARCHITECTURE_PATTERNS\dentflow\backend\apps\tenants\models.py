"""
Tenant Models for Multi-tenant DentFlow Architecture
"""

from django.db import models
from django.core.validators import MinValueValidator
import uuid


class Tenant(models.Model):
    """
    Tenant model representing a dental laboratory
    """
    
    PLAN_CHOICES = [
        ('starter', 'Starter Plan'),
        ('professional', 'Professional Plan'), 
        ('enterprise', 'Enterprise Plan'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=255)
    subdomain = models.CharField(max_length=100, unique=True)
    contact_email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    website = models.URLField(blank=True)
    
    # Subscription details
    plan = models.CharField(max_length=20, choices=PLAN_CHOICES, default='starter')
    max_users = models.PositiveIntegerField(default=5)
    max_cases_per_month = models.PositiveIntegerField(default=100)
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['subdomain']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return self.name


class TenantSettings(models.Model):
    """
    Configurable settings for each tenant
    """
    
    TIMEZONE_CHOICES = [
        ('UTC', 'UTC'),
        ('US/Eastern', 'Eastern Time'),
        ('US/Central', 'Central Time'),
        ('US/Mountain', 'Mountain Time'),
        ('US/Pacific', 'Pacific Time'),
        ('Europe/London', 'London'),
        ('Europe/Berlin', 'Berlin'),
    ]
    
    CURRENCY_CHOICES = [
        ('USD', 'US Dollar'),
        ('EUR', 'Euro'),
        ('GBP', 'British Pound'),
        ('CAD', 'Canadian Dollar'),
    ]
    
    LANGUAGE_CHOICES = [
        ('en', 'English'),
        ('es', 'Spanish'),
        ('fr', 'French'),
        ('de', 'German'),
    ]
    
    tenant = models.OneToOneField(Tenant, on_delete=models.CASCADE, related_name='settings')
    
    # Localization
    timezone = models.CharField(max_length=50, choices=TIMEZONE_CHOICES, default='UTC')
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD')
    language = models.CharField(max_length=2, choices=LANGUAGE_CHOICES, default='en')
    date_format = models.CharField(max_length=20, default='MM/DD/YYYY')
    
    # Business settings
    business_hours = models.JSONField(default=dict)  # Store business hours
    default_workflow = models.JSONField(default=list)  # Default workflow stages
    
    # Notification settings
    email_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)
    slack_webhook = models.URLField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f'{self.tenant.name} Settings'


class TenantSubscription(models.Model):
    """
    Subscription and billing information for tenants
    """
    
    PLAN_CHOICES = [
        ('starter', 'Starter Plan - $29/month'),
        ('professional', 'Professional Plan - $79/month'),
        ('enterprise', 'Enterprise Plan - $199/month'),
    ]
    
    tenant = models.OneToOneField(Tenant, on_delete=models.CASCADE, related_name='subscription')
    plan = models.CharField(max_length=20, choices=PLAN_CHOICES)
    
    # Subscription status
    is_active = models.BooleanField(default=True)
    is_trial = models.BooleanField(default=False)
    
    # Billing period
    start_date = models.DateField()
    end_date = models.DateField()
    monthly_price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0)])
    
    # Payment integration
    stripe_subscription_id = models.CharField(max_length=100, blank=True)
    last_payment_date = models.DateField(null=True, blank=True)
    next_billing_date = models.DateField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['plan', 'is_active']),
            models.Index(fields=['end_date']),
        ]
    
    def __str__(self):
        return f'{self.tenant.name} - {self.plan}'