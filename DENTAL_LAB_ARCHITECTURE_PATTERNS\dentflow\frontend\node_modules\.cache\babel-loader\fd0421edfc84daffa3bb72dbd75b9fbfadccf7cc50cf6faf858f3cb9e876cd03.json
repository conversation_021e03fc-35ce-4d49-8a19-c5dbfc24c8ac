{"ast": null, "code": "'use client';\n\nimport { useSyncExternalStore as useSyncExternalStore$1 } from 'use-sync-external-store/shim/index.js';\nconst useSyncExternalStore = useSyncExternalStore$1;\nexport { useSyncExternalStore };", "map": {"version": 3, "names": ["useSyncExternalStore", "useSyncExternalStore$1"], "sources": ["C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\node_modules\\@tanstack\\react-query\\src\\useSyncExternalStore.ts"], "sourcesContent": ["'use client'\n// Temporary workaround due to an issue with react-native uSES - https://github.com/TanStack/query/pull/3601\nimport { useSyncExternalStore as uSES } from 'use-sync-external-store/shim/index.js'\n\nexport const useSyncExternalStore = uSES\n"], "mappings": ";;;AAIO,MAAAA,oBAAA,GAAAC,sBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}