{"version": 3, "file": "remove-accents.js", "sources": ["../../src/remove-accents.ts"], "sourcesContent": ["const characterMap: Record<string, string> = {\n  À: 'A',\n  Á: 'A',\n  Â: 'A',\n  Ã: 'A',\n  Ä: 'A',\n  Å: 'A',\n  Ấ: 'A',\n  Ắ: 'A',\n  Ẳ: 'A',\n  Ẵ: 'A',\n  Ặ: 'A',\n  Æ: 'AE',\n  Ầ: 'A',\n  Ằ: 'A',\n  Ȃ: 'A',\n  Ç: 'C',\n  Ḉ: 'C',\n  È: 'E',\n  É: 'E',\n  Ê: 'E',\n  Ë: 'E',\n  Ế: 'E',\n  Ḗ: 'E',\n  Ề: 'E',\n  Ḕ: 'E',\n  Ḝ: 'E',\n  Ȇ: 'E',\n  Ì: 'I',\n  Í: 'I',\n  Î: 'I',\n  Ï: 'I',\n  Ḯ: 'I',\n  Ȋ: 'I',\n  Ð: 'D',\n  Ñ: 'N',\n  Ò: 'O',\n  Ó: 'O',\n  Ô: 'O',\n  Õ: 'O',\n  Ö: 'O',\n  Ø: 'O',\n  Ố: 'O',\n  Ṍ: 'O',\n  Ṓ: 'O',\n  Ȏ: 'O',\n  Ù: 'U',\n  Ú: 'U',\n  Û: 'U',\n  Ü: 'U',\n  Ý: 'Y',\n  à: 'a',\n  á: 'a',\n  â: 'a',\n  ã: 'a',\n  ä: 'a',\n  å: 'a',\n  ấ: 'a',\n  ắ: 'a',\n  ẳ: 'a',\n  ẵ: 'a',\n  ặ: 'a',\n  æ: 'ae',\n  ầ: 'a',\n  ằ: 'a',\n  ȃ: 'a',\n  ç: 'c',\n  ḉ: 'c',\n  è: 'e',\n  é: 'e',\n  ê: 'e',\n  ë: 'e',\n  ế: 'e',\n  ḗ: 'e',\n  ề: 'e',\n  ḕ: 'e',\n  ḝ: 'e',\n  ȇ: 'e',\n  ì: 'i',\n  í: 'i',\n  î: 'i',\n  ï: 'i',\n  ḯ: 'i',\n  ȋ: 'i',\n  ð: 'd',\n  ñ: 'n',\n  ò: 'o',\n  ó: 'o',\n  ô: 'o',\n  õ: 'o',\n  ö: 'o',\n  ø: 'o',\n  ố: 'o',\n  ṍ: 'o',\n  ṓ: 'o',\n  ȏ: 'o',\n  ù: 'u',\n  ú: 'u',\n  û: 'u',\n  ü: 'u',\n  ý: 'y',\n  ÿ: 'y',\n  Ā: 'A',\n  ā: 'a',\n  Ă: 'A',\n  ă: 'a',\n  Ą: 'A',\n  ą: 'a',\n  Ć: 'C',\n  ć: 'c',\n  Ĉ: 'C',\n  ĉ: 'c',\n  Ċ: 'C',\n  ċ: 'c',\n  Č: 'C',\n  č: 'c',\n  C̆: 'C',\n  c̆: 'c',\n  Ď: 'D',\n  ď: 'd',\n  Đ: 'D',\n  đ: 'd',\n  Ē: 'E',\n  ē: 'e',\n  Ĕ: 'E',\n  ĕ: 'e',\n  Ė: 'E',\n  ė: 'e',\n  Ę: 'E',\n  ę: 'e',\n  Ě: 'E',\n  ě: 'e',\n  Ĝ: 'G',\n  Ǵ: 'G',\n  ĝ: 'g',\n  ǵ: 'g',\n  Ğ: 'G',\n  ğ: 'g',\n  Ġ: 'G',\n  ġ: 'g',\n  Ģ: 'G',\n  ģ: 'g',\n  Ĥ: 'H',\n  ĥ: 'h',\n  Ħ: 'H',\n  ħ: 'h',\n  Ḫ: 'H',\n  ḫ: 'h',\n  Ĩ: 'I',\n  ĩ: 'i',\n  Ī: 'I',\n  ī: 'i',\n  Ĭ: 'I',\n  ĭ: 'i',\n  Į: 'I',\n  į: 'i',\n  İ: 'I',\n  ı: 'i',\n  Ĳ: 'IJ',\n  ĳ: 'ij',\n  Ĵ: 'J',\n  ĵ: 'j',\n  Ķ: 'K',\n  ķ: 'k',\n  Ḱ: 'K',\n  ḱ: 'k',\n  K̆: 'K',\n  k̆: 'k',\n  Ĺ: 'L',\n  ĺ: 'l',\n  Ļ: 'L',\n  ļ: 'l',\n  Ľ: 'L',\n  ľ: 'l',\n  Ŀ: 'L',\n  ŀ: 'l',\n  Ł: 'l',\n  ł: 'l',\n  Ḿ: 'M',\n  ḿ: 'm',\n  M̆: 'M',\n  m̆: 'm',\n  Ń: 'N',\n  ń: 'n',\n  Ņ: 'N',\n  ņ: 'n',\n  Ň: 'N',\n  ň: 'n',\n  ŉ: 'n',\n  N̆: 'N',\n  n̆: 'n',\n  Ō: 'O',\n  ō: 'o',\n  Ŏ: 'O',\n  ŏ: 'o',\n  Ő: 'O',\n  ő: 'o',\n  Œ: 'OE',\n  œ: 'oe',\n  P̆: 'P',\n  p̆: 'p',\n  Ŕ: 'R',\n  ŕ: 'r',\n  Ŗ: 'R',\n  ŗ: 'r',\n  Ř: 'R',\n  ř: 'r',\n  R̆: 'R',\n  r̆: 'r',\n  Ȓ: 'R',\n  ȓ: 'r',\n  Ś: 'S',\n  ś: 's',\n  Ŝ: 'S',\n  ŝ: 's',\n  Ş: 'S',\n  Ș: 'S',\n  ș: 's',\n  ş: 's',\n  Š: 'S',\n  š: 's',\n  Ţ: 'T',\n  ţ: 't',\n  ț: 't',\n  Ț: 'T',\n  Ť: 'T',\n  ť: 't',\n  Ŧ: 'T',\n  ŧ: 't',\n  T̆: 'T',\n  t̆: 't',\n  Ũ: 'U',\n  ũ: 'u',\n  Ū: 'U',\n  ū: 'u',\n  Ŭ: 'U',\n  ŭ: 'u',\n  Ů: 'U',\n  ů: 'u',\n  Ű: 'U',\n  ű: 'u',\n  Ų: 'U',\n  ų: 'u',\n  Ȗ: 'U',\n  ȗ: 'u',\n  V̆: 'V',\n  v̆: 'v',\n  Ŵ: 'W',\n  ŵ: 'w',\n  Ẃ: 'W',\n  ẃ: 'w',\n  X̆: 'X',\n  x̆: 'x',\n  Ŷ: 'Y',\n  ŷ: 'y',\n  Ÿ: 'Y',\n  Y̆: 'Y',\n  y̆: 'y',\n  Ź: 'Z',\n  ź: 'z',\n  Ż: 'Z',\n  ż: 'z',\n  Ž: 'Z',\n  ž: 'z',\n  ſ: 's',\n  ƒ: 'f',\n  Ơ: 'O',\n  ơ: 'o',\n  Ư: 'U',\n  ư: 'u',\n  Ǎ: 'A',\n  ǎ: 'a',\n  Ǐ: 'I',\n  ǐ: 'i',\n  Ǒ: 'O',\n  ǒ: 'o',\n  Ǔ: 'U',\n  ǔ: 'u',\n  Ǖ: 'U',\n  ǖ: 'u',\n  Ǘ: 'U',\n  ǘ: 'u',\n  Ǚ: 'U',\n  ǚ: 'u',\n  Ǜ: 'U',\n  ǜ: 'u',\n  Ứ: 'U',\n  ứ: 'u',\n  Ṹ: 'U',\n  ṹ: 'u',\n  Ǻ: 'A',\n  ǻ: 'a',\n  Ǽ: 'AE',\n  ǽ: 'ae',\n  Ǿ: 'O',\n  ǿ: 'o',\n  Þ: 'TH',\n  þ: 'th',\n  Ṕ: 'P',\n  ṕ: 'p',\n  Ṥ: 'S',\n  ṥ: 's',\n  X́: 'X',\n  x́: 'x',\n  Ѓ: 'Г',\n  ѓ: 'г',\n  Ќ: 'К',\n  ќ: 'к',\n  A̋: 'A',\n  a̋: 'a',\n  E̋: 'E',\n  e̋: 'e',\n  I̋: 'I',\n  i̋: 'i',\n  Ǹ: 'N',\n  ǹ: 'n',\n  Ồ: 'O',\n  ồ: 'o',\n  Ṑ: 'O',\n  ṑ: 'o',\n  Ừ: 'U',\n  ừ: 'u',\n  Ẁ: 'W',\n  ẁ: 'w',\n  Ỳ: 'Y',\n  ỳ: 'y',\n  Ȁ: 'A',\n  ȁ: 'a',\n  Ȅ: 'E',\n  ȅ: 'e',\n  Ȉ: 'I',\n  ȉ: 'i',\n  Ȍ: 'O',\n  ȍ: 'o',\n  Ȑ: 'R',\n  ȑ: 'r',\n  Ȕ: 'U',\n  ȕ: 'u',\n  B̌: 'B',\n  b̌: 'b',\n  Č̣: 'C',\n  č̣: 'c',\n  Ê̌: 'E',\n  ê̌: 'e',\n  F̌: 'F',\n  f̌: 'f',\n  Ǧ: 'G',\n  ǧ: 'g',\n  Ȟ: 'H',\n  ȟ: 'h',\n  J̌: 'J',\n  ǰ: 'j',\n  Ǩ: 'K',\n  ǩ: 'k',\n  M̌: 'M',\n  m̌: 'm',\n  P̌: 'P',\n  p̌: 'p',\n  Q̌: 'Q',\n  q̌: 'q',\n  Ř̩: 'R',\n  ř̩: 'r',\n  Ṧ: 'S',\n  ṧ: 's',\n  V̌: 'V',\n  v̌: 'v',\n  W̌: 'W',\n  w̌: 'w',\n  X̌: 'X',\n  x̌: 'x',\n  Y̌: 'Y',\n  y̌: 'y',\n  A̧: 'A',\n  a̧: 'a',\n  B̧: 'B',\n  b̧: 'b',\n  Ḑ: 'D',\n  ḑ: 'd',\n  Ȩ: 'E',\n  ȩ: 'e',\n  Ɛ̧: 'E',\n  ɛ̧: 'e',\n  Ḩ: 'H',\n  ḩ: 'h',\n  I̧: 'I',\n  i̧: 'i',\n  Ɨ̧: 'I',\n  ɨ̧: 'i',\n  M̧: 'M',\n  m̧: 'm',\n  O̧: 'O',\n  o̧: 'o',\n  Q̧: 'Q',\n  q̧: 'q',\n  U̧: 'U',\n  u̧: 'u',\n  X̧: 'X',\n  x̧: 'x',\n  Z̧: 'Z',\n  z̧: 'z',\n}\n\nconst chars = Object.keys(characterMap).join('|')\nconst allAccents = new RegExp(chars, 'g')\n\nexport function removeAccents(str: string) {\n  return str.replace(allAccents, match => {\n    return characterMap[match]!\n  })\n}\n"], "names": ["characterMap", "À", "Á", "Â", "Ã", "Ä", "Å", "Ấ", "Ắ", "Ẳ", "Ẵ", "Ặ", "<PERSON>", "Ầ", "Ằ", "Ȃ", "Ç", "Ḉ", "È", "É", "Ê", "Ë", "Ế", "Ḗ", "Ề", "Ḕ", "Ḝ", "Ȇ", "Ì", "Í", "Î", "Ï", "Ḯ", "Ȋ", "Ð", "Ñ", "Ò", "<PERSON>", "Ô", "Õ", "Ö", "Ø", "Ố", "Ṍ", "Ṓ", "Ȏ", "Ù", "Ú", "Û", "Ü", "Ý", "à", "á", "â", "ã", "ä", "å", "ấ", "ắ", "ẳ", "ẵ", "ặ", "æ", "ầ", "ằ", "ȃ", "ç", "ḉ", "è", "é", "ê", "ë", "ế", "ḗ", "ề", "ḕ", "ḝ", "ȇ", "ì", "í", "î", "ï", "ḯ", "ȋ", "ð", "ñ", "ò", "ó", "ô", "õ", "ö", "ø", "ố", "ṍ", "ṓ", "ȏ", "ù", "ú", "û", "ü", "ý", "ÿ", "Ā", "ā", "Ă", "ă", "Ą", "ą", "Ć", "ć", "Ĉ", "ĉ", "Ċ", "ċ", "Č", "č", "C̆", "c̆", "Ď", "ď", "Đ", "đ", "Ē", "ē", "Ĕ", "ĕ", "Ė", "ė", "Ę", "ę", "Ě", "ě", "Ĝ", "Ǵ", "ĝ", "ǵ", "Ğ", "ğ", "Ġ", "ġ", "Ģ", "ģ", "Ĥ", "ĥ", "Ħ", "ħ", "Ḫ", "ḫ", "Ĩ", "ĩ", "Ī", "ī", "Ĭ", "ĭ", "Į", "į", "İ", "ı", "Ĳ", "ĳ", "Ĵ", "ĵ", "Ķ", "ķ", "Ḱ", "ḱ", "K̆", "k̆", "Ĺ", "ĺ", "Ļ", "ļ", "Ľ", "ľ", "Ŀ", "ŀ", "Ł", "ł", "Ḿ", "ḿ", "M̆", "m̆", "Ń", "ń", "Ņ", "ņ", "Ň", "ň", "ŉ", "N̆", "n̆", "Ō", "<PERSON>", "Ŏ", "ŏ", "Ő", "ő", "Œ", "œ", "P̆", "p̆", "Ŕ", "ŕ", "Ŗ", "ŗ", "Ř", "ř", "R̆", "r̆", "Ȓ", "ȓ", "Ś", "ś", "Ŝ", "ŝ", "Ş", "Ș", "ș", "ş", "Š", "š", "Ţ", "ţ", "ț", "Ț", "Ť", "ť", "Ŧ", "ŧ", "T̆", "t̆", "Ũ", "ũ", "Ū", "ū", "Ŭ", "ŭ", "Ů", "ů", "Ű", "ű", "Ų", "ų", "Ȗ", "ȗ", "V̆", "v̆", "Ŵ", "ŵ", "Ẃ", "ẃ", "X̆", "x̆", "Ŷ", "ŷ", "Ÿ", "Y̆", "y̆", "Ź", "ź", "Ż", "ż", "Ž", "ž", "ſ", "ƒ", "Ơ", "ơ", "Ư", "ư", "Ǎ", "ǎ", "Ǐ", "ǐ", "Ǒ", "ǒ", "Ǔ", "ǔ", "Ǖ", "ǖ", "Ǘ", "ǘ", "Ǚ", "ǚ", "Ǜ", "ǜ", "Ứ", "ứ", "Ṹ", "ṹ", "Ǻ", "ǻ", "Ǽ", "ǽ", "Ǿ", "ǿ", "Þ", "þ", "Ṕ", "ṕ", "Ṥ", "ṥ", "X́", "x́", "Ѓ", "ѓ", "Ќ", "ќ", "A̋", "a̋", "E̋", "e̋", "I̋", "i̋", "Ǹ", "ǹ", "Ồ", "ồ", "Ṑ", "ṑ", "Ừ", "ừ", "Ẁ", "ẁ", "Ỳ", "ỳ", "Ȁ", "ȁ", "Ȅ", "ȅ", "Ȉ", "ȉ", "Ȍ", "ȍ", "Ȑ", "ȑ", "Ȕ", "ȕ", "B̌", "b̌", "Č̣", "č̣", "Ê̌", "ê̌", "F̌", "f̌", "Ǧ", "ǧ", "Ȟ", "ȟ", "J̌", "ǰ", "Ǩ", "ǩ", "M̌", "m̌", "P̌", "p̌", "Q̌", "q̌", "Ř̩", "ř̩", "Ṧ", "ṧ", "V̌", "v̌", "W̌", "w̌", "X̌", "x̌", "Y̌", "y̌", "A̧", "a̧", "B̧", "b̧", "Ḑ", "ḑ", "Ȩ", "ȩ", "Ɛ̧", "ɛ̧", "Ḩ", "ḩ", "I̧", "i̧", "Ɨ̧", "ɨ̧", "M̧", "m̧", "O̧", "o̧", "Q̧", "q̧", "U̧", "u̧", "X̧", "x̧", "Z̧", "z̧", "chars", "Object", "keys", "join", "allAccents", "RegExp", "removeAccents", "str", "replace", "match"], "mappings": ";;;;;;;;;;;;AAAA,MAAMA,YAAoC,GAAG;AAC3CC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,IAAI;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,IAAI;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,IAAI;AACPC,EAAAA,CAAC,EAAE,IAAI;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,IAAI;AACPC,EAAAA,CAAC,EAAE,IAAI;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,IAAI;AACPC,EAAAA,CAAC,EAAE,IAAI;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,IAAI;AACPC,EAAAA,CAAC,EAAE,IAAI;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,CAAC,EAAE,GAAG;AACNC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAG;AACPC,EAAAA,EAAE,EAAE,GAAA;AACN,CAAC,CAAA;AAED,MAAMC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAAClZ,YAAY,CAAC,CAACmZ,IAAI,CAAC,GAAG,CAAC,CAAA;AACjD,MAAMC,UAAU,GAAG,IAAIC,MAAM,CAACL,KAAK,EAAE,GAAG,CAAC,CAAA;AAElC,SAASM,aAAaA,CAACC,GAAW,EAAE;AACzC,EAAA,OAAOA,GAAG,CAACC,OAAO,CAACJ,UAAU,EAAEK,KAAK,IAAI;IACtC,OAAOzZ,YAAY,CAACyZ,KAAK,CAAC,CAAA;AAC5B,GAAC,CAAC,CAAA;AACJ;;;;"}