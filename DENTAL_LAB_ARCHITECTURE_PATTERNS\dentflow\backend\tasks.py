"""
Celery Tasks for DentFlow
Async processing for workflow automation, notifications, and background jobs
"""

from celery import shared_task
from django.utils import timezone
from apps.cases.models import Case as DjangoCase, Tenant
from bootstrap import get_message_bus
from domain import commands
from datetime import timedelta
import logging


logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def process_workflow_automation(self, tenant_id=None):
    """
    Periodic task to process workflow automation
    Runs every 5 minutes via Celery Beat
    """
    
    try:
        if tenant_id:
            tenants = [Tenant.objects.get(id=tenant_id, is_active=True)]
        else:
            tenants = Tenant.objects.filter(is_active=True)
        
        total_processed = 0
        
        for tenant in tenants:
            logger.info(f"Processing workflow automation for tenant: {tenant.name}")
            
            # Auto-assign unassigned cases
            assigned_count = auto_assign_technicians(str(tenant.id))
            
            # Check for SLA violations
            sla_violations = check_sla_violations(str(tenant.id))
            
            # Process automatic workflow transitions
            transitions = process_automatic_transitions(str(tenant.id))
            
            total_processed += assigned_count + len(sla_violations) + transitions
            
            logger.info(
                f"Tenant {tenant.name}: {assigned_count} assigned, "
                f"{len(sla_violations)} SLA alerts, {transitions} transitions"
            )
        
        return {
            'status': 'success',
            'tenants_processed': len(tenants),
            'total_actions': total_processed
        }
        
    except Exception as e:
        logger.error(f"Workflow automation task failed: {e}")
        
        # Retry with exponential backoff
        countdown = 2 ** self.request.retries * 60  # 1min, 2min, 4min
        raise self.retry(countdown=countdown, exc=e)


@shared_task
def auto_assign_technicians(tenant_id):
    """Auto-assign technicians to unassigned cases"""
    
    try:
        unassigned_cases = DjangoCase.objects.filter(
            tenant_id=tenant_id,
            assigned_technician_id__isnull=True,
            status__in=['received', 'design', 'milling', 'sintering'],
            deleted_at__isnull=True
        ).order_by('priority', 'created_at')
        
        message_bus = get_message_bus(tenant_id=tenant_id)
        assigned_count = 0
        
        for django_case in unassigned_cases:
            try:
                domain_case = django_case.to_domain()
                
                if not domain_case.workflow_stages:
                    continue
                
                current_stage = domain_case.workflow_stages[domain_case.current_stage_index]
                technician_id = find_available_technician(current_stage.department, tenant_id)
                
                if technician_id:
                    command = commands.AssignTechnician(
                        case_id=django_case.id,
                        technician_id=technician_id,
                        stage=current_stage.name,
                        tenant_id=tenant_id
                    )
                    
                    message_bus.handle(command)
                    assigned_count += 1
                    
                    logger.info(f"Auto-assigned {technician_id} to case {django_case.id}")
                
            except Exception as e:
                logger.error(f"Failed to auto-assign case {django_case.id}: {e}")
                continue
        
        return assigned_count
        
    except Exception as e:
        logger.error(f"Auto-assignment task failed for tenant {tenant_id}: {e}")
        return 0


@shared_task
def check_sla_violations(tenant_id):
    """Check for SLA violations and send alerts"""
    
    try:
        now = timezone.now()
        violations = []
        
        # Cases approaching due date (within 2 hours)
        approaching_due = DjangoCase.objects.filter(
            tenant_id=tenant_id,
            due_date__lte=now + timedelta(hours=2),
            due_date__gt=now,
            status__in=['received', 'design', 'milling', 'sintering', 'qc'],
            deleted_at__isnull=True
        )
        
        # Overdue cases
        overdue_cases = DjangoCase.objects.filter(
            tenant_id=tenant_id,
            due_date__lt=now,
            status__in=['received', 'design', 'milling', 'sintering', 'qc'],
            deleted_at__isnull=True
        )
        
        # Send alerts for approaching due
        for case in approaching_due:
            hours_until_due = (case.due_date - now).total_seconds() / 3600
            send_sla_warning_alert.delay(case.id, hours_until_due)
            violations.append({
                'case_id': case.id,
                'type': 'approaching_due',
                'hours_until_due': hours_until_due
            })
        
        # Send alerts for overdue
        for case in overdue_cases:
            hours_overdue = (now - case.due_date).total_seconds() / 3600
            send_sla_breach_alert.delay(case.id, hours_overdue)
            violations.append({
                'case_id': case.id,
                'type': 'overdue',
                'hours_overdue': hours_overdue
            })
        
        logger.info(f"SLA check for tenant {tenant_id}: {len(violations)} violations found")
        return violations
        
    except Exception as e:
        logger.error(f"SLA check failed for tenant {tenant_id}: {e}")
        return []


@shared_task
def process_automatic_transitions(tenant_id):
    """Process automatic workflow transitions (e.g., sintering completion)"""
    
    try:
        # Cases in sintering that should be automatically moved to QC
        sintering_cases = DjangoCase.objects.filter(
            tenant_id=tenant_id,
            status='sintering',
            updated_at__lt=timezone.now() - timedelta(hours=8),  # 8+ hours
            deleted_at__isnull=True
        )
        
        message_bus = get_message_bus(tenant_id=tenant_id)
        transitions = 0
        
        for case in sintering_cases:
            try:
                # In a real implementation, this would check actual furnace status
                # For now, auto-advance after 8 hours
                
                command = commands.AdvanceCase(
                    case_id=case.id,
                    notes="Automatically advanced from sintering to QC",
                    tenant_id=tenant_id
                )
                
                message_bus.handle(command)
                transitions += 1
                
                logger.info(f"Auto-advanced case {case.id} from sintering to QC")
                
            except Exception as e:
                logger.error(f"Failed to auto-advance case {case.id}: {e}")
                continue
        
        return transitions
        
    except Exception as e:
        logger.error(f"Automatic transitions failed for tenant {tenant_id}: {e}")
        return 0


@shared_task
def send_sla_warning_alert(case_id, hours_until_due):
    """Send SLA warning alert"""
    
    try:
        # In a real implementation, this would:
        # 1. Get case details
        # 2. Find relevant users to notify
        # 3. Send email/SMS/Slack notifications
        # 4. Log the alert
        
        logger.warning(f"SLA WARNING: Case {case_id} due in {hours_until_due:.1f} hours")
        
        # Mock implementation
        return {
            'case_id': case_id,
            'alert_type': 'sla_warning',
            'hours_until_due': hours_until_due,
            'status': 'sent'
        }
        
    except Exception as e:
        logger.error(f"Failed to send SLA warning for case {case_id}: {e}")
        return {'status': 'failed', 'error': str(e)}


@shared_task
def send_sla_breach_alert(case_id, hours_overdue):
    """Send SLA breach alert"""
    
    try:
        logger.error(f"SLA BREACH: Case {case_id} overdue by {hours_overdue:.1f} hours")
        
        # Mock implementation
        return {
            'case_id': case_id,
            'alert_type': 'sla_breach',
            'hours_overdue': hours_overdue,
            'status': 'sent'
        }
        
    except Exception as e:
        logger.error(f"Failed to send SLA breach alert for case {case_id}: {e}")
        return {'status': 'failed', 'error': str(e)}


@shared_task
def send_case_created_notifications(case_id):
    """Send notifications when a case is created"""
    
    try:
        case = DjangoCase.objects.get(id=case_id)
        
        # Send confirmation email to clinic
        send_case_confirmation_email.delay(case_id)
        
        # Notify lab staff
        send_new_case_notification.delay(case_id)
        
        logger.info(f"Sent case creation notifications for {case_id}")
        
    except DjangoCase.DoesNotExist:
        logger.error(f"Case {case_id} not found for notifications")
    except Exception as e:
        logger.error(f"Failed to send case creation notifications: {e}")


@shared_task
def send_case_confirmation_email(case_id):
    """Send case confirmation email to clinic"""
    
    try:
        case = DjangoCase.objects.get(id=case_id)
        
        # Mock email sending
        logger.info(
            f"MOCK EMAIL: Case {case_id} confirmation sent to {case.clinic.email}"
        )
        
        return {'status': 'sent', 'recipient': case.clinic.email}
        
    except Exception as e:
        logger.error(f"Failed to send confirmation email for case {case_id}: {e}")
        return {'status': 'failed', 'error': str(e)}


@shared_task
def send_new_case_notification(case_id):
    """Send new case notification to lab staff"""
    
    try:
        case = DjangoCase.objects.get(id=case_id)
        
        # Mock notification
        logger.info(f"MOCK NOTIFICATION: New case {case_id} for {case.service_type}")
        
        return {'status': 'sent', 'case_id': case_id}
        
    except Exception as e:
        logger.error(f"Failed to send new case notification: {e}")
        return {'status': 'failed', 'error': str(e)}


def find_available_technician(department, tenant_id):
    """Find available technician for department"""
    
    # Mock implementation - would integrate with real scheduling system
    mock_technicians = {
        'CAD': 'tech_001',
        'CAM': 'tech_002',
        'Furnace': 'tech_003',
        'Printing': 'tech_004',
        'QC': 'tech_005'
    }
    
    return mock_technicians.get(department)