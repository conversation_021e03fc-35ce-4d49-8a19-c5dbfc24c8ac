"""
DentFlow Case Serializers
REST API serializers for Case models
"""

from rest_framework import serializers
from apps.cases.models import Case, Clinic, Tenant


class ClinicSerializer(serializers.ModelSerializer):
    """Serializer for Clinic model"""
    
    class Meta:
        model = Clinic
        fields = ['id', 'name', 'email', 'phone', 'address']
        read_only_fields = ['id']


class CaseSerializer(serializers.ModelSerializer):
    """Serializer for Case model with related data"""
    
    clinic = ClinicSerializer(read_only=True)
    days_until_due = serializers.SerializerMethodField()
    is_overdue = serializers.SerializerMethodField()
    current_stage = serializers.SerializerMethodField()
    
    class Meta:
        model = Case
        fields = [
            'id', 'clinic', 'patient_name', 'tooth_number', 'service_type',
            'priority', 'status', 'current_stage_index', 'workflow_stages',
            'assigned_technician_id', 'due_date', 'created_at', 'updated_at',
            'notes', 'files', 'days_until_due', 'is_overdue', 'current_stage'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'days_until_due', 
            'is_overdue', 'current_stage'
        ]
    
    def get_days_until_due(self, obj):
        """Calculate days until due date"""
        if not obj.due_date:
            return None
        
        from django.utils import timezone
        from datetime import timedelta
        
        delta = obj.due_date - timezone.now()
        return delta.days
    
    def get_is_overdue(self, obj):
        """Check if case is overdue"""
        if not obj.due_date:
            return False
        
        from django.utils import timezone
        return timezone.now() > obj.due_date
    
    def get_current_stage(self, obj):
        """Get current workflow stage name"""
        if (obj.current_stage_index < len(obj.workflow_stages) and 
            obj.workflow_stages):
            return obj.workflow_stages[obj.current_stage_index].get('name', '')
        return obj.status


class CreateCaseSerializer(serializers.Serializer):
    """Serializer for creating new cases"""
    
    clinic_id = serializers.UUIDField()
    patient_name = serializers.CharField(max_length=255)
    tooth_number = serializers.CharField(max_length=5)
    service_type = serializers.ChoiceField(choices=[
        ('crown', 'Crown'),
        ('bridge', 'Bridge'),
        ('denture', 'Denture'),
        ('implant', 'Implant'),
        ('veneer', 'Veneer'),
    ])
    priority = serializers.ChoiceField(
        choices=[('low', 'Low'), ('normal', 'Normal'), ('urgent', 'Urgent'), ('stat', 'STAT')],
        default='normal',
        required=False
    )
    notes = serializers.CharField(required=False, allow_blank=True)
    files = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True
    )
    
    def validate_clinic_id(self, value):
        """Validate that clinic exists and belongs to user's tenant"""
        request = self.context.get('request')
        if not request or not hasattr(request.user, 'tenant_id'):
            raise serializers.ValidationError("No tenant context available")
        
        try:
            clinic = Clinic.objects.get(id=value, tenant_id=request.user.tenant_id)
            return str(clinic.id)
        except Clinic.DoesNotExist:
            raise serializers.ValidationError("Clinic not found")
    
    def validate_tooth_number(self, value):
        """Validate tooth number using ISO 3950 notation"""
        if not value.isdigit():
            raise serializers.ValidationError("Tooth number must be numeric")
        
        tooth_num = int(value)
        if not (11 <= tooth_num <= 48):
            raise serializers.ValidationError(
                "Invalid tooth number. Must be between 11-48 (ISO 3950)"
            )
        
        return value


class CaseListSerializer(serializers.ModelSerializer):
    """Simplified serializer for case lists"""
    
    clinic_name = serializers.CharField(source='clinic.name', read_only=True)
    days_until_due = serializers.SerializerMethodField()
    is_overdue = serializers.SerializerMethodField()
    
    class Meta:
        model = Case
        fields = [
            'id', 'clinic_name', 'patient_name', 'tooth_number', 'service_type',
            'priority', 'status', 'assigned_technician_id', 'due_date',
            'created_at', 'days_until_due', 'is_overdue'
        ]
    
    def get_days_until_due(self, obj):
        """Calculate days until due date"""
        if not obj.due_date:
            return None
        
        from django.utils import timezone
        delta = obj.due_date - timezone.now()
        return delta.days
    
    def get_is_overdue(self, obj):
        """Check if case is overdue"""
        if not obj.due_date:
            return False
        
        from django.utils import timezone
        return timezone.now() > obj.due_date


class CaseStatusUpdateSerializer(serializers.Serializer):
    """Serializer for updating case status"""
    
    notes = serializers.CharField(required=False, allow_blank=True)
    quality_check_passed = serializers.BooleanField(default=True)


class AssignTechnicianSerializer(serializers.Serializer):
    """Serializer for assigning technician to case"""
    
    technician_id = serializers.CharField(max_length=50)
    stage = serializers.CharField(max_length=100, required=False)