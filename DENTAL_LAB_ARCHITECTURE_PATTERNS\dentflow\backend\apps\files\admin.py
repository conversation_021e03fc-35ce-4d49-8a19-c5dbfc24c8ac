"""
Django Admin for Files App
File management and storage for DentFlow
"""

from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.urls import reverse
from django.conf import settings
from apps.files.models import FileUpload, FileCategory, FileShare


@admin.register(FileCategory)
class FileCategoryAdmin(admin.ModelAdmin):
    """Admin interface for File Categories"""
    list_display = ['name', 'tenant', 'file_count', 'is_active', 'created_at']
    list_filter = ['is_active', 'tenant', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Category Information', {
            'fields': ('tenant', 'name', 'description', 'is_active')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def file_count(self, obj):
        """Show number of files in this category"""
        count = obj.files.count()
        if count > 0:
            url = reverse('admin:files_fileupload_changelist') + f'?category__id__exact={obj.id}'
            return format_html('<a href="{}">{} files</a>', url, count)
        return '0 files'
    file_count.short_description = 'Files'


@admin.register(FileUpload)
class FileUploadAdmin(admin.ModelAdmin):
    """Admin interface for File Uploads"""
    list_display = [
        'filename', 'case', 'category', 'file_type_display', 'file_size_display',
        'uploaded_by', 'is_active', 'created_at', 'thumbnail_preview'
    ]
    list_filter = ['file_type', 'category', 'is_active', 'created_at', 'case__status']
    search_fields = ['filename', 'original_filename', 'case__id', 'case__patient_name']
    readonly_fields = [
        'id', 'file_hash', 'file_size', 'file_type', 'created_at', 
        'updated_at', 'thumbnail_preview', 'file_info_display'
    ]
    
    fieldsets = (
        ('File Information', {
            'fields': ('case', 'category', 'filename', 'original_filename')
        }),
        ('File Details', {
            'fields': ('file_type_display', 'file_size_display', 'file_hash')
        }),
        ('Storage', {
            'fields': ('file_path', 'storage_backend', 'is_active')
        }),
        ('Metadata', {
            'fields': ('uploaded_by', 'description', 'tags'),
            'classes': ('collapse',)
        }),
        ('Preview', {
            'fields': ('thumbnail_preview', 'file_info_display'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def file_type_display(self, obj):
        """Display file type with icon"""
        icons = {
            'image': '🖼️',
            'pdf': '📄',
            'stl': '🏗️',
            'zip': '📦',
            'doc': '📝',
            'video': '🎥',
            'audio': '🎵'
        }
        icon = icons.get(obj.file_type, '📁')
        return f'{icon} {obj.file_type.upper()}'
    file_type_display.short_description = 'Type'
    
    def file_size_display(self, obj):
        """Display file size in human readable format"""
        if obj.file_size < 1024:
            return f'{obj.file_size} B'
        elif obj.file_size < 1024 * 1024:
            return f'{obj.file_size / 1024:.1f} KB'
        elif obj.file_size < 1024 * 1024 * 1024:
            return f'{obj.file_size / (1024 * 1024):.1f} MB'
        else:
            return f'{obj.file_size / (1024 * 1024 * 1024):.1f} GB'
    file_size_display.short_description = 'Size'
    
    def thumbnail_preview(self, obj):
        """Show thumbnail preview for images"""
        if obj.file_type == 'image' and obj.file_path:
            # This would need proper URL construction in real implementation
            return mark_safe(f'<img src="/media/{obj.file_path}" style="max-width: 100px; max-height: 100px;" />')
        elif obj.file_type == 'pdf':
            return '📄 PDF Document'
        elif obj.file_type == 'stl':
            return '🏗️ 3D Model'
        else:
            return f'{obj.file_type.upper()} File'
    thumbnail_preview.short_description = 'Preview'
    
    def file_info_display(self, obj):
        """Display detailed file information"""
        info = [
            f'Original: {obj.original_filename}',
            f'Hash: {obj.file_hash[:16]}...' if obj.file_hash else 'No hash',
            f'Backend: {obj.storage_backend}',
        ]
        return mark_safe('<br>'.join(info))
    file_info_display.short_description = 'File Info'
    
    actions = ['activate_files', 'deactivate_files', 'regenerate_thumbnails']
    
    def activate_files(self, request, queryset):
        """Activate selected files"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} files activated.')
    activate_files.short_description = 'Activate selected files'
    
    def deactivate_files(self, request, queryset):
        """Deactivate selected files"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} files deactivated.')
    deactivate_files.short_description = 'Deactivate selected files'
    
    def regenerate_thumbnails(self, request, queryset):
        """Regenerate thumbnails for image files"""
        image_files = queryset.filter(file_type='image')
        count = image_files.count()
        # This would trigger thumbnail regeneration in real implementation
        self.message_user(request, f'Thumbnail regeneration queued for {count} images.')
    regenerate_thumbnails.short_description = 'Regenerate thumbnails'


@admin.register(FileShare)
class FileShareAdmin(admin.ModelAdmin):
    """Admin interface for File Sharing"""
    list_display = [
        'file_upload', 'share_token', 'shared_with_display', 'expires_at',
        'download_count', 'is_active', 'created_at'
    ]
    list_filter = ['is_active', 'expires_at', 'created_at']
    search_fields = ['share_token', 'shared_with_email', 'file_upload__filename']
    readonly_fields = ['share_token', 'download_count', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Share Information', {
            'fields': ('file_upload', 'share_token', 'shared_by')
        }),
        ('Access Control', {
            'fields': ('shared_with_email', 'expires_at', 'is_active')
        }),
        ('Usage Statistics', {
            'fields': ('download_count', 'last_accessed')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def shared_with_display(self, obj):
        """Display who the file is shared with"""
        if obj.shared_with_email:
            return obj.shared_with_email
        return 'Public link'
    shared_with_display.short_description = 'Shared With'
    
    actions = ['revoke_shares', 'extend_expiry']
    
    def revoke_shares(self, request, queryset):
        """Revoke selected file shares"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} file shares revoked.')
    revoke_shares.short_description = 'Revoke selected shares'
    
    def extend_expiry(self, request, queryset):
        """Extend expiry by 7 days"""
        from datetime import timedelta
        from django.utils import timezone
        
        for share in queryset:
            if share.expires_at:
                share.expires_at = share.expires_at + timedelta(days=7)
                share.save()
        
        self.message_user(request, f'Extended expiry for {queryset.count()} shares by 7 days.')
    extend_expiry.short_description = 'Extend expiry by 7 days'