{"ast": null, "code": "var _jsxFileName = \"C:\\\\GPT4_PROJECTS\\\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\\\dentflow\\\\frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * DentFlow Frontend - Working Application\n * Complete integration with routing and authentication\n */\n\nimport React from 'react';\nimport { Box, Typography, Container, Paper, Button, Alert } from '@mui/material';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\n\n// CORRECTED IMPORTS\nimport { AuthProvider, useAuth } from './context'; // Uses the index.ts barrel file in /context\nimport Login from './components/Login';\nimport Dashboard from './components/Dashboard';\nimport Layout from './components/Layout';\nimport ProtectedRoute from './components/ProtectedRoute';\n\n// Import feature components - Ensure this file exists: src/features/cases/CasesList.tsx\nimport CasesList from './features/cases/CasesList';\n\n// Create React Query client\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      staleTime: 5 * 60 * 1000,\n      refetchOnWindowFocus: false\n    }\n  }\n});\n\n// Create theme\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#1565c0'\n    },\n    secondary: {\n      main: '#00acc1',\n      light: '#4dd0e1',\n      dark: '#00838f'\n    },\n    success: {\n      main: '#2e7d32',\n      light: '#4caf50',\n      dark: '#1b5e20'\n    },\n    warning: {\n      main: '#ff9800',\n      light: '#ffb74d',\n      dark: '#f57c00'\n    },\n    error: {\n      main: '#d32f2f',\n      light: '#ef5350',\n      dark: '#c62828'\n    },\n    background: {\n      default: '#fafafa',\n      paper: '#ffffff'\n    }\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    button: {\n      textTransform: 'none',\n      fontWeight: 600\n    }\n  },\n  shape: {\n    borderRadius: 8\n  }\n});\n\n// Overview Page Component\nfunction OverviewPage({\n  onLaunch\n}) {\n  // ... (JSX for OverviewPage remains the same)\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      bgcolor: 'background.default'\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      sx: {\n        py: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 3,\n        sx: {\n          p: 4,\n          textAlign: 'center',\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h2\",\n          gutterBottom: true,\n          color: \"primary\",\n          children: \"\\uD83E\\uDDB7 DentFlow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Complete Dental Laboratory Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"success\",\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\uD83C\\uDF89 CONGRATULATIONS!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this), \" All components successfully integrated!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          textAlign: 'center',\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"\\uD83D\\uDE80 Ready to Launch!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2,\n            justifyContent: 'center',\n            flexWrap: 'wrap',\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            size: \"large\",\n            onClick: onLaunch,\n            children: \"\\uD83D\\uDD10 Launch Full Application\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n}\n\n// Main Application Component with Routing\n_c = OverviewPage;\nfunction MainApplication() {\n  _s();\n  const {\n    isAuthenticated,\n    isLoading\n  } = useAuth();\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        minHeight: '100vh',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/login\",\n      element: isAuthenticated ? /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/dashboard\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 36\n      }, this) : /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 75\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/dashboard\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        children: /*#__PURE__*/_jsxDEV(Layout, {\n          children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/cases\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        children: /*#__PURE__*/_jsxDEV(Layout, {\n          children: /*#__PURE__*/_jsxDEV(CasesList, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(Navigate, {\n        to: isAuthenticated ? \"/dashboard\" : \"/login\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 18\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n}\n_s(MainApplication, \"yb/FJYAIXt7wZoU4a4YvGQ4Nlsc=\", false, function () {\n  return [useAuth];\n});\n_c2 = MainApplication;\nfunction App() {\n  _s2();\n  const [showOverview, setShowOverview] = React.useState(true);\n  const handleLaunch = () => {\n    setShowOverview(false);\n  };\n  return /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n    client: queryClient,\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), showOverview ? /*#__PURE__*/_jsxDEV(OverviewPage, {\n        onLaunch: handleLaunch\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n          children: /*#__PURE__*/_jsxDEV(MainApplication, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ReactQueryDevtools, {\n        initialIsOpen: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n}\n_s2(App, \"ZdWA9GkBCuwRUDyTZPbeHgYzsbI=\");\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"OverviewPage\");\n$RefreshReg$(_c2, \"MainApplication\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Container", "Paper", "<PERSON><PERSON>", "<PERSON><PERSON>", "ThemeProvider", "createTheme", "CssBaseline", "QueryClient", "QueryClientProvider", "ReactQueryDevtools", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "<PERSON><PERSON>", "Dashboard", "Layout", "ProtectedRoute", "CasesList", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "staleTime", "refetchOnWindowFocus", "theme", "palette", "primary", "main", "light", "dark", "secondary", "success", "warning", "error", "background", "default", "paper", "typography", "fontFamily", "button", "textTransform", "fontWeight", "shape", "borderRadius", "OverviewPage", "onLaunch", "sx", "minHeight", "bgcolor", "children", "max<PERSON><PERSON><PERSON>", "py", "elevation", "p", "textAlign", "mb", "variant", "gutterBottom", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "mt", "display", "gap", "justifyContent", "flexWrap", "size", "onClick", "_c", "MainApplication", "_s", "isAuthenticated", "isLoading", "alignItems", "path", "element", "to", "replace", "_c2", "App", "_s2", "showOverview", "setShowOverview", "useState", "handleLaunch", "client", "initialIsOpen", "_c3", "$RefreshReg$"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/App.tsx"], "sourcesContent": ["/**\n * DentFlow Frontend - Working Application\n * Complete integration with routing and authentication\n */\n\nimport React from 'react';\nimport { Box, Typography, Container, Paper, Button, Grid, Card, CardContent, Alert } from '@mui/material';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\n\n// CORRECTED IMPORTS\nimport { AuthProvider, useAuth } from './context'; // Uses the index.ts barrel file in /context\nimport Login from './components/Login';\nimport Dashboard from './components/Dashboard';\nimport Layout from './components/Layout';\nimport ProtectedRoute from './components/ProtectedRoute';\n\n// Import feature components - Ensure this file exists: src/features/cases/CasesList.tsx\nimport CasesList from './features/cases/CasesList';\n\n// Create React Query client\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      staleTime: 5 * 60 * 1000,\n      refetchOnWindowFocus: false,\n    },\n  },\n});\n\n// Create theme\nconst theme = createTheme({\n  palette: {\n    primary: { main: '#1976d2', light: '#42a5f5', dark: '#1565c0' },\n    secondary: { main: '#00acc1', light: '#4dd0e1', dark: '#00838f' },\n    success: { main: '#2e7d32', light: '#4caf50', dark: '#1b5e20' },\n    warning: { main: '#ff9800', light: '#ffb74d', dark: '#f57c00' },\n    error: { main: '#d32f2f', light: '#ef5350', dark: '#c62828' },\n    background: { default: '#fafafa', paper: '#ffffff' },\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    button: { textTransform: 'none', fontWeight: 600 },\n  },\n  shape: { borderRadius: 8 },\n});\n\n// Overview Page Component\nfunction OverviewPage({ onLaunch }: { onLaunch: () => void }) {\n  // ... (JSX for OverviewPage remains the same)\n  return (\n    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        \n        {/* Success Header */}\n        <Paper elevation={3} sx={{ p: 4, textAlign: 'center', mb: 4 }}>\n          <Typography variant=\"h2\" gutterBottom color=\"primary\">\n            🦷 DentFlow\n          </Typography>\n          <Typography variant=\"h5\" gutterBottom>\n            Complete Dental Laboratory Management System\n          </Typography>\n          <Alert severity=\"success\" sx={{ mt: 2 }}>\n            <strong>🎉 CONGRATULATIONS!</strong> All components successfully integrated!\n          </Alert>\n        </Paper>\n\n        {/* Action Buttons */}\n        <Paper sx={{ p: 3, textAlign: 'center', mt: 4 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            🚀 Ready to Launch!\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap', mt: 2 }}>\n            <Button \n              variant=\"contained\" \n              size=\"large\"\n              onClick={onLaunch}\n            >\n              🔐 Launch Full Application\n            </Button>\n          </Box>\n        </Paper>\n      </Container>\n    </Box>\n  );\n}\n\n// Main Application Component with Routing\nfunction MainApplication() {\n  const { isAuthenticated, isLoading } = useAuth();\n\n  if (isLoading) {\n    return (\n      <Box sx={{ \n        minHeight: '100vh', \n        display: 'flex', \n        alignItems: 'center', \n        justifyContent: 'center' \n      }}>\n        <Typography variant=\"h6\">Loading...</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Routes>\n      <Route \n        path=\"/login\" \n        element={isAuthenticated ? <Navigate to=\"/dashboard\" replace /> : <Login />} \n      />\n      <Route \n        path=\"/dashboard\" \n        element={\n          <ProtectedRoute>\n            <Layout>\n              <Dashboard />\n            </Layout>\n          </ProtectedRoute>\n        } \n      />\n      <Route \n        path=\"/cases\" \n        element={\n          <ProtectedRoute>\n            <Layout>\n              <CasesList />\n            </Layout>\n          </ProtectedRoute>\n        } \n      />\n      {/* ... other routes */}\n      <Route \n        path=\"/\" \n        element={<Navigate to={isAuthenticated ? \"/dashboard\" : \"/login\"} replace />} \n      />\n    </Routes>\n  );\n}\n\nfunction App() {\n  const [showOverview, setShowOverview] = React.useState(true);\n\n  const handleLaunch = () => {\n    setShowOverview(false);\n  };\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        {showOverview ? (\n          <OverviewPage onLaunch={handleLaunch} />\n        ) : (\n          <Router>\n            <AuthProvider>\n              <MainApplication />\n            </AuthProvider>\n          </Router>\n        )}\n        <ReactQueryDevtools initialIsOpen={false} />\n      </ThemeProvider>\n    </QueryClientProvider>\n  );\n}\n\nexport default App;"], "mappings": ";;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAA2BC,KAAK,QAAQ,eAAe;AACzG,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,uBAAuB;AACxE,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;;AAEnF;AACA,SAASC,YAAY,EAAEC,OAAO,QAAQ,WAAW,CAAC,CAAC;AACnD,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,cAAc,MAAM,6BAA6B;;AAExD;AACA,OAAOC,SAAS,MAAM,4BAA4B;;AAElD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIjB,WAAW,CAAC;EAClCkB,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;MACxBC,oBAAoB,EAAE;IACxB;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,KAAK,GAAGxB,WAAW,CAAC;EACxByB,OAAO,EAAE;IACPC,OAAO,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAU,CAAC;IAC/DC,SAAS,EAAE;MAAEH,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAU,CAAC;IACjEE,OAAO,EAAE;MAAEJ,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAU,CAAC;IAC/DG,OAAO,EAAE;MAAEL,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAU,CAAC;IAC/DI,KAAK,EAAE;MAAEN,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAU,CAAC;IAC7DK,UAAU,EAAE;MAAEC,OAAO,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAU;EACrD,CAAC;EACDC,UAAU,EAAE;IACVC,UAAU,EAAE,qDAAqD;IACjEC,MAAM,EAAE;MAAEC,aAAa,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAI;EACnD,CAAC;EACDC,KAAK,EAAE;IAAEC,YAAY,EAAE;EAAE;AAC3B,CAAC,CAAC;;AAEF;AACA,SAASC,YAAYA,CAAC;EAAEC;AAAmC,CAAC,EAAE;EAC5D;EACA,oBACE3B,OAAA,CAACzB,GAAG;IAACqD,EAAE,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAqB,CAAE;IAAAC,QAAA,eAC7D/B,OAAA,CAACvB,SAAS;MAACuD,QAAQ,EAAC,IAAI;MAACJ,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBAGrC/B,OAAA,CAACtB,KAAK;QAACwD,SAAS,EAAE,CAAE;QAACN,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEC,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,gBAC5D/B,OAAA,CAACxB,UAAU;UAAC8D,OAAO,EAAC,IAAI;UAACC,YAAY;UAACC,KAAK,EAAC,SAAS;UAAAT,QAAA,EAAC;QAEtD;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5C,OAAA,CAACxB,UAAU;UAAC8D,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAR,QAAA,EAAC;QAEtC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5C,OAAA,CAACpB,KAAK;UAACiE,QAAQ,EAAC,SAAS;UAACjB,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,gBACtC/B,OAAA;YAAA+B,QAAA,EAAQ;UAAmB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,4CACtC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGR5C,OAAA,CAACtB,KAAK;QAACkD,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEC,SAAS,EAAE,QAAQ;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAf,QAAA,gBAC9C/B,OAAA,CAACxB,UAAU;UAAC8D,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAR,QAAA,EAAC;QAEtC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5C,OAAA,CAACzB,GAAG;UAACqD,EAAE,EAAE;YAAEmB,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,CAAC;YAAEC,cAAc,EAAE,QAAQ;YAAEC,QAAQ,EAAE,MAAM;YAAEJ,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,eACtF/B,OAAA,CAACrB,MAAM;YACL2D,OAAO,EAAC,WAAW;YACnBa,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEzB,QAAS;YAAAI,QAAA,EACnB;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV;;AAEA;AAAAS,EAAA,GAvCS3B,YAAY;AAwCrB,SAAS4B,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAM;IAAEC,eAAe;IAAEC;EAAU,CAAC,GAAGhE,OAAO,CAAC,CAAC;EAEhD,IAAIgE,SAAS,EAAE;IACb,oBACEzD,OAAA,CAACzB,GAAG;MAACqD,EAAE,EAAE;QACPC,SAAS,EAAE,OAAO;QAClBkB,OAAO,EAAE,MAAM;QACfW,UAAU,EAAE,QAAQ;QACpBT,cAAc,EAAE;MAClB,CAAE;MAAAlB,QAAA,eACA/B,OAAA,CAACxB,UAAU;QAAC8D,OAAO,EAAC,IAAI;QAAAP,QAAA,EAAC;MAAU;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEV;EAEA,oBACE5C,OAAA,CAACX,MAAM;IAAA0C,QAAA,gBACL/B,OAAA,CAACV,KAAK;MACJqE,IAAI,EAAC,QAAQ;MACbC,OAAO,EAAEJ,eAAe,gBAAGxD,OAAA,CAACT,QAAQ;QAACsE,EAAE,EAAC,YAAY;QAACC,OAAO;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACN,KAAK;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CAAC,eACF5C,OAAA,CAACV,KAAK;MACJqE,IAAI,EAAC,YAAY;MACjBC,OAAO,eACL5D,OAAA,CAACH,cAAc;QAAAkC,QAAA,eACb/B,OAAA,CAACJ,MAAM;UAAAmC,QAAA,eACL/B,OAAA,CAACL,SAAS;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACF5C,OAAA,CAACV,KAAK;MACJqE,IAAI,EAAC,QAAQ;MACbC,OAAO,eACL5D,OAAA,CAACH,cAAc;QAAAkC,QAAA,eACb/B,OAAA,CAACJ,MAAM;UAAAmC,QAAA,eACL/B,OAAA,CAACF,SAAS;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEF5C,OAAA,CAACV,KAAK;MACJqE,IAAI,EAAC,GAAG;MACRC,OAAO,eAAE5D,OAAA,CAACT,QAAQ;QAACsE,EAAE,EAAEL,eAAe,GAAG,YAAY,GAAG,QAAS;QAACM,OAAO;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9E,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb;AAACW,EAAA,CAjDQD,eAAe;EAAA,QACiB7D,OAAO;AAAA;AAAAsE,GAAA,GADvCT,eAAe;AAmDxB,SAASU,GAAGA,CAAA,EAAG;EAAAC,GAAA;EACb,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7F,KAAK,CAAC8F,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBF,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,oBACEnE,OAAA,CAACf,mBAAmB;IAACqF,MAAM,EAAErE,WAAY;IAAA8B,QAAA,eACvC/B,OAAA,CAACnB,aAAa;MAACyB,KAAK,EAAEA,KAAM;MAAAyB,QAAA,gBAC1B/B,OAAA,CAACjB,WAAW;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACdsB,YAAY,gBACXlE,OAAA,CAAC0B,YAAY;QAACC,QAAQ,EAAE0C;MAAa;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAExC5C,OAAA,CAACZ,MAAM;QAAA2C,QAAA,eACL/B,OAAA,CAACR,YAAY;UAAAuC,QAAA,eACX/B,OAAA,CAACsD,eAAe;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACT,eACD5C,OAAA,CAACd,kBAAkB;QAACqF,aAAa,EAAE;MAAM;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAE1B;AAACqB,GAAA,CAxBQD,GAAG;AAAAQ,GAAA,GAAHR,GAAG;AA0BZ,eAAeA,GAAG;AAAC,IAAAX,EAAA,EAAAU,GAAA,EAAAS,GAAA;AAAAC,YAAA,CAAApB,EAAA;AAAAoB,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}