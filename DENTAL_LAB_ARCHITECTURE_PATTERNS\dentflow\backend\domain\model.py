"""
DentFlow Domain Model - Case Aggregate
The Case is the main aggregate root that encapsulates business rules
"""

from dataclasses import dataclass, field
from typing import List, Optional
from datetime import datetime, timedelta
from enum import Enum
import uuid

from . import events


class CaseStatus(Enum):
    RECEIVED = "received"
    DESIGN = "design"
    MILLING = "milling"
    SINTERING = "sintering"
    QC = "qc"
    SHIPPED = "shipped"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"


class Priority(Enum):
    LOW = "low"
    NORMAL = "normal"
    URGENT = "urgent"
    STAT = "stat"


@dataclass(frozen=True)
class CaseId:
    value: str

    @classmethod
    def generate(cls, lab_prefix: str = "LAB") -> 'CaseId':
        timestamp = datetime.now().strftime("%Y-%m-%d")
        unique_id = str(uuid.uuid4())[:8]
        return cls(f"{lab_prefix}-{timestamp}-{unique_id}")


@dataclass(frozen=True)
class ToothNumber:
    value: str
    
    def __post_init__(self):
        if not self._is_valid_tooth_number(self.value):
            raise ValueError(f"Invalid tooth number: {self.value}")
    
    def _is_valid_tooth_number(self, value: str) -> bool:
        # ISO 3950 dental notation validation
        if not value.isdigit():
            return False
        tooth_num = int(value)
        return 11 <= tooth_num <= 48


@dataclass
class WorkflowStage:
    name: str
    department: str
    estimated_duration_minutes: int
    auto_assign: bool = False
    requires_quality_check: bool = False
    machine_required: Optional[str] = None

class Case:
    """
    Case Aggregate Root
    Encapsulates all business rules around dental cases
    """
    
    def __init__(self,
                 case_id: CaseId,
                 clinic_id: str,
                 patient_name: str,
                 tooth_number: ToothNumber,
                 service_type: str,
                 tenant_id: str,
                 priority: Priority = Priority.NORMAL):
        
        self.case_id = case_id
        self.clinic_id = clinic_id
        self.patient_name = patient_name
        self.tooth_number = tooth_number
        self.service_type = service_type
        self.tenant_id = tenant_id
        self.priority = priority
        
        self.status = CaseStatus.RECEIVED
        self.current_stage_index = 0
        self.workflow_stages: List[WorkflowStage] = []
        self.assigned_technician_id: Optional[str] = None
        self.due_date: Optional[datetime] = None
        self.notes: List[str] = []
        self.files: List[str] = []
        
        self.created_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        # Domain events
        self.events: List[events.Event] = []
        
        # Record creation event
        self._add_event(events.CaseCreated(
            case_id=self.case_id.value,
            clinic_id=self.clinic_id,
            patient_name=self.patient_name,
            tooth_number=self.tooth_number.value,
            service_type=self.service_type,
            priority=self.priority.value,
            tenant_id=self.tenant_id,
            created_at=self.created_at
        ))    
    def assign_workflow(self, stages: List[WorkflowStage]):
        """Assign workflow stages and calculate due date"""
        self.workflow_stages = stages
        self._calculate_due_date()
    
    def advance_to_next_stage(self, technician_id: str, notes: str = "") -> None:
        """Advance case to the next workflow stage"""
        if not self.can_advance():
            raise InvalidStageTransition(
                f"Cannot advance case {self.case_id.value} from {self.status.value}"
            )
        
        previous_status = self.status
        self.current_stage_index += 1
        
        if self.current_stage_index >= len(self.workflow_stages):
            self.status = CaseStatus.QC
        else:
            next_stage = self.workflow_stages[self.current_stage_index]
            self.status = CaseStatus(next_stage.name.lower())
        
        self.assigned_technician_id = None  # Clear assignment for next stage
        self.updated_at = datetime.utcnow()
        
        if notes:
            self.notes.append(f"{datetime.utcnow()}: {notes}")
        
        self._add_event(events.CaseStatusChanged(
            case_id=self.case_id.value,
            from_status=previous_status.value,
            to_status=self.status.value,
            technician_id=technician_id,
            note=notes,
            tenant_id=self.tenant_id,
            changed_at=self.updated_at
        ))    
    def assign_technician(self, technician_id: str, assigned_by: str) -> None:
        """Assign technician to current stage"""
        if self.status in [CaseStatus.DELIVERED, CaseStatus.CANCELLED]:
            raise InvalidOperation("Cannot assign technician to completed case")
        
        self.assigned_technician_id = technician_id
        self.updated_at = datetime.utcnow()
        
        current_stage = self._get_current_stage_name()
        
        self._add_event(events.TechnicianAssigned(
            case_id=self.case_id.value,
            technician_id=technician_id,
            stage=current_stage,
            assigned_by=assigned_by,
            tenant_id=self.tenant_id,
            assigned_at=self.updated_at
        ))
    
    def upload_file(self, file_id: str, filename: str, file_type: str, 
                   size_bytes: int, uploaded_by: str) -> None:
        """Add file to case"""
        self.files.append(file_id)
        self.updated_at = datetime.utcnow()
        
        self._add_event(events.FileUploaded(
            case_id=self.case_id.value,
            file_id=file_id,
            filename=filename,
            file_type=file_type,
            size_bytes=size_bytes,
            uploaded_by=uploaded_by,
            tenant_id=self.tenant_id,
            uploaded_at=self.updated_at
        ))    
    def can_advance(self) -> bool:
        """Check if case can advance to next stage"""
        if self.status in [CaseStatus.DELIVERED, CaseStatus.CANCELLED]:
            return False
        
        if self.status == CaseStatus.QC:
            return False  # QC is handled separately
        
        return True
    
    def is_overdue(self) -> bool:
        """Check if case is overdue"""
        if not self.due_date:
            return False
        return datetime.utcnow() > self.due_date
    
    def days_until_due(self) -> Optional[int]:
        """Calculate days until due date"""
        if not self.due_date:
            return None
        delta = self.due_date - datetime.utcnow()
        return delta.days
    
    def _calculate_due_date(self):
        """Calculate due date based on workflow stages and priority"""
        if not self.workflow_stages:
            return
        
        total_minutes = sum(stage.estimated_duration_minutes for stage in self.workflow_stages)
        
        # Priority adjustments
        priority_multipliers = {
            Priority.STAT: 0.5,
            Priority.URGENT: 0.7,
            Priority.NORMAL: 1.0,
            Priority.LOW: 1.3
        }        
        adjusted_minutes = total_minutes * priority_multipliers[self.priority]
        
        # Add buffer time and convert to business days
        buffer_hours = 4  # 4 hour buffer
        total_hours = (adjusted_minutes / 60) + buffer_hours
        
        # Assuming 8-hour work days
        business_days = int(total_hours / 8) + 1
        
        self.due_date = self._add_business_days(self.created_at, business_days)
    
    def _add_business_days(self, start_date: datetime, days: int) -> datetime:
        """Add business days to a date (skip weekends)"""
        current = start_date
        days_added = 0
        
        while days_added < days:
            current += timedelta(days=1)
            if current.weekday() < 5:  # Monday=0, Sunday=6
                days_added += 1
        
        return current
    
    def _get_current_stage_name(self) -> str:
        """Get name of current workflow stage"""
        if self.current_stage_index < len(self.workflow_stages):
            return self.workflow_stages[self.current_stage_index].name
        return self.status.value
    
    def _add_event(self, event: events.Event):
        """Add domain event to be published"""
        self.events.append(event)


# Domain Exceptions
class DomainException(Exception):
    """Base exception for domain errors"""
    pass


class InvalidStageTransition(DomainException):
    """Raised when attempting invalid stage transition"""
    pass


class InvalidOperation(DomainException):
    """Raised when attempting invalid operation"""
    pass