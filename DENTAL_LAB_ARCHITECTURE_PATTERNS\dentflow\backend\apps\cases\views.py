"""
DentFlow Case API Views
Clean API layer that delegates to service layer via message bus
Following the pattern: HTTP -> Command -> Domain -> Events
"""

from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
import logging

from domain import commands, messagebus
from domain.model import DomainException
from services.unit_of_work import DjangoUnitOfWork
from services import handlers
from apps.cases.models import Case as DjangoCase
from apps.cases.serializers import CaseSerializer, CreateCaseSerializer


logger = logging.getLogger(__name__)


class CaseViewSet(viewsets.ModelViewSet):
    """
    Cases API ViewSet
    
    Provides CRUD operations for dental cases with proper
    separation between HTTP concerns and business logic.
    """
    
    permission_classes = [IsAuthenticated]
    serializer_class = CaseSerializer
    
    def get_queryset(self):
        """Filter cases by tenant"""
        if not hasattr(self.request.user, 'tenant_id'):
            return DjangoCase.objects.none()
        
        return DjangoCase.objects.filter(
            tenant_id=self.request.user.tenant_id,
            deleted_at__isnull=True
        ).order_by('-created_at')
    
    def get_serializer_class(self):
        """Use different serializers for different actions"""
        if self.action == 'create':
            return CreateCaseSerializer
        return CaseSerializer
    
    def create(self, request):
        """
        Create a new case
        POST /api/v1/cases/
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # Create command from validated data
        command = commands.CreateCase(
            clinic_id=serializer.validated_data['clinic_id'],
            patient_name=serializer.validated_data['patient_name'],
            tooth_number=serializer.validated_data['tooth_number'],
            service_type=serializer.validated_data['service_type'],
            priority=serializer.validated_data.get('priority', 'normal'),
            notes=serializer.validated_data.get('notes', ''),
            files=serializer.validated_data.get('files', []),
            tenant_id=str(request.user.tenant_id)
        )
        
        try:
            # Execute command via message bus
            message_bus = self._get_message_bus(request.user.tenant_id)
            case_id = message_bus.handle(command)
            
            # Return the created case
            case = get_object_or_404(DjangoCase, id=case_id)
            response_serializer = CaseSerializer(case)
            
            logger.info(f"Created case {case_id} for user {request.user.id}")
            
            return Response(
                response_serializer.data,
                status=status.HTTP_201_CREATED
            )
            
        except DomainException as e:
            logger.warning(f"Domain error creating case: {e}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error creating case: {e}")
            return Response(
                {'error': 'Internal server error'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def advance(self, request, pk=None):
        """
        Advance case to next stage
        POST /api/v1/cases/{id}/advance/
        """
        case = self.get_object()
        
        command = commands.AdvanceCase(
            case_id=case.id,
            notes=request.data.get('notes', ''),
            quality_check_passed=request.data.get('quality_check_passed', True),
            tenant_id=str(request.user.tenant_id)
        )
        
        try:
            message_bus = self._get_message_bus(request.user.tenant_id)
            message_bus.handle(command)
            
            # Refresh case from database
            case.refresh_from_db()
            serializer = CaseSerializer(case)
            
            logger.info(f"Advanced case {case.id} by user {request.user.id}")
            
            return Response(serializer.data)
            
        except DomainException as e:
            logger.warning(f"Domain error advancing case {case.id}: {e}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def assign_technician(self, request, pk=None):
        """
        Assign technician to case
        POST /api/v1/cases/{id}/assign_technician/
        """
        case = self.get_object()
        
        command = commands.AssignTechnician(
            case_id=case.id,
            technician_id=request.data.get('technician_id'),
            stage=request.data.get('stage', ''),
            tenant_id=str(request.user.tenant_id)
        )
        
        try:
            message_bus = self._get_message_bus(request.user.tenant_id)
            message_bus.handle(command)
            
            case.refresh_from_db()
            serializer = CaseSerializer(case)
            
            logger.info(f"Assigned technician to case {case.id}")
            
            return Response(serializer.data)
            
        except DomainException as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'])
    def overdue(self, request):
        """
        Get overdue cases for tenant
        GET /api/v1/cases/overdue/
        """
        uow = DjangoUnitOfWork(tenant_id=str(request.user.tenant_id))
        overdue_cases = uow.cases.list_overdue(str(request.user.tenant_id))
        
        # Convert domain cases to Django models for serialization
        django_cases = []
        for domain_case in overdue_cases:
            try:
                django_case = DjangoCase.objects.get(id=domain_case.case_id.value)
                django_cases.append(django_case)
            except DjangoCase.DoesNotExist:
                continue
        
        serializer = CaseSerializer(django_cases, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_status(self, request):
        """
        Get cases by status
        GET /api/v1/cases/by_status/?status=design
        """
        status_filter = request.query_params.get('status')
        if not status_filter:
            return Response(
                {'error': 'status parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        cases = self.get_queryset().filter(status=status_filter)
        serializer = CaseSerializer(cases, many=True)
        return Response(serializer.data)
    
    def _get_message_bus(self, tenant_id: str):
        """Get configured message bus for tenant"""
        # This would normally come from a bootstrap/dependency injection container
        uow = DjangoUnitOfWork(tenant_id=str(tenant_id))
        
        # Event handlers with dependency injection
        event_handlers = {
            # handlers.CaseCreated: [
            #     lambda e: handlers.send_case_confirmation_email(e, email_service),
            #     lambda e: handlers.auto_assign_technician(e, uow),
            # ],
            # handlers.CaseStatusChanged: [
            #     lambda e: handlers.notify_status_change(e, email_service),
            #     lambda e: handlers.update_sla_tracking(e, uow),
            # ],
        }
        
        # Command handlers
        command_handlers = {
            commands.CreateCase: lambda c: handlers.create_case(c, uow),
            commands.AdvanceCase: lambda c: handlers.advance_case(c, uow),
            commands.AssignTechnician: lambda c: handlers.assign_technician(c, uow),
        }
        
        return messagebus.MessageBus(
            event_handlers=event_handlers,
            command_handlers=command_handlers,
            uow=uow
        )