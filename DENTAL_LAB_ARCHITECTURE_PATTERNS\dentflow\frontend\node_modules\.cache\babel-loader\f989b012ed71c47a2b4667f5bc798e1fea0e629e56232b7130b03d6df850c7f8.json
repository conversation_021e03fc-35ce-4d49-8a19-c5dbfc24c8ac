{"ast": null, "code": "/**\n * Enhanced Mock Data for DentFlow API Services\n * Comprehensive test data for all modules\n */\n\n// Mock Clinics\nexport const mockClinics = [{\n  id: 'CLI-001',\n  name: 'Smile Dental Clinic',\n  email: '<EMAIL>',\n  phone: '******-0123',\n  address: '123 Main St, Downtown, NY 10001',\n  contact_person: 'Dr<PERSON> <PERSON>',\n  is_active: true,\n  created_at: '2023-01-15T10:00:00Z'\n}, {\n  id: 'CLI-002',\n  name: 'City Dental Care',\n  email: '<EMAIL>',\n  phone: '******-0456',\n  address: '456 Oak Ave, Midtown, NY 10002',\n  contact_person: 'Dr<PERSON> <PERSON>',\n  is_active: true,\n  created_at: '2023-02-20T14:30:00Z'\n}, {\n  id: 'CLI-003',\n  name: 'Family Dentistry Plus',\n  email: '<EMAIL>',\n  phone: '******-0789',\n  address: '789 Pine St, Uptown, NY 10003',\n  contact_person: 'Dr<PERSON> <PERSON>',\n  is_active: true,\n  created_at: '2023-03-10T09:15:00Z'\n}];\n\n// Mock Technicians\nexport const mockTechnicians = [{\n  id: 'TECH-001',\n  first_name: '<PERSON>',\n  last_name: '<PERSON>',\n  email: '<EMAIL>',\n  specialties: ['crown', 'bridge', 'veneer'],\n  is_active: true,\n  current_workload: 6,\n  max_capacity: 8\n}, {\n  id: 'TECH-002',\n  first_name: 'David',\n  last_name: 'Chen',\n  email: '<EMAIL>',\n  specialties: ['implant', 'denture'],\n  is_active: true,\n  current_workload: 4,\n  max_capacity: 6\n}, {\n  id: 'TECH-003',\n  first_name: 'Sarah',\n  last_name: 'Thompson',\n  email: '<EMAIL>',\n  specialties: ['crown', 'veneer', 'bridge'],\n  is_active: true,\n  current_workload: 7,\n  max_capacity: 8\n}, {\n  id: 'TECH-004',\n  first_name: 'Michael',\n  last_name: 'Johnson',\n  email: '<EMAIL>',\n  specialties: ['denture', 'implant'],\n  is_active: true,\n  current_workload: 3,\n  max_capacity: 5\n}];\n\n// Mock Cases with enhanced data\nexport const mockCases = [{\n  id: 'LAB-2024-001',\n  clinic: mockClinics[0],\n  patient_name: 'John Doe',\n  tooth_number: '#15',\n  service_type: 'crown',\n  priority: 'normal',\n  status: 'milling',\n  current_stage_index: 2,\n  workflow_stages: [{\n    name: 'received',\n    department: 'intake',\n    estimated_duration_minutes: 30,\n    auto_assign: true,\n    requires_quality_check: false\n  }, {\n    name: 'design',\n    department: 'design',\n    estimated_duration_minutes: 120,\n    auto_assign: false,\n    requires_quality_check: true\n  }, {\n    name: 'milling',\n    department: 'production',\n    estimated_duration_minutes: 180,\n    auto_assign: true,\n    requires_quality_check: false\n  }, {\n    name: 'sintering',\n    department: 'production',\n    estimated_duration_minutes: 240,\n    auto_assign: true,\n    requires_quality_check: false\n  }, {\n    name: 'qc',\n    department: 'quality',\n    estimated_duration_minutes: 45,\n    auto_assign: false,\n    requires_quality_check: true\n  }],\n  assigned_technician_id: 'TECH-001',\n  due_date: '2024-02-15T17:00:00Z',\n  created_at: '2024-01-15T10:00:00Z',\n  updated_at: '2024-01-16T14:30:00Z',\n  notes: ['2024-01-15T10:00:00Z: Case received - Standard crown procedure', '2024-01-15T11:30:00Z: Design phase completed - Color match A3', '2024-01-16T14:30:00Z: Milling in progress'],\n  files: [{\n    id: 'F001',\n    name: 'impression.stl',\n    url: '/files/F001',\n    size: 2048576,\n    uploaded_at: '2024-01-15T10:00:00Z',\n    uploaded_by: 'Dr. Johnson'\n  }, {\n    id: 'F002',\n    name: 'color_guide.jpg',\n    url: '/files/F002',\n    size: 1024768,\n    uploaded_at: '2024-01-15T10:05:00Z',\n    uploaded_by: 'Dr. Johnson'\n  }],\n  days_until_due: 12,\n  is_overdue: false,\n  current_stage: 'Milling'\n}, {\n  id: 'LAB-2024-002',\n  clinic: mockClinics[1],\n  patient_name: 'Jane Smith',\n  tooth_number: '#12-14',\n  service_type: 'bridge',\n  priority: 'urgent',\n  status: 'qc',\n  current_stage_index: 4,\n  workflow_stages: [{\n    name: 'received',\n    department: 'intake',\n    estimated_duration_minutes: 30,\n    auto_assign: true,\n    requires_quality_check: false\n  }, {\n    name: 'design',\n    department: 'design',\n    estimated_duration_minutes: 180,\n    auto_assign: false,\n    requires_quality_check: true\n  }, {\n    name: 'milling',\n    department: 'production',\n    estimated_duration_minutes: 240,\n    auto_assign: true,\n    requires_quality_check: false\n  }, {\n    name: 'sintering',\n    department: 'production',\n    estimated_duration_minutes: 300,\n    auto_assign: true,\n    requires_quality_check: false\n  }, {\n    name: 'qc',\n    department: 'quality',\n    estimated_duration_minutes: 60,\n    auto_assign: false,\n    requires_quality_check: true\n  }],\n  assigned_technician_id: 'TECH-003',\n  due_date: '2024-02-10T17:00:00Z',\n  created_at: '2024-01-08T09:00:00Z',\n  updated_at: '2024-01-18T16:45:00Z',\n  notes: ['2024-01-08T09:00:00Z: Urgent case received - 3-unit bridge', '2024-01-10T15:30:00Z: Design approved by dentist', '2024-01-15T11:00:00Z: Milling completed', '2024-01-17T14:20:00Z: Sintering completed', '2024-01-18T16:45:00Z: Quality control in progress'],\n  files: [{\n    id: 'F003',\n    name: 'bridge_impression.stl',\n    url: '/files/F003',\n    size: 3145728,\n    uploaded_at: '2024-01-08T09:00:00Z',\n    uploaded_by: 'Dr. Smith'\n  }, {\n    id: 'F004',\n    name: 'bite_registration.stl',\n    url: '/files/F004',\n    size: 1572864,\n    uploaded_at: '2024-01-08T09:05:00Z',\n    uploaded_by: 'Dr. Smith'\n  }],\n  days_until_due: 5,\n  is_overdue: false,\n  current_stage: 'Quality Control'\n}, {\n  id: 'LAB-2024-003',\n  clinic: mockClinics[2],\n  patient_name: 'Robert Wilson',\n  tooth_number: '#6',\n  service_type: 'implant',\n  priority: 'stat',\n  status: 'design',\n  current_stage_index: 1,\n  workflow_stages: [{\n    name: 'received',\n    department: 'intake',\n    estimated_duration_minutes: 30,\n    auto_assign: true,\n    requires_quality_check: false\n  }, {\n    name: 'design',\n    department: 'design',\n    estimated_duration_minutes: 150,\n    auto_assign: false,\n    requires_quality_check: true\n  }, {\n    name: 'milling',\n    department: 'production',\n    estimated_duration_minutes: 200,\n    auto_assign: true,\n    requires_quality_check: false\n  }, {\n    name: 'qc',\n    department: 'quality',\n    estimated_duration_minutes: 45,\n    auto_assign: false,\n    requires_quality_check: true\n  }],\n  assigned_technician_id: 'TECH-002',\n  due_date: '2024-01-22T17:00:00Z',\n  created_at: '2024-01-18T14:00:00Z',\n  updated_at: '2024-01-19T10:30:00Z',\n  notes: ['2024-01-18T14:00:00Z: STAT case received - Implant crown', '2024-01-19T10:30:00Z: Design phase started - Priority processing'],\n  files: [{\n    id: 'F005',\n    name: 'implant_scan.stl',\n    url: '/files/F005',\n    size: 2621440,\n    uploaded_at: '2024-01-18T14:00:00Z',\n    uploaded_by: 'Dr. Wilson'\n  }],\n  days_until_due: 2,\n  is_overdue: false,\n  current_stage: 'Design'\n}, {\n  id: 'LAB-2024-004',\n  clinic: mockClinics[0],\n  patient_name: 'Emily Davis',\n  tooth_number: '#7-10',\n  service_type: 'veneer',\n  priority: 'normal',\n  status: 'received',\n  current_stage_index: 0,\n  workflow_stages: [{\n    name: 'received',\n    department: 'intake',\n    estimated_duration_minutes: 30,\n    auto_assign: true,\n    requires_quality_check: false\n  }, {\n    name: 'design',\n    department: 'design',\n    estimated_duration_minutes: 180,\n    auto_assign: false,\n    requires_quality_check: true\n  }, {\n    name: 'milling',\n    department: 'production',\n    estimated_duration_minutes: 220,\n    auto_assign: true,\n    requires_quality_check: false\n  }, {\n    name: 'qc',\n    department: 'quality',\n    estimated_duration_minutes: 60,\n    auto_assign: false,\n    requires_quality_check: true\n  }],\n  assigned_technician_id: null,\n  due_date: '2024-02-20T17:00:00Z',\n  created_at: '2024-01-19T16:00:00Z',\n  updated_at: '2024-01-19T16:00:00Z',\n  notes: ['2024-01-19T16:00:00Z: Case received - 4 porcelain veneers'],\n  files: [{\n    id: 'F006',\n    name: 'veneer_impressions.stl',\n    url: '/files/F006',\n    size: 4194304,\n    uploaded_at: '2024-01-19T16:00:00Z',\n    uploaded_by: 'Dr. Johnson'\n  }, {\n    id: 'F007',\n    name: 'shade_selection.jpg',\n    url: '/files/F007',\n    size: 851968,\n    uploaded_at: '2024-01-19T16:02:00Z',\n    uploaded_by: 'Dr. Johnson'\n  }],\n  days_until_due: 20,\n  is_overdue: false,\n  current_stage: 'Received'\n}, {\n  id: 'LAB-2024-005',\n  clinic: mockClinics[1],\n  patient_name: 'Thomas Brown',\n  tooth_number: 'Full Upper',\n  service_type: 'denture',\n  priority: 'urgent',\n  status: 'delivered',\n  current_stage_index: 5,\n  workflow_stages: [{\n    name: 'received',\n    department: 'intake',\n    estimated_duration_minutes: 45,\n    auto_assign: true,\n    requires_quality_check: false\n  }, {\n    name: 'design',\n    department: 'design',\n    estimated_duration_minutes: 240,\n    auto_assign: false,\n    requires_quality_check: true\n  }, {\n    name: 'milling',\n    department: 'production',\n    estimated_duration_minutes: 300,\n    auto_assign: true,\n    requires_quality_check: false\n  }, {\n    name: 'fitting',\n    department: 'production',\n    estimated_duration_minutes: 180,\n    auto_assign: false,\n    requires_quality_check: false\n  }, {\n    name: 'qc',\n    department: 'quality',\n    estimated_duration_minutes: 90,\n    auto_assign: false,\n    requires_quality_check: true\n  }, {\n    name: 'delivered',\n    department: 'shipping',\n    estimated_duration_minutes: 15,\n    auto_assign: true,\n    requires_quality_check: false\n  }],\n  assigned_technician_id: 'TECH-004',\n  due_date: '2024-01-18T17:00:00Z',\n  created_at: '2024-01-05T11:00:00Z',\n  updated_at: '2024-01-18T15:30:00Z',\n  notes: ['2024-01-05T11:00:00Z: Complete denture case received', '2024-01-08T14:00:00Z: Design phase completed - Patient approval required', '2024-01-10T10:30:00Z: Design approved, milling started', '2024-01-15T16:00:00Z: Fitting adjustments completed', '2024-01-17T13:45:00Z: Quality control passed', '2024-01-18T15:30:00Z: Delivered to clinic'],\n  files: [{\n    id: 'F008',\n    name: 'denture_impression.stl',\n    url: '/files/F008',\n    size: 5242880,\n    uploaded_at: '2024-01-05T11:00:00Z',\n    uploaded_by: 'Dr. Smith'\n  }, {\n    id: 'F009',\n    name: 'bite_record.stl',\n    url: '/files/F009',\n    size: 2097152,\n    uploaded_at: '2024-01-05T11:05:00Z',\n    uploaded_by: 'Dr. Smith'\n  }],\n  days_until_due: 0,\n  is_overdue: false,\n  current_stage: 'Delivered'\n}];\n\n// Mock Tasks\nexport const mockTasks = [{\n  id: 'TASK-001',\n  case_id: 'LAB-2024-001',\n  stage_name: 'milling',\n  assigned_technician: mockTechnicians[0],\n  status: 'in_progress',\n  priority: 'normal',\n  estimated_duration: 180,\n  actual_duration: 120,\n  started_at: '2024-01-16T09:00:00Z',\n  notes: ['Material loaded: IPS e.max CAD', 'Milling 60% complete']\n}, {\n  id: 'TASK-002',\n  case_id: 'LAB-2024-002',\n  stage_name: 'qc',\n  assigned_technician: mockTechnicians[2],\n  status: 'in_progress',\n  priority: 'urgent',\n  estimated_duration: 60,\n  started_at: '2024-01-18T14:00:00Z',\n  notes: ['Checking fit and finish', 'Color verification in progress']\n}, {\n  id: 'TASK-003',\n  case_id: 'LAB-2024-003',\n  stage_name: 'design',\n  assigned_technician: mockTechnicians[1],\n  status: 'in_progress',\n  priority: 'stat',\n  estimated_duration: 150,\n  started_at: '2024-01-19T08:00:00Z',\n  notes: ['STAT priority - rush processing', 'CAD design 70% complete']\n}, {\n  id: 'TASK-004',\n  case_id: 'LAB-2024-004',\n  stage_name: 'received',\n  status: 'pending',\n  priority: 'normal',\n  estimated_duration: 30,\n  notes: ['Waiting for assignment', 'Standard priority case']\n}];\n\n// Dashboard Statistics\nexport const mockDashboardStats = {\n  active_cases: 24,\n  completed_today: 8,\n  pending_qc: 5,\n  revenue_today: 2850,\n  overdue_cases: 3,\n  total_technicians: 12,\n  capacity_utilization: 78\n};\n\n// Helper functions\nexport const getMockCasesByStatus = status => {\n  return mockCases.filter(case_ => case_.status === status);\n};\nexport const getMockOverdueCases = () => {\n  return mockCases.filter(case_ => case_.is_overdue);\n};\nexport const getMockCaseById = id => {\n  return mockCases.find(case_ => case_.id === id);\n};\nexport const getMockTasksByTechnician = technicianId => {\n  return mockTasks.filter(task => {\n    var _task$assigned_techni;\n    return ((_task$assigned_techni = task.assigned_technician) === null || _task$assigned_techni === void 0 ? void 0 : _task$assigned_techni.id) === technicianId;\n  });\n};\nexport const getMockTasksByStatus = status => {\n  return mockTasks.filter(task => task.status === status);\n};", "map": {"version": 3, "names": ["mockClinics", "id", "name", "email", "phone", "address", "contact_person", "is_active", "created_at", "mockTechnicians", "first_name", "last_name", "specialties", "current_workload", "max_capacity", "mockCases", "clinic", "patient_name", "tooth_number", "service_type", "priority", "status", "current_stage_index", "workflow_stages", "department", "estimated_duration_minutes", "auto_assign", "requires_quality_check", "assigned_technician_id", "due_date", "updated_at", "notes", "files", "url", "size", "uploaded_at", "uploaded_by", "days_until_due", "is_overdue", "current_stage", "mockTasks", "case_id", "stage_name", "assigned_technician", "estimated_duration", "actual_duration", "started_at", "mockDashboardStats", "active_cases", "completed_today", "pending_qc", "revenue_today", "overdue_cases", "total_technicians", "capacity_utilization", "getMockCasesByStatus", "filter", "case_", "getMockOverdueCases", "getMockCaseById", "find", "getMockTasksByTechnician", "technicianId", "task", "_task$assigned_techni", "getMockTasksByStatus"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/api/mockData.ts"], "sourcesContent": ["/**\n * Enhanced Mock Data for DentFlow API Services\n * Comprehensive test data for all modules\n */\n\nimport { Case, Clinic, WorkflowStage, Technician, Task } from './types';\n\n// Mock Clinics\nexport const mockClinics: Clinic[] = [\n  {\n    id: 'CLI-001',\n    name: 'Smile Dental Clinic',\n    email: '<EMAIL>',\n    phone: '******-0123',\n    address: '123 Main St, Downtown, NY 10001',\n    contact_person: 'Dr. <PERSON>',\n    is_active: true,\n    created_at: '2023-01-15T10:00:00Z'\n  },\n  {\n    id: 'CLI-002',\n    name: 'City Dental Care',\n    email: '<EMAIL>',\n    phone: '******-0456',\n    address: '456 Oak Ave, Midtown, NY 10002',\n    contact_person: 'Dr<PERSON> <PERSON>',\n    is_active: true,\n    created_at: '2023-02-20T14:30:00Z'\n  },\n  {\n    id: 'CLI-003',\n    name: 'Family Dentistry Plus',\n    email: '<EMAIL>',\n    phone: '******-0789',\n    address: '789 Pine St, Uptown, NY 10003',\n    contact_person: 'Dr. <PERSON>',\n    is_active: true,\n    created_at: '2023-03-10T09:15:00Z'\n  }\n];\n\n// Mock Technicians\nexport const mockTechnicians: Technician[] = [\n  {\n    id: 'TECH-001',\n    first_name: 'Maria',\n    last_name: 'Rodriguez',\n    email: '<EMAIL>',\n    specialties: ['crown', 'bridge', 'veneer'],\n    is_active: true,\n    current_workload: 6,\n    max_capacity: 8\n  },\n  {\n    id: 'TECH-002',\n    first_name: 'David',\n    last_name: 'Chen',\n    email: '<EMAIL>',\n    specialties: ['implant', 'denture'],\n    is_active: true,\n    current_workload: 4,\n    max_capacity: 6\n  },\n  {\n    id: 'TECH-003',\n    first_name: 'Sarah',\n    last_name: 'Thompson',\n    email: '<EMAIL>',\n    specialties: ['crown', 'veneer', 'bridge'],\n    is_active: true,\n    current_workload: 7,\n    max_capacity: 8\n  },\n  {\n    id: 'TECH-004',\n    first_name: 'Michael',\n    last_name: 'Johnson',\n    email: '<EMAIL>',\n    specialties: ['denture', 'implant'],\n    is_active: true,\n    current_workload: 3,\n    max_capacity: 5\n  }\n];\n\n// Mock Cases with enhanced data\nexport const mockCases: Case[] = [\n  {\n    id: 'LAB-2024-001',\n    clinic: mockClinics[0],\n    patient_name: 'John Doe',\n    tooth_number: '#15',\n    service_type: 'crown',\n    priority: 'normal',\n    status: 'milling',\n    current_stage_index: 2,\n    workflow_stages: [\n      { name: 'received', department: 'intake', estimated_duration_minutes: 30, auto_assign: true, requires_quality_check: false },\n      { name: 'design', department: 'design', estimated_duration_minutes: 120, auto_assign: false, requires_quality_check: true },\n      { name: 'milling', department: 'production', estimated_duration_minutes: 180, auto_assign: true, requires_quality_check: false },\n      { name: 'sintering', department: 'production', estimated_duration_minutes: 240, auto_assign: true, requires_quality_check: false },\n      { name: 'qc', department: 'quality', estimated_duration_minutes: 45, auto_assign: false, requires_quality_check: true }\n    ],\n    assigned_technician_id: 'TECH-001',\n    due_date: '2024-02-15T17:00:00Z',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-16T14:30:00Z',\n    notes: [\n      '2024-01-15T10:00:00Z: Case received - Standard crown procedure',\n      '2024-01-15T11:30:00Z: Design phase completed - Color match A3',\n      '2024-01-16T14:30:00Z: Milling in progress'\n    ],\n    files: [\n      { id: 'F001', name: 'impression.stl', url: '/files/F001', size: 2048576, uploaded_at: '2024-01-15T10:00:00Z', uploaded_by: 'Dr. Johnson' },\n      { id: 'F002', name: 'color_guide.jpg', url: '/files/F002', size: 1024768, uploaded_at: '2024-01-15T10:05:00Z', uploaded_by: 'Dr. Johnson' }\n    ],\n    days_until_due: 12,\n    is_overdue: false,\n    current_stage: 'Milling'\n  },\n  {\n    id: 'LAB-2024-002',\n    clinic: mockClinics[1],\n    patient_name: 'Jane Smith',\n    tooth_number: '#12-14',\n    service_type: 'bridge',\n    priority: 'urgent',\n    status: 'qc',\n    current_stage_index: 4,\n    workflow_stages: [\n      { name: 'received', department: 'intake', estimated_duration_minutes: 30, auto_assign: true, requires_quality_check: false },\n      { name: 'design', department: 'design', estimated_duration_minutes: 180, auto_assign: false, requires_quality_check: true },\n      { name: 'milling', department: 'production', estimated_duration_minutes: 240, auto_assign: true, requires_quality_check: false },\n      { name: 'sintering', department: 'production', estimated_duration_minutes: 300, auto_assign: true, requires_quality_check: false },\n      { name: 'qc', department: 'quality', estimated_duration_minutes: 60, auto_assign: false, requires_quality_check: true }\n    ],\n    assigned_technician_id: 'TECH-003',\n    due_date: '2024-02-10T17:00:00Z',\n    created_at: '2024-01-08T09:00:00Z',\n    updated_at: '2024-01-18T16:45:00Z',\n    notes: [\n      '2024-01-08T09:00:00Z: Urgent case received - 3-unit bridge',\n      '2024-01-10T15:30:00Z: Design approved by dentist',\n      '2024-01-15T11:00:00Z: Milling completed',\n      '2024-01-17T14:20:00Z: Sintering completed',\n      '2024-01-18T16:45:00Z: Quality control in progress'\n    ],\n    files: [\n      { id: 'F003', name: 'bridge_impression.stl', url: '/files/F003', size: 3145728, uploaded_at: '2024-01-08T09:00:00Z', uploaded_by: 'Dr. Smith' },\n      { id: 'F004', name: 'bite_registration.stl', url: '/files/F004', size: 1572864, uploaded_at: '2024-01-08T09:05:00Z', uploaded_by: 'Dr. Smith' }\n    ],\n    days_until_due: 5,\n    is_overdue: false,\n    current_stage: 'Quality Control'\n  },\n  {\n    id: 'LAB-2024-003',\n    clinic: mockClinics[2],\n    patient_name: 'Robert Wilson',\n    tooth_number: '#6',\n    service_type: 'implant',\n    priority: 'stat',\n    status: 'design',\n    current_stage_index: 1,\n    workflow_stages: [\n      { name: 'received', department: 'intake', estimated_duration_minutes: 30, auto_assign: true, requires_quality_check: false },\n      { name: 'design', department: 'design', estimated_duration_minutes: 150, auto_assign: false, requires_quality_check: true },\n      { name: 'milling', department: 'production', estimated_duration_minutes: 200, auto_assign: true, requires_quality_check: false },\n      { name: 'qc', department: 'quality', estimated_duration_minutes: 45, auto_assign: false, requires_quality_check: true }\n    ],\n    assigned_technician_id: 'TECH-002',\n    due_date: '2024-01-22T17:00:00Z',\n    created_at: '2024-01-18T14:00:00Z',\n    updated_at: '2024-01-19T10:30:00Z',\n    notes: [\n      '2024-01-18T14:00:00Z: STAT case received - Implant crown',\n      '2024-01-19T10:30:00Z: Design phase started - Priority processing'\n    ],\n    files: [\n      { id: 'F005', name: 'implant_scan.stl', url: '/files/F005', size: 2621440, uploaded_at: '2024-01-18T14:00:00Z', uploaded_by: 'Dr. Wilson' }\n    ],\n    days_until_due: 2,\n    is_overdue: false,\n    current_stage: 'Design'\n  },\n  {\n    id: 'LAB-2024-004',\n    clinic: mockClinics[0],\n    patient_name: 'Emily Davis',\n    tooth_number: '#7-10',\n    service_type: 'veneer',\n    priority: 'normal',\n    status: 'received',\n    current_stage_index: 0,\n    workflow_stages: [\n      { name: 'received', department: 'intake', estimated_duration_minutes: 30, auto_assign: true, requires_quality_check: false },\n      { name: 'design', department: 'design', estimated_duration_minutes: 180, auto_assign: false, requires_quality_check: true },\n      { name: 'milling', department: 'production', estimated_duration_minutes: 220, auto_assign: true, requires_quality_check: false },\n      { name: 'qc', department: 'quality', estimated_duration_minutes: 60, auto_assign: false, requires_quality_check: true }\n    ],\n    assigned_technician_id: null,\n    due_date: '2024-02-20T17:00:00Z',\n    created_at: '2024-01-19T16:00:00Z',\n    updated_at: '2024-01-19T16:00:00Z',\n    notes: [\n      '2024-01-19T16:00:00Z: Case received - 4 porcelain veneers'\n    ],\n    files: [\n      { id: 'F006', name: 'veneer_impressions.stl', url: '/files/F006', size: 4194304, uploaded_at: '2024-01-19T16:00:00Z', uploaded_by: 'Dr. Johnson' },\n      { id: 'F007', name: 'shade_selection.jpg', url: '/files/F007', size: 851968, uploaded_at: '2024-01-19T16:02:00Z', uploaded_by: 'Dr. Johnson' }\n    ],\n    days_until_due: 20,\n    is_overdue: false,\n    current_stage: 'Received'\n  },\n  {\n    id: 'LAB-2024-005',\n    clinic: mockClinics[1],\n    patient_name: 'Thomas Brown',\n    tooth_number: 'Full Upper',\n    service_type: 'denture',\n    priority: 'urgent',\n    status: 'delivered',\n    current_stage_index: 5,\n    workflow_stages: [\n      { name: 'received', department: 'intake', estimated_duration_minutes: 45, auto_assign: true, requires_quality_check: false },\n      { name: 'design', department: 'design', estimated_duration_minutes: 240, auto_assign: false, requires_quality_check: true },\n      { name: 'milling', department: 'production', estimated_duration_minutes: 300, auto_assign: true, requires_quality_check: false },\n      { name: 'fitting', department: 'production', estimated_duration_minutes: 180, auto_assign: false, requires_quality_check: false },\n      { name: 'qc', department: 'quality', estimated_duration_minutes: 90, auto_assign: false, requires_quality_check: true },\n      { name: 'delivered', department: 'shipping', estimated_duration_minutes: 15, auto_assign: true, requires_quality_check: false }\n    ],\n    assigned_technician_id: 'TECH-004',\n    due_date: '2024-01-18T17:00:00Z',\n    created_at: '2024-01-05T11:00:00Z',\n    updated_at: '2024-01-18T15:30:00Z',\n    notes: [\n      '2024-01-05T11:00:00Z: Complete denture case received',\n      '2024-01-08T14:00:00Z: Design phase completed - Patient approval required',\n      '2024-01-10T10:30:00Z: Design approved, milling started',\n      '2024-01-15T16:00:00Z: Fitting adjustments completed',\n      '2024-01-17T13:45:00Z: Quality control passed',\n      '2024-01-18T15:30:00Z: Delivered to clinic'\n    ],\n    files: [\n      { id: 'F008', name: 'denture_impression.stl', url: '/files/F008', size: 5242880, uploaded_at: '2024-01-05T11:00:00Z', uploaded_by: 'Dr. Smith' },\n      { id: 'F009', name: 'bite_record.stl', url: '/files/F009', size: 2097152, uploaded_at: '2024-01-05T11:05:00Z', uploaded_by: 'Dr. Smith' }\n    ],\n    days_until_due: 0,\n    is_overdue: false,\n    current_stage: 'Delivered'\n  }\n];\n\n// Mock Tasks\nexport const mockTasks: Task[] = [\n  {\n    id: 'TASK-001',\n    case_id: 'LAB-2024-001',\n    stage_name: 'milling',\n    assigned_technician: mockTechnicians[0],\n    status: 'in_progress',\n    priority: 'normal',\n    estimated_duration: 180,\n    actual_duration: 120,\n    started_at: '2024-01-16T09:00:00Z',\n    notes: ['Material loaded: IPS e.max CAD', 'Milling 60% complete']\n  },\n  {\n    id: 'TASK-002',\n    case_id: 'LAB-2024-002',\n    stage_name: 'qc',\n    assigned_technician: mockTechnicians[2],\n    status: 'in_progress',\n    priority: 'urgent',\n    estimated_duration: 60,\n    started_at: '2024-01-18T14:00:00Z',\n    notes: ['Checking fit and finish', 'Color verification in progress']\n  },\n  {\n    id: 'TASK-003',\n    case_id: 'LAB-2024-003',\n    stage_name: 'design',\n    assigned_technician: mockTechnicians[1],\n    status: 'in_progress',\n    priority: 'stat',\n    estimated_duration: 150,\n    started_at: '2024-01-19T08:00:00Z',\n    notes: ['STAT priority - rush processing', 'CAD design 70% complete']\n  },\n  {\n    id: 'TASK-004',\n    case_id: 'LAB-2024-004',\n    stage_name: 'received',\n    status: 'pending',\n    priority: 'normal',\n    estimated_duration: 30,\n    notes: ['Waiting for assignment', 'Standard priority case']\n  }\n];\n\n// Dashboard Statistics\nexport const mockDashboardStats = {\n  active_cases: 24,\n  completed_today: 8,\n  pending_qc: 5,\n  revenue_today: 2850,\n  overdue_cases: 3,\n  total_technicians: 12,\n  capacity_utilization: 78\n};\n\n// Helper functions\nexport const getMockCasesByStatus = (status: string): Case[] => {\n  return mockCases.filter(case_ => case_.status === status);\n};\n\nexport const getMockOverdueCases = (): Case[] => {\n  return mockCases.filter(case_ => case_.is_overdue);\n};\n\nexport const getMockCaseById = (id: string): Case | undefined => {\n  return mockCases.find(case_ => case_.id === id);\n};\n\nexport const getMockTasksByTechnician = (technicianId: string): Task[] => {\n  return mockTasks.filter(task => task.assigned_technician?.id === technicianId);\n};\n\nexport const getMockTasksByStatus = (status: string): Task[] => {\n  return mockTasks.filter(task => task.status === status);\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAIA;AACA,OAAO,MAAMA,WAAqB,GAAG,CACnC;EACEC,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,KAAK,EAAE,4BAA4B;EACnCC,KAAK,EAAE,aAAa;EACpBC,OAAO,EAAE,iCAAiC;EAC1CC,cAAc,EAAE,aAAa;EAC7BC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE;AACd,CAAC,EACD;EACEP,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE,4BAA4B;EACnCC,KAAK,EAAE,aAAa;EACpBC,OAAO,EAAE,gCAAgC;EACzCC,cAAc,EAAE,WAAW;EAC3BC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE;AACd,CAAC,EACD;EACEP,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,uBAAuB;EAC7BC,KAAK,EAAE,+BAA+B;EACtCC,KAAK,EAAE,aAAa;EACpBC,OAAO,EAAE,+BAA+B;EACxCC,cAAc,EAAE,YAAY;EAC5BC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE;AACd,CAAC,CACF;;AAED;AACA,OAAO,MAAMC,eAA6B,GAAG,CAC3C;EACER,EAAE,EAAE,UAAU;EACdS,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE,WAAW;EACtBR,KAAK,EAAE,8BAA8B;EACrCS,WAAW,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAC1CL,SAAS,EAAE,IAAI;EACfM,gBAAgB,EAAE,CAAC;EACnBC,YAAY,EAAE;AAChB,CAAC,EACD;EACEb,EAAE,EAAE,UAAU;EACdS,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE,MAAM;EACjBR,KAAK,EAAE,yBAAyB;EAChCS,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;EACnCL,SAAS,EAAE,IAAI;EACfM,gBAAgB,EAAE,CAAC;EACnBC,YAAY,EAAE;AAChB,CAAC,EACD;EACEb,EAAE,EAAE,UAAU;EACdS,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE,UAAU;EACrBR,KAAK,EAAE,6BAA6B;EACpCS,WAAW,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAC1CL,SAAS,EAAE,IAAI;EACfM,gBAAgB,EAAE,CAAC;EACnBC,YAAY,EAAE;AAChB,CAAC,EACD;EACEb,EAAE,EAAE,UAAU;EACdS,UAAU,EAAE,SAAS;EACrBC,SAAS,EAAE,SAAS;EACpBR,KAAK,EAAE,8BAA8B;EACrCS,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;EACnCL,SAAS,EAAE,IAAI;EACfM,gBAAgB,EAAE,CAAC;EACnBC,YAAY,EAAE;AAChB,CAAC,CACF;;AAED;AACA,OAAO,MAAMC,SAAiB,GAAG,CAC/B;EACEd,EAAE,EAAE,cAAc;EAClBe,MAAM,EAAEhB,WAAW,CAAC,CAAC,CAAC;EACtBiB,YAAY,EAAE,UAAU;EACxBC,YAAY,EAAE,KAAK;EACnBC,YAAY,EAAE,OAAO;EACrBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,SAAS;EACjBC,mBAAmB,EAAE,CAAC;EACtBC,eAAe,EAAE,CACf;IAAErB,IAAI,EAAE,UAAU;IAAEsB,UAAU,EAAE,QAAQ;IAAEC,0BAA0B,EAAE,EAAE;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EAC5H;IAAEzB,IAAI,EAAE,QAAQ;IAAEsB,UAAU,EAAE,QAAQ;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,KAAK;IAAEC,sBAAsB,EAAE;EAAK,CAAC,EAC3H;IAAEzB,IAAI,EAAE,SAAS;IAAEsB,UAAU,EAAE,YAAY;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EAChI;IAAEzB,IAAI,EAAE,WAAW;IAAEsB,UAAU,EAAE,YAAY;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EAClI;IAAEzB,IAAI,EAAE,IAAI;IAAEsB,UAAU,EAAE,SAAS;IAAEC,0BAA0B,EAAE,EAAE;IAAEC,WAAW,EAAE,KAAK;IAAEC,sBAAsB,EAAE;EAAK,CAAC,CACxH;EACDC,sBAAsB,EAAE,UAAU;EAClCC,QAAQ,EAAE,sBAAsB;EAChCrB,UAAU,EAAE,sBAAsB;EAClCsB,UAAU,EAAE,sBAAsB;EAClCC,KAAK,EAAE,CACL,gEAAgE,EAChE,+DAA+D,EAC/D,2CAA2C,CAC5C;EACDC,KAAK,EAAE,CACL;IAAE/B,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,gBAAgB;IAAE+B,GAAG,EAAE,aAAa;IAAEC,IAAI,EAAE,OAAO;IAAEC,WAAW,EAAE,sBAAsB;IAAEC,WAAW,EAAE;EAAc,CAAC,EAC1I;IAAEnC,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,iBAAiB;IAAE+B,GAAG,EAAE,aAAa;IAAEC,IAAI,EAAE,OAAO;IAAEC,WAAW,EAAE,sBAAsB;IAAEC,WAAW,EAAE;EAAc,CAAC,CAC5I;EACDC,cAAc,EAAE,EAAE;EAClBC,UAAU,EAAE,KAAK;EACjBC,aAAa,EAAE;AACjB,CAAC,EACD;EACEtC,EAAE,EAAE,cAAc;EAClBe,MAAM,EAAEhB,WAAW,CAAC,CAAC,CAAC;EACtBiB,YAAY,EAAE,YAAY;EAC1BC,YAAY,EAAE,QAAQ;EACtBC,YAAY,EAAE,QAAQ;EACtBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,IAAI;EACZC,mBAAmB,EAAE,CAAC;EACtBC,eAAe,EAAE,CACf;IAAErB,IAAI,EAAE,UAAU;IAAEsB,UAAU,EAAE,QAAQ;IAAEC,0BAA0B,EAAE,EAAE;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EAC5H;IAAEzB,IAAI,EAAE,QAAQ;IAAEsB,UAAU,EAAE,QAAQ;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,KAAK;IAAEC,sBAAsB,EAAE;EAAK,CAAC,EAC3H;IAAEzB,IAAI,EAAE,SAAS;IAAEsB,UAAU,EAAE,YAAY;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EAChI;IAAEzB,IAAI,EAAE,WAAW;IAAEsB,UAAU,EAAE,YAAY;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EAClI;IAAEzB,IAAI,EAAE,IAAI;IAAEsB,UAAU,EAAE,SAAS;IAAEC,0BAA0B,EAAE,EAAE;IAAEC,WAAW,EAAE,KAAK;IAAEC,sBAAsB,EAAE;EAAK,CAAC,CACxH;EACDC,sBAAsB,EAAE,UAAU;EAClCC,QAAQ,EAAE,sBAAsB;EAChCrB,UAAU,EAAE,sBAAsB;EAClCsB,UAAU,EAAE,sBAAsB;EAClCC,KAAK,EAAE,CACL,4DAA4D,EAC5D,kDAAkD,EAClD,yCAAyC,EACzC,2CAA2C,EAC3C,mDAAmD,CACpD;EACDC,KAAK,EAAE,CACL;IAAE/B,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,uBAAuB;IAAE+B,GAAG,EAAE,aAAa;IAAEC,IAAI,EAAE,OAAO;IAAEC,WAAW,EAAE,sBAAsB;IAAEC,WAAW,EAAE;EAAY,CAAC,EAC/I;IAAEnC,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,uBAAuB;IAAE+B,GAAG,EAAE,aAAa;IAAEC,IAAI,EAAE,OAAO;IAAEC,WAAW,EAAE,sBAAsB;IAAEC,WAAW,EAAE;EAAY,CAAC,CAChJ;EACDC,cAAc,EAAE,CAAC;EACjBC,UAAU,EAAE,KAAK;EACjBC,aAAa,EAAE;AACjB,CAAC,EACD;EACEtC,EAAE,EAAE,cAAc;EAClBe,MAAM,EAAEhB,WAAW,CAAC,CAAC,CAAC;EACtBiB,YAAY,EAAE,eAAe;EAC7BC,YAAY,EAAE,IAAI;EAClBC,YAAY,EAAE,SAAS;EACvBC,QAAQ,EAAE,MAAM;EAChBC,MAAM,EAAE,QAAQ;EAChBC,mBAAmB,EAAE,CAAC;EACtBC,eAAe,EAAE,CACf;IAAErB,IAAI,EAAE,UAAU;IAAEsB,UAAU,EAAE,QAAQ;IAAEC,0BAA0B,EAAE,EAAE;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EAC5H;IAAEzB,IAAI,EAAE,QAAQ;IAAEsB,UAAU,EAAE,QAAQ;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,KAAK;IAAEC,sBAAsB,EAAE;EAAK,CAAC,EAC3H;IAAEzB,IAAI,EAAE,SAAS;IAAEsB,UAAU,EAAE,YAAY;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EAChI;IAAEzB,IAAI,EAAE,IAAI;IAAEsB,UAAU,EAAE,SAAS;IAAEC,0BAA0B,EAAE,EAAE;IAAEC,WAAW,EAAE,KAAK;IAAEC,sBAAsB,EAAE;EAAK,CAAC,CACxH;EACDC,sBAAsB,EAAE,UAAU;EAClCC,QAAQ,EAAE,sBAAsB;EAChCrB,UAAU,EAAE,sBAAsB;EAClCsB,UAAU,EAAE,sBAAsB;EAClCC,KAAK,EAAE,CACL,0DAA0D,EAC1D,kEAAkE,CACnE;EACDC,KAAK,EAAE,CACL;IAAE/B,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,kBAAkB;IAAE+B,GAAG,EAAE,aAAa;IAAEC,IAAI,EAAE,OAAO;IAAEC,WAAW,EAAE,sBAAsB;IAAEC,WAAW,EAAE;EAAa,CAAC,CAC5I;EACDC,cAAc,EAAE,CAAC;EACjBC,UAAU,EAAE,KAAK;EACjBC,aAAa,EAAE;AACjB,CAAC,EACD;EACEtC,EAAE,EAAE,cAAc;EAClBe,MAAM,EAAEhB,WAAW,CAAC,CAAC,CAAC;EACtBiB,YAAY,EAAE,aAAa;EAC3BC,YAAY,EAAE,OAAO;EACrBC,YAAY,EAAE,QAAQ;EACtBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,UAAU;EAClBC,mBAAmB,EAAE,CAAC;EACtBC,eAAe,EAAE,CACf;IAAErB,IAAI,EAAE,UAAU;IAAEsB,UAAU,EAAE,QAAQ;IAAEC,0BAA0B,EAAE,EAAE;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EAC5H;IAAEzB,IAAI,EAAE,QAAQ;IAAEsB,UAAU,EAAE,QAAQ;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,KAAK;IAAEC,sBAAsB,EAAE;EAAK,CAAC,EAC3H;IAAEzB,IAAI,EAAE,SAAS;IAAEsB,UAAU,EAAE,YAAY;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EAChI;IAAEzB,IAAI,EAAE,IAAI;IAAEsB,UAAU,EAAE,SAAS;IAAEC,0BAA0B,EAAE,EAAE;IAAEC,WAAW,EAAE,KAAK;IAAEC,sBAAsB,EAAE;EAAK,CAAC,CACxH;EACDC,sBAAsB,EAAE,IAAI;EAC5BC,QAAQ,EAAE,sBAAsB;EAChCrB,UAAU,EAAE,sBAAsB;EAClCsB,UAAU,EAAE,sBAAsB;EAClCC,KAAK,EAAE,CACL,2DAA2D,CAC5D;EACDC,KAAK,EAAE,CACL;IAAE/B,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,wBAAwB;IAAE+B,GAAG,EAAE,aAAa;IAAEC,IAAI,EAAE,OAAO;IAAEC,WAAW,EAAE,sBAAsB;IAAEC,WAAW,EAAE;EAAc,CAAC,EAClJ;IAAEnC,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,qBAAqB;IAAE+B,GAAG,EAAE,aAAa;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAE,sBAAsB;IAAEC,WAAW,EAAE;EAAc,CAAC,CAC/I;EACDC,cAAc,EAAE,EAAE;EAClBC,UAAU,EAAE,KAAK;EACjBC,aAAa,EAAE;AACjB,CAAC,EACD;EACEtC,EAAE,EAAE,cAAc;EAClBe,MAAM,EAAEhB,WAAW,CAAC,CAAC,CAAC;EACtBiB,YAAY,EAAE,cAAc;EAC5BC,YAAY,EAAE,YAAY;EAC1BC,YAAY,EAAE,SAAS;EACvBC,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,WAAW;EACnBC,mBAAmB,EAAE,CAAC;EACtBC,eAAe,EAAE,CACf;IAAErB,IAAI,EAAE,UAAU;IAAEsB,UAAU,EAAE,QAAQ;IAAEC,0BAA0B,EAAE,EAAE;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EAC5H;IAAEzB,IAAI,EAAE,QAAQ;IAAEsB,UAAU,EAAE,QAAQ;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,KAAK;IAAEC,sBAAsB,EAAE;EAAK,CAAC,EAC3H;IAAEzB,IAAI,EAAE,SAAS;IAAEsB,UAAU,EAAE,YAAY;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EAChI;IAAEzB,IAAI,EAAE,SAAS;IAAEsB,UAAU,EAAE,YAAY;IAAEC,0BAA0B,EAAE,GAAG;IAAEC,WAAW,EAAE,KAAK;IAAEC,sBAAsB,EAAE;EAAM,CAAC,EACjI;IAAEzB,IAAI,EAAE,IAAI;IAAEsB,UAAU,EAAE,SAAS;IAAEC,0BAA0B,EAAE,EAAE;IAAEC,WAAW,EAAE,KAAK;IAAEC,sBAAsB,EAAE;EAAK,CAAC,EACvH;IAAEzB,IAAI,EAAE,WAAW;IAAEsB,UAAU,EAAE,UAAU;IAAEC,0BAA0B,EAAE,EAAE;IAAEC,WAAW,EAAE,IAAI;IAAEC,sBAAsB,EAAE;EAAM,CAAC,CAChI;EACDC,sBAAsB,EAAE,UAAU;EAClCC,QAAQ,EAAE,sBAAsB;EAChCrB,UAAU,EAAE,sBAAsB;EAClCsB,UAAU,EAAE,sBAAsB;EAClCC,KAAK,EAAE,CACL,sDAAsD,EACtD,0EAA0E,EAC1E,wDAAwD,EACxD,qDAAqD,EACrD,8CAA8C,EAC9C,2CAA2C,CAC5C;EACDC,KAAK,EAAE,CACL;IAAE/B,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,wBAAwB;IAAE+B,GAAG,EAAE,aAAa;IAAEC,IAAI,EAAE,OAAO;IAAEC,WAAW,EAAE,sBAAsB;IAAEC,WAAW,EAAE;EAAY,CAAC,EAChJ;IAAEnC,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,iBAAiB;IAAE+B,GAAG,EAAE,aAAa;IAAEC,IAAI,EAAE,OAAO;IAAEC,WAAW,EAAE,sBAAsB;IAAEC,WAAW,EAAE;EAAY,CAAC,CAC1I;EACDC,cAAc,EAAE,CAAC;EACjBC,UAAU,EAAE,KAAK;EACjBC,aAAa,EAAE;AACjB,CAAC,CACF;;AAED;AACA,OAAO,MAAMC,SAAiB,GAAG,CAC/B;EACEvC,EAAE,EAAE,UAAU;EACdwC,OAAO,EAAE,cAAc;EACvBC,UAAU,EAAE,SAAS;EACrBC,mBAAmB,EAAElC,eAAe,CAAC,CAAC,CAAC;EACvCY,MAAM,EAAE,aAAa;EACrBD,QAAQ,EAAE,QAAQ;EAClBwB,kBAAkB,EAAE,GAAG;EACvBC,eAAe,EAAE,GAAG;EACpBC,UAAU,EAAE,sBAAsB;EAClCf,KAAK,EAAE,CAAC,gCAAgC,EAAE,sBAAsB;AAClE,CAAC,EACD;EACE9B,EAAE,EAAE,UAAU;EACdwC,OAAO,EAAE,cAAc;EACvBC,UAAU,EAAE,IAAI;EAChBC,mBAAmB,EAAElC,eAAe,CAAC,CAAC,CAAC;EACvCY,MAAM,EAAE,aAAa;EACrBD,QAAQ,EAAE,QAAQ;EAClBwB,kBAAkB,EAAE,EAAE;EACtBE,UAAU,EAAE,sBAAsB;EAClCf,KAAK,EAAE,CAAC,yBAAyB,EAAE,gCAAgC;AACrE,CAAC,EACD;EACE9B,EAAE,EAAE,UAAU;EACdwC,OAAO,EAAE,cAAc;EACvBC,UAAU,EAAE,QAAQ;EACpBC,mBAAmB,EAAElC,eAAe,CAAC,CAAC,CAAC;EACvCY,MAAM,EAAE,aAAa;EACrBD,QAAQ,EAAE,MAAM;EAChBwB,kBAAkB,EAAE,GAAG;EACvBE,UAAU,EAAE,sBAAsB;EAClCf,KAAK,EAAE,CAAC,iCAAiC,EAAE,yBAAyB;AACtE,CAAC,EACD;EACE9B,EAAE,EAAE,UAAU;EACdwC,OAAO,EAAE,cAAc;EACvBC,UAAU,EAAE,UAAU;EACtBrB,MAAM,EAAE,SAAS;EACjBD,QAAQ,EAAE,QAAQ;EAClBwB,kBAAkB,EAAE,EAAE;EACtBb,KAAK,EAAE,CAAC,wBAAwB,EAAE,wBAAwB;AAC5D,CAAC,CACF;;AAED;AACA,OAAO,MAAMgB,kBAAkB,GAAG;EAChCC,YAAY,EAAE,EAAE;EAChBC,eAAe,EAAE,CAAC;EAClBC,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,IAAI;EACnBC,aAAa,EAAE,CAAC;EAChBC,iBAAiB,EAAE,EAAE;EACrBC,oBAAoB,EAAE;AACxB,CAAC;;AAED;AACA,OAAO,MAAMC,oBAAoB,GAAIlC,MAAc,IAAa;EAC9D,OAAON,SAAS,CAACyC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACpC,MAAM,KAAKA,MAAM,CAAC;AAC3D,CAAC;AAED,OAAO,MAAMqC,mBAAmB,GAAGA,CAAA,KAAc;EAC/C,OAAO3C,SAAS,CAACyC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACnB,UAAU,CAAC;AACpD,CAAC;AAED,OAAO,MAAMqB,eAAe,GAAI1D,EAAU,IAAuB;EAC/D,OAAOc,SAAS,CAAC6C,IAAI,CAACH,KAAK,IAAIA,KAAK,CAACxD,EAAE,KAAKA,EAAE,CAAC;AACjD,CAAC;AAED,OAAO,MAAM4D,wBAAwB,GAAIC,YAAoB,IAAa;EACxE,OAAOtB,SAAS,CAACgB,MAAM,CAACO,IAAI;IAAA,IAAAC,qBAAA;IAAA,OAAI,EAAAA,qBAAA,GAAAD,IAAI,CAACpB,mBAAmB,cAAAqB,qBAAA,uBAAxBA,qBAAA,CAA0B/D,EAAE,MAAK6D,YAAY;EAAA,EAAC;AAChF,CAAC;AAED,OAAO,MAAMG,oBAAoB,GAAI5C,MAAc,IAAa;EAC9D,OAAOmB,SAAS,CAACgB,MAAM,CAACO,IAAI,IAAIA,IAAI,CAAC1C,MAAM,KAAKA,MAAM,CAAC;AACzD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}