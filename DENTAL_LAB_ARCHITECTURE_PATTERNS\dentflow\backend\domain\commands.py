"""
DentFlow Domain Commands
Commands represent intentions to change the system state
"""

from dataclasses import dataclass
from typing import Optional, List
from abc import ABC


class Command(ABC):
    """Base class for all domain commands"""
    pass


@dataclass
class CreateCase(Command):
    clinic_id: str
    patient_name: str
    tooth_number: str
    service_type: str
    priority: str = "normal"
    notes: str = ""
    files: List[str] = None
    tenant_id: str = None


@dataclass
class AdvanceCase(Command):
    case_id: str
    notes: str = ""
    quality_check_passed: bool = True
    tenant_id: str = None


@dataclass
class AssignTechnician(Command):
    case_id: str
    technician_id: str
    stage: str
    tenant_id: str = None


@dataclass
class UploadFile(Command):
    case_id: str
    filename: str
    file_content: bytes
    file_type: str
    uploaded_by: str
    tenant_id: str = None


@dataclass
class UpdateCasePriority(Command):
    case_id: str
    new_priority: str
    reason: str
    updated_by: str
    tenant_id: str = None


@dataclass
class AddCaseNote(Command):
    case_id: str
    note: str
    note_type: str = "general"  # general, technical, quality
    author_id: str = None
    tenant_id: str = None


@dataclass
class SchedulePickup(Command):
    case_ids: List[str]
    pickup_date: str
    pickup_time: str
    clinic_id: str
    tenant_id: str = None


@dataclass
class GenerateInvoice(Command):
    case_ids: List[str]
    clinic_id: str
    due_date: str
    tenant_id: str = None


@dataclass
class ProcessPayment(Command):
    invoice_id: str
    payment_intent_id: str
    tenant_id: str = None