{"ast": null, "code": "'use client';\n\nimport * as ReactDOM from 'react-dom';\nconst unstable_batchedUpdates = ReactDOM.unstable_batchedUpdates;\nexport { unstable_batchedUpdates };", "map": {"version": 3, "names": ["unstable_batchedUpdates", "ReactDOM"], "sources": ["C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\node_modules\\@tanstack\\react-query\\src\\reactBatchedUpdates.ts"], "sourcesContent": ["'use client'\nimport * as ReactDOM from 'react-dom'\n\nexport const unstable_batchedUpdates = ReactDOM.unstable_batchedUpdates\n"], "mappings": ";;;AAGa,MAAAA,uBAAA,GAAAC,QAAA,CAAAD,uBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}