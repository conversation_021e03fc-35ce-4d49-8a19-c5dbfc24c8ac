# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [5.4.0](https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-transform-react-native-svg/compare/v5.3.1...v5.4.0) (2020-04-27)


### Features

* add `ForeignObject` support for react native ([#430](https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-transform-react-native-svg/issues/430)) ([1b56b85](https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-transform-react-native-svg/commit/1b56b851478803d40105ce63c70e457bd3183da6))





## [5.0.1](https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-transform-react-native-svg/compare/v5.0.0...v5.0.1) (2019-12-29)


### Bug Fixes

* fix engines in package.json ([a45d6fc](https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-transform-react-native-svg/commit/a45d6fc8b43402bec60ed4e9273f90fdc65a23a7))





# [4.2.0](https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-transform-react-native-svg/compare/v4.1.0...v4.2.0) (2019-04-11)


### Features

* add expo option ([#289](https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-transform-react-native-svg/issues/289)) ([978db3e](https://github.com/gregberge/svgr/tree/master/packages/babel-plugin-transform-react-native-svg/commit/978db3e))





# [4.0.0](https://github.com/gregberge/svgr/compare/v3.1.0...v4.0.0) (2018-11-04)


### Features

* **v4:** new architecture ([ac8b8ca](https://github.com/gregberge/svgr/commit/ac8b8ca))


### BREAKING CHANGES

* **v4:** - `template` option must now returns a Babel AST
- `@svgr/core` does not include svgo & prettier by default
