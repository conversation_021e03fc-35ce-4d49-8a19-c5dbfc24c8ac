{"ast": null, "code": "import { replaceData, noop, timeUntilStale, getAbortController } from './utils.mjs';\nimport { defaultLogger } from './logger.mjs';\nimport { notifyManager } from './notifyManager.mjs';\nimport { createRetryer, isCancelledError, canFetch } from './retryer.mjs';\nimport { Removable } from './removable.mjs';\n\n// CLASS\nclass Query extends Removable {\n  constructor(config) {\n    super();\n    this.abortSignalConsumed = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.logger = config.logger || defaultLogger;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || getDefaultState(this.options);\n    this.state = this.initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  setOptions(options) {\n    this.options = {\n      ...this.defaultOptions,\n      ...options\n    };\n    this.updateCacheTime(this.options.cacheTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options); // Set data and mark it as cached\n\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt,\n      manual: options == null ? void 0 : options.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state,\n      setStateOptions\n    });\n  }\n  cancel(options) {\n    var _this$retryer;\n    const promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({\n      silent: true\n    });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  }\n  isActive() {\n    return this.observers.some(observer => observer.options.enabled !== false);\n  }\n  isDisabled() {\n    return this.getObserversCount() > 0 && !this.isActive();\n  }\n  isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(observer => observer.getCurrentResult().isStale);\n  }\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    var _this$retryer2;\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus());\n    if (observer) {\n      observer.refetch({\n        cancelRefetch: false\n      });\n    } // Continue fetch if currently paused\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  }\n  onOnline() {\n    var _this$retryer3;\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect());\n    if (observer) {\n      observer.refetch({\n        cancelRefetch: false\n      });\n    } // Continue fetch if currently paused\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer); // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter(x => x !== observer);\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer\n      });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  }\n  fetch(options, fetchOptions) {\n    var _this$options$behavio, _context$fetchOptions;\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions != null && fetchOptions.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\"As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']\");\n      }\n    }\n    const abortController = getAbortController(); // Create query function context\n\n    const queryFnContext = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    }; // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n\n    const addSignalProperty = object => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true;\n            return abortController.signal;\n          }\n          return undefined;\n        }\n      });\n    };\n    addSignalProperty(queryFnContext); // Create fetch function\n\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\"Missing queryFn for queryKey '\" + this.options.queryHash + \"'\");\n      }\n      this.abortSignalConsumed = false;\n      return this.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    (_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch(context); // Store state in case the current fetch needs to be reverted\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (this.state.fetchStatus === 'idle' || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    }\n    const onError = error => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error\n        });\n      }\n      if (!isCancelledError(error)) {\n        var _this$cache$config$on, _this$cache$config, _this$cache$config$on2, _this$cache$config2;\n\n        // Notify cache callback\n        (_this$cache$config$on = (_this$cache$config = this.cache.config).onError) == null ? void 0 : _this$cache$config$on.call(_this$cache$config, error, this);\n        (_this$cache$config$on2 = (_this$cache$config2 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on2.call(_this$cache$config2, this.state.data, error, this);\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error);\n        }\n      }\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc();\n      }\n      this.isFetchingOptimistic = false;\n    }; // Try to fetch the data\n\n    this.retryer = createRetryer({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : abortController.abort.bind(abortController),\n      onSuccess: data => {\n        var _this$cache$config$on3, _this$cache$config3, _this$cache$config$on4, _this$cache$config4;\n        if (typeof data === 'undefined') {\n          if (process.env.NODE_ENV !== 'production') {\n            this.logger.error(\"Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: \" + this.queryHash);\n          }\n          onError(new Error(this.queryHash + \" data is undefined\"));\n          return;\n        }\n        this.setData(data); // Notify cache callback\n\n        (_this$cache$config$on3 = (_this$cache$config3 = this.cache.config).onSuccess) == null ? void 0 : _this$cache$config$on3.call(_this$cache$config3, data, this);\n        (_this$cache$config$on4 = (_this$cache$config4 = this.cache.config).onSettled) == null ? void 0 : _this$cache$config$on4.call(_this$cache$config4, data, this.state.error, this);\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc();\n        }\n        this.isFetchingOptimistic = false;\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({\n          type: 'failed',\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: () => {\n        this.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  }\n  dispatch(action) {\n    const reducer = state => {\n      var _action$meta, _action$dataUpdatedAt;\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused'\n          };\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching'\n          };\n        case 'fetch':\n          return {\n            ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n            fetchStatus: canFetch(this.options.networkMode) ? 'fetching' : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading'\n            })\n          };\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            })\n          };\n        case 'error':\n          const error = action.error;\n          if (isCancelledError(error) && error.revert && this.revertState) {\n            return {\n              ...this.revertState,\n              fetchStatus: 'idle'\n            };\n          }\n          return {\n            ...state,\n            error: error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: 'idle',\n            status: 'error'\n          };\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case 'setState':\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate(action);\n      });\n      this.cache.notify({\n        query: this,\n        type: 'updated',\n        action\n      });\n    });\n  }\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n  const hasData = typeof data !== 'undefined';\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle'\n  };\n}\nexport { Query };", "map": {"version": 3, "names": ["Query", "Removable", "constructor", "config", "abortSignalConsumed", "defaultOptions", "setOptions", "options", "observers", "cache", "logger", "defaultLogger", "query<PERSON><PERSON>", "queryHash", "initialState", "state", "getDefaultState", "scheduleGc", "meta", "updateCacheTime", "cacheTime", "optionalRemove", "length", "fetchStatus", "remove", "setData", "newData", "data", "replaceData", "dispatch", "type", "dataUpdatedAt", "updatedAt", "manual", "setState", "setStateOptions", "cancel", "_this$retryer", "promise", "retryer", "then", "noop", "catch", "Promise", "resolve", "destroy", "silent", "reset", "isActive", "some", "observer", "enabled", "isDisabled", "getObserversCount", "isStale", "isInvalidated", "getCurrentResult", "isStaleByTime", "staleTime", "timeUntilStale", "onFocus", "_this$retryer2", "find", "x", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "continue", "onOnline", "_this$retryer3", "shouldFetchOnReconnect", "addObserver", "includes", "push", "clearGcTimeout", "notify", "query", "removeObserver", "filter", "revert", "cancelRetry", "invalidate", "fetch", "fetchOptions", "_this$options$behavio", "_context$fetchOptions", "_this$retryer4", "continueRetry", "queryFn", "process", "env", "NODE_ENV", "Array", "isArray", "error", "abortController", "getAbortController", "queryFnContext", "pageParam", "undefined", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "fetchFn", "reject", "context", "behavior", "onFetch", "revertState", "fetchMeta", "_context$fetchOptions2", "onError", "isCancelledError", "_this$cache$config$on", "_this$cache$config", "_this$cache$config$on2", "_this$cache$config2", "call", "onSettled", "isFetchingOptimistic", "createRetryer", "fn", "abort", "bind", "onSuccess", "_this$cache$config$on3", "_this$cache$config3", "_this$cache$config$on4", "_this$cache$config4", "Error", "onFail", "failureCount", "onPause", "onContinue", "retry", "retry<PERSON><PERSON><PERSON>", "networkMode", "action", "reducer", "_action$meta", "_action$dataUpdatedAt", "fetchFailureCount", "fetchFailureReason", "canFetch", "status", "dataUpdateCount", "Date", "now", "errorUpdateCount", "errorUpdatedAt", "notify<PERSON><PERSON>ger", "batch", "for<PERSON>ach", "onQueryUpdate", "initialData", "hasData", "initialDataUpdatedAt"], "sources": ["C:\\GPT4_PROJECTS\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\dentflow\\frontend\\node_modules\\@tanstack\\query-core\\src\\query.ts"], "sourcesContent": ["import { getAbortController, noop, replaceData, timeUntilStale } from './utils'\nimport { defaultLogger } from './logger'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type {\n  CancelOptions,\n  FetchStatus,\n  InitialDataFunction,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n} from './types'\nimport type { QueryCache } from './queryCache'\nimport type { QueryObserver } from './queryObserver'\nimport type { Logger } from './logger'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  cache: QueryCache\n  queryKey: TQueryKey\n  queryHash: string\n  logger?: Logger\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = unknown> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: any\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal?: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n  ) => void\n}\n\nexport interface FetchOptions {\n  cancelRefetch?: boolean\n  meta?: any\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: any\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  initialState: QueryState<TData, TError>\n  revertState?: QueryState<TData, TError>\n  state: QueryState<TData, TError>\n  isFetchingOptimistic?: boolean\n\n  private cache: QueryCache\n  private logger: Logger\n  private promise?: Promise<TData>\n  private retryer?: Retryer<TData>\n  private observers: QueryObserver<any, any, any, any, any>[]\n  private defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  private abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.abortSignalConsumed = false\n    this.defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.cache = config.cache\n    this.logger = config.logger || defaultLogger\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.initialState = config.state || getDefaultState(this.options)\n    this.state = this.initialState\n    this.scheduleGc()\n  }\n\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  private setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.promise\n    this.retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some((observer) => observer.options.enabled !== false)\n  }\n\n  isDisabled(): boolean {\n    return this.getObserversCount() > 0 && !this.isActive()\n  }\n\n  isStale(): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      this.observers.some((observer) => observer.getCurrentResult().isStale)\n    )\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({ revert: true })\n          } else {\n            this.retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({ silent: true })\n      } else if (this.promise) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.retryer?.continueRetry()\n        // Return current promise if we are already fetching\n        return this.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = getAbortController()\n\n    // Create query function context\n    const queryFnContext: QueryFunctionContext<TQueryKey> = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta,\n    }\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true\n            return abortController.signal\n          }\n          return undefined\n        },\n      })\n    }\n\n    addSignalProperty(queryFnContext)\n\n    // Create fetch function\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\n          `Missing queryFn for queryKey '${this.options.queryHash}'`,\n        )\n      }\n      this.abortSignalConsumed = false\n      return this.options.queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const context: FetchContext<TQueryFnData, TError, TData, TQueryKey> = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn,\n    }\n\n    addSignalProperty(context)\n\n    this.options.behavior?.onFetch(context)\n\n    // Store state in case the current fetch needs to be reverted\n    this.revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.cache.config.onError?.(error, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      }\n      this.isFetchingOptimistic = false\n    }\n\n    // Try to fetch the data\n    this.retryer = createRetryer({\n      fn: context.fetchFn as () => TData,\n      abort: abortController?.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (typeof data === 'undefined') {\n          if (process.env.NODE_ENV !== 'production') {\n            this.logger.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        this.setData(data as TData)\n\n        // Notify cache callback\n        this.cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc()\n        }\n        this.isFetchingOptimistic = false\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n    })\n\n    this.promise = this.retryer.promise\n\n    return this.promise\n  }\n\n  private dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: action.meta ?? null,\n            fetchStatus: canFetch(this.options.networkMode)\n              ? 'fetching'\n              : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading',\n            }),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error as unknown\n\n          if (isCancelledError(error) && error.revert && this.revertState) {\n            return { ...this.revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error: error as TError,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error as TError,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate(action)\n      })\n\n      this.cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = typeof data !== 'undefined'\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle',\n  }\n}\n"], "mappings": ";;;;;;AA0IA;AAEO,MAAMA,KAAN,SAKGC,SALH,CAKa;EAiBlBC,WAAWA,CAACC,MAAD,EAA8D;IACvE;IAEA,IAAK,CAAAC,mBAAL,GAA2B,KAA3B;IACA,KAAKC,cAAL,GAAsBF,MAAM,CAACE,cAA7B;IACA,KAAKC,UAAL,CAAgBH,MAAM,CAACI,OAAvB;IACA,IAAK,CAAAC,SAAL,GAAiB,EAAjB;IACA,KAAKC,KAAL,GAAaN,MAAM,CAACM,KAApB;IACA,KAAKC,MAAL,GAAcP,MAAM,CAACO,MAAP,IAAiBC,aAA/B;IACA,KAAKC,QAAL,GAAgBT,MAAM,CAACS,QAAvB;IACA,KAAKC,SAAL,GAAiBV,MAAM,CAACU,SAAxB;IACA,IAAK,CAAAC,YAAL,GAAoBX,MAAM,CAACY,KAAP,IAAgBC,eAAe,CAAC,IAAK,CAAAT,OAAN,CAAnD;IACA,IAAK,CAAAQ,KAAL,GAAa,KAAKD,YAAlB;IACA,KAAKG,UAAL;EACD;EAEO,IAAJC,IAAIA,CAAA,EAA0B;IAChC,OAAO,KAAKX,OAAL,CAAaW,IAApB;EACD;EAEOZ,UAAUA,CAChBC,OADgB,EAEV;IACN,KAAKA,OAAL,GAAe;MAAE,GAAG,KAAKF,cAAV;MAA0B,GAAGE;KAA5C;IAEA,KAAKY,eAAL,CAAqB,IAAK,CAAAZ,OAAL,CAAaa,SAAlC;EACD;EAESC,cAAcA,CAAA,EAAG;IACzB,IAAI,CAAC,KAAKb,SAAL,CAAec,MAAhB,IAA0B,IAAK,CAAAP,KAAL,CAAWQ,WAAX,KAA2B,MAAzD,EAAiE;MAC/D,KAAKd,KAAL,CAAWe,MAAX,CAAkB,IAAlB;IACD;EACF;EAEDC,OAAOA,CACLC,OADK,EAELnB,OAFK,EAGE;IACP,MAAMoB,IAAI,GAAGC,WAAW,CAAC,KAAKb,KAAL,CAAWY,IAAZ,EAAkBD,OAAlB,EAA2B,KAAKnB,OAAhC,CAAxB,CADO;;IAIP,KAAKsB,QAAL,CAAc;MACZF,IADY;MAEZG,IAAI,EAAE,SAFM;MAGZC,aAAa,EAAExB,OAAF,IAAE,gBAAAA,OAAO,CAAEyB,SAHZ;MAIZC,MAAM,EAAE1B,OAAF,IAAE,gBAAAA,OAAO,CAAE0B;KAJnB;IAOA,OAAON,IAAP;EACD;EAEDO,QAAQA,CACNnB,KADM,EAENoB,eAFM,EAGA;IACN,KAAKN,QAAL,CAAc;MAAEC,IAAI,EAAE,UAAR;MAAoBf,KAApB;MAA2BoB;KAAzC;EACD;EAEDC,MAAMA,CAAC7B,OAAD,EAAyC;IAAA,IAAA8B,aAAA;IAC7C,MAAMC,OAAO,GAAG,KAAKA,OAArB;IACA,CAAAD,aAAA,QAAKE,OAAL,qBAAAF,aAAA,CAAcD,MAAd,CAAqB7B,OAArB;IACA,OAAO+B,OAAO,GAAGA,OAAO,CAACE,IAAR,CAAaC,IAAb,EAAmBC,KAAnB,CAAyBD,IAAzB,CAAH,GAAoCE,OAAO,CAACC,OAAR,EAAlD;EACD;EAEDC,OAAOA,CAAA,EAAS;IACd,MAAMA,OAAN;IAEA,KAAKT,MAAL,CAAY;MAAEU,MAAM,EAAE;KAAtB;EACD;EAEDC,KAAKA,CAAA,EAAS;IACZ,KAAKF,OAAL;IACA,IAAK,CAAAX,QAAL,CAAc,KAAKpB,YAAnB;EACD;EAEDkC,QAAQA,CAAA,EAAY;IAClB,OAAO,IAAK,CAAAxC,SAAL,CAAeyC,IAAf,CAAqBC,QAAD,IAAcA,QAAQ,CAAC3C,OAAT,CAAiB4C,OAAjB,KAA6B,KAA/D,CAAP;EACD;EAEDC,UAAUA,CAAA,EAAY;IACpB,OAAO,KAAKC,iBAAL,EAA2B,IAA3B,IAAgC,CAAC,KAAKL,QAAL,EAAxC;EACD;EAEDM,OAAOA,CAAA,EAAY;IACjB,OACE,KAAKvC,KAAL,CAAWwC,aAAX,IACA,CAAC,KAAKxC,KAAL,CAAWgB,aADZ,IAEA,KAAKvB,SAAL,CAAeyC,IAAf,CAAqBC,QAAD,IAAcA,QAAQ,CAACM,gBAAT,EAA4B,CAAAF,OAA9D,CAHF;EAKD;EAEDG,aAAaA,CAACC,SAAS,GAAG,CAAb,EAAyB;IACpC,OACE,KAAK3C,KAAL,CAAWwC,aAAX,IACA,CAAC,KAAKxC,KAAL,CAAWgB,aADZ,IAEA,CAAC4B,cAAc,CAAC,IAAK,CAAA5C,KAAL,CAAWgB,aAAZ,EAA2B2B,SAA3B,CAHjB;EAKD;EAEDE,OAAOA,CAAA,EAAS;IAAA,IAAAC,cAAA;IACd,MAAMX,QAAQ,GAAG,IAAK,CAAA1C,SAAL,CAAesD,IAAf,CAAqBC,CAAD,IAAOA,CAAC,CAACC,wBAAF,EAA3B,CAAjB;IAEA,IAAId,QAAJ,EAAc;MACZA,QAAQ,CAACe,OAAT,CAAiB;QAAEC,aAAa,EAAE;OAAlC;IACD,CALa;;IAQd,CAAKL,cAAA,QAAAtB,OAAL,qBAAAsB,cAAA,CAAcM,QAAd;EACD;EAEDC,QAAQA,CAAA,EAAS;IAAA,IAAAC,cAAA;IACf,MAAMnB,QAAQ,GAAG,IAAK,CAAA1C,SAAL,CAAesD,IAAf,CAAqBC,CAAD,IAAOA,CAAC,CAACO,sBAAF,EAA3B,CAAjB;IAEA,IAAIpB,QAAJ,EAAc;MACZA,QAAQ,CAACe,OAAT,CAAiB;QAAEC,aAAa,EAAE;OAAlC;IACD,CALc;;IAQf,CAAKG,cAAA,QAAA9B,OAAL,qBAAA8B,cAAA,CAAcF,QAAd;EACD;EAEDI,WAAWA,CAACrB,QAAD,EAAyD;IAClE,IAAI,CAAC,KAAK1C,SAAL,CAAegE,QAAf,CAAwBtB,QAAxB,CAAL,EAAwC;MACtC,KAAK1C,SAAL,CAAeiE,IAAf,CAAoBvB,QAApB,EADsC;;MAItC,KAAKwB,cAAL;MAEA,IAAK,CAAAjE,KAAL,CAAWkE,MAAX,CAAkB;QAAE7C,IAAI,EAAE,eAAR;QAAyB8C,KAAK,EAAE,IAAhC;QAAsC1B;OAAxD;IACD;EACF;EAED2B,cAAcA,CAAC3B,QAAD,EAAyD;IACrE,IAAI,KAAK1C,SAAL,CAAegE,QAAf,CAAwBtB,QAAxB,CAAJ,EAAuC;MACrC,KAAK1C,SAAL,GAAiB,IAAK,CAAAA,SAAL,CAAesE,MAAf,CAAuBf,CAAD,IAAOA,CAAC,KAAKb,QAAnC,CAAjB;MAEA,IAAI,CAAC,KAAK1C,SAAL,CAAec,MAApB,EAA4B;QAC1B;QACA;QACA,IAAI,KAAKiB,OAAT,EAAkB;UAChB,IAAI,KAAKnC,mBAAT,EAA8B;YAC5B,IAAK,CAAAmC,OAAL,CAAaH,MAAb,CAAoB;cAAE2C,MAAM,EAAE;aAA9B;UACD,CAFD,MAEO;YACL,IAAK,CAAAxC,OAAL,CAAayC,WAAb;UACD;QACF;QAED,KAAK/D,UAAL;MACD;MAED,IAAK,CAAAR,KAAL,CAAWkE,MAAX,CAAkB;QAAE7C,IAAI,EAAE,iBAAR;QAA2B8C,KAAK,EAAE,IAAlC;QAAwC1B;OAA1D;IACD;EACF;EAEDG,iBAAiBA,CAAA,EAAW;IAC1B,OAAO,KAAK7C,SAAL,CAAec,MAAtB;EACD;EAED2D,UAAUA,CAAA,EAAS;IACjB,IAAI,CAAC,KAAKlE,KAAL,CAAWwC,aAAhB,EAA+B;MAC7B,KAAK1B,QAAL,CAAc;QAAEC,IAAI,EAAE;OAAtB;IACD;EACF;EAEDoD,KAAKA,CACH3E,OADG,EAEH4E,YAFG,EAGa;IAAA,IAAAC,qBAAA,EAAAC,qBAAA;IAChB,IAAI,KAAKtE,KAAL,CAAWQ,WAAX,KAA2B,MAA/B,EAAuC;MACrC,IAAI,KAAKR,KAAL,CAAWgB,aAAX,IAA4BoD,YAA5B,IAA4B,QAAAA,YAAY,CAAEjB,aAA9C,EAA6D;QAC3D;QACA,KAAK9B,MAAL,CAAY;UAAEU,MAAM,EAAE;SAAtB;MACD,CAHD,MAGO,IAAI,IAAK,CAAAR,OAAT,EAAkB;QAAA,IAAAgD,cAAA;;QACvB;QACA,CAAAA,cAAA,QAAK/C,OAAL,qBAAA+C,cAAA,CAAcC,aAAd,GAFuB;;QAIvB,OAAO,KAAKjD,OAAZ;MACD;IACF,CAXe;;IAchB,IAAI/B,OAAJ,EAAa;MACX,IAAK,CAAAD,UAAL,CAAgBC,OAAhB;IACD,CAhBe;IAmBhB;;IACA,IAAI,CAAC,KAAKA,OAAL,CAAaiF,OAAlB,EAA2B;MACzB,MAAMtC,QAAQ,GAAG,IAAK,CAAA1C,SAAL,CAAesD,IAAf,CAAqBC,CAAD,IAAOA,CAAC,CAACxD,OAAF,CAAUiF,OAArC,CAAjB;MACA,IAAItC,QAAJ,EAAc;QACZ,KAAK5C,UAAL,CAAgB4C,QAAQ,CAAC3C,OAAzB;MACD;IACF;IAED,IAAIkF,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;MACzC,IAAI,CAACC,KAAK,CAACC,OAAN,CAAc,KAAKtF,OAAL,CAAaK,QAA3B,CAAL,EAA2C;QACzC,IAAK,CAAAF,MAAL,CAAYoF,KAAZ;MAGD;IACF;IAED,MAAMC,eAAe,GAAGC,kBAAkB,EAA1C,CAnCgB;;IAsChB,MAAMC,cAA+C,GAAG;MACtDrF,QAAQ,EAAE,KAAKA,QADuC;MAEtDsF,SAAS,EAAEC,SAF2C;MAGtDjF,IAAI,EAAE,IAAK,CAAAA;IAH2C,CAAxD,CAtCgB;IA6ChB;IACA;;IACA,MAAMkF,iBAAiB,GAAIC,MAAD,IAAqB;MAC7CC,MAAM,CAACC,cAAP,CAAsBF,MAAtB,EAA8B,QAA9B,EAAwC;QACtCG,UAAU,EAAE,IAD0B;QAEtCC,GAAG,EAAEA,CAAA,KAAM;UACT,IAAIV,eAAJ,EAAqB;YACnB,IAAK,CAAA3F,mBAAL,GAA2B,IAA3B;YACA,OAAO2F,eAAe,CAACW,MAAvB;UACD;UACD,OAAOP,SAAP;QACD;OARH;KADF;IAaAC,iBAAiB,CAACH,cAAD,CAAjB,CA5DgB;;IA+DhB,MAAMU,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAI,CAAC,KAAKpG,OAAL,CAAaiF,OAAlB,EAA2B;QACzB,OAAO7C,OAAO,CAACiE,MAAR,oCAC4B,KAAKrG,OAAL,CAAaM,SADzC,GAAP;MAGD;MACD,IAAK,CAAAT,mBAAL,GAA2B,KAA3B;MACA,OAAO,KAAKG,OAAL,CAAaiF,OAAb,CAAqBS,cAArB,CAAP;IACD,CARD,CA/DgB;;IA0EhB,MAAMY,OAA6D,GAAG;MACpE1B,YADoE;MAEpE5E,OAAO,EAAE,KAAKA,OAFsD;MAGpEK,QAAQ,EAAE,KAAKA,QAHqD;MAIpEG,KAAK,EAAE,KAAKA,KAJwD;MAKpE4F;KALF;IAQAP,iBAAiB,CAACS,OAAD,CAAjB;IAEA,CAAKzB,qBAAA,QAAA7E,OAAL,CAAauG,QAAb,qBAAA1B,qBAAA,CAAuB2B,OAAvB,CAA+BF,OAA/B,EApFgB;;IAuFhB,KAAKG,WAAL,GAAmB,IAAK,CAAAjG,KAAxB,CAvFgB;;IA0FhB,IACE,KAAKA,KAAL,CAAWQ,WAAX,KAA2B,MAA3B,IACA,IAAK,CAAAR,KAAL,CAAWkG,SAAX,OAAA5B,qBAAA,GAAyBwB,OAAO,CAAC1B,YAAjC,qBAAyBE,qBAAsB,CAAAnE,IAA/C,CAFF,EAGE;MAAA,IAAAgG,sBAAA;MACA,KAAKrF,QAAL,CAAc;QAAEC,IAAI,EAAE,OAAR;QAAiBZ,IAAI,EAAE,CAAAgG,sBAAA,GAAAL,OAAO,CAAC1B,YAAV,qBAAE+B,sBAAsB,CAAAhG;OAA3D;IACD;IAED,MAAMiG,OAAO,GAAIrB,KAAD,IAA0C;MACxD;MACA,IAAI,EAAEsB,gBAAgB,CAACtB,KAAD,CAAhB,IAA2BA,KAAK,CAAChD,MAAnC,CAAJ,EAAgD;QAC9C,KAAKjB,QAAL,CAAc;UACZC,IAAI,EAAE,OADM;UAEZgE,KAAK,EAAEA;SAFT;MAID;MAED,IAAI,CAACsB,gBAAgB,CAACtB,KAAD,CAArB,EAA8B;QAAA,IAAAuB,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,mBAAA;;QAC5B;QACA,CAAKH,qBAAA,IAAAC,kBAAA,QAAA7G,KAAL,CAAWN,MAAX,EAAkBgH,OAAlB,KAA4B,gBAAAE,qBAAA,CAAAI,IAAA,CAAAH,kBAAA,EAAAxB,KAA5B,EAAmC,IAAnC;QACA,CAAAyB,sBAAA,IAAAC,mBAAA,QAAK/G,KAAL,CAAWN,MAAX,EAAkBuH,SAAlB,qBAAAH,sBAAA,CAAAE,IAAA,CAAAD,mBAAA,EACE,IAAK,CAAAzG,KAAL,CAAWY,IADb,EAEEmE,KAFF,EAGE,IAHF;QAMA,IAAIL,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;UACzC,KAAKjF,MAAL,CAAYoF,KAAZ,CAAkBA,KAAlB;QACD;MACF;MAED,IAAI,CAAC,IAAK,CAAA6B,oBAAV,EAAgC;QAC9B;QACA,KAAK1G,UAAL;MACD;MACD,IAAK,CAAA0G,oBAAL,GAA4B,KAA5B;IACD,CA5BD,CAjGgB;;IAgIhB,IAAK,CAAApF,OAAL,GAAeqF,aAAa,CAAC;MAC3BC,EAAE,EAAEhB,OAAO,CAACF,OADe;MAE3BmB,KAAK,EAAE/B,eAAF,oBAAEA,eAAe,CAAE+B,KAAjB,CAAuBC,IAAvB,CAA4BhC,eAA5B,CAFoB;MAG3BiC,SAAS,EAAGrG,IAAD,IAAU;QAAA,IAAAsG,sBAAA,EAAAC,mBAAA,EAAAC,sBAAA,EAAAC,mBAAA;QACnB,IAAI,OAAOzG,IAAP,KAAgB,WAApB,EAAiC;UAC/B,IAAI8D,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;YACzC,KAAKjF,MAAL,CAAYoF,KAAZ,4IAC2I,KAAKjF,SADhJ;UAGD;UACDsG,OAAO,CAAC,IAAIkB,KAAJ,CAAa,IAAK,CAAAxH,SAAlB,wBAAD,CAAP;UACA;QACD;QAED,KAAKY,OAAL,CAAaE,IAAb,EAXmB;;QAcnB,CAAKsG,sBAAA,IAAAC,mBAAA,QAAAzH,KAAL,CAAWN,MAAX,EAAkB6H,SAAlB,KAA8B,gBAAAC,sBAAA,CAAAR,IAAA,CAAAS,mBAAA,EAAAvG,IAA9B,EAAoC,IAApC;QACA,CAAAwG,sBAAA,IAAAC,mBAAA,QAAK3H,KAAL,CAAWN,MAAX,EAAkBuH,SAAlB,qBAAAS,sBAAA,CAAAV,IAAA,CAAAW,mBAAA,EACEzG,IADF,EAEE,IAAK,CAAAZ,KAAL,CAAW+E,KAFb,EAGE,IAHF;QAMA,IAAI,CAAC,IAAK,CAAA6B,oBAAV,EAAgC;UAC9B;UACA,KAAK1G,UAAL;QACD;QACD,IAAK,CAAA0G,oBAAL,GAA4B,KAA5B;OA5ByB;MA8B3BR,OA9B2B;MA+B3BmB,MAAM,EAAEA,CAACC,YAAD,EAAezC,KAAf,KAAyB;QAC/B,KAAKjE,QAAL,CAAc;UAAEC,IAAI,EAAE,QAAR;UAAkByG,YAAlB;UAAgCzC;SAA9C;OAhCyB;MAkC3B0C,OAAO,EAAEA,CAAA,KAAM;QACb,KAAK3G,QAAL,CAAc;UAAEC,IAAI,EAAE;SAAtB;OAnCyB;MAqC3B2G,UAAU,EAAEA,CAAA,KAAM;QAChB,KAAK5G,QAAL,CAAc;UAAEC,IAAI,EAAE;SAAtB;OAtCyB;MAwC3B4G,KAAK,EAAE7B,OAAO,CAACtG,OAAR,CAAgBmI,KAxCI;MAyC3BC,UAAU,EAAE9B,OAAO,CAACtG,OAAR,CAAgBoI,UAzCD;MA0C3BC,WAAW,EAAE/B,OAAO,CAACtG,OAAR,CAAgBqI;IA1CF,CAAD,CAA5B;IA6CA,KAAKtG,OAAL,GAAe,IAAK,CAAAC,OAAL,CAAaD,OAA5B;IAEA,OAAO,KAAKA,OAAZ;EACD;EAEOT,QAAQA,CAACgH,MAAD,EAAsC;IACpD,MAAMC,OAAO,GACX/H,KADc,IAEgB;MAAA,IAAAgI,YAAA,EAAAC,qBAAA;MAC9B,QAAQH,MAAM,CAAC/G,IAAf;QACE,KAAK,QAAL;UACE,OAAO;YACL,GAAGf,KADE;YAELkI,iBAAiB,EAAEJ,MAAM,CAACN,YAFrB;YAGLW,kBAAkB,EAAEL,MAAM,CAAC/C;WAH7B;QAKF,KAAK,OAAL;UACE,OAAO;YACL,GAAG/E,KADE;YAELQ,WAAW,EAAE;WAFf;QAIF,KAAK,UAAL;UACE,OAAO;YACL,GAAGR,KADE;YAELQ,WAAW,EAAE;WAFf;QAIF,KAAK,OAAL;UACE,OAAO;YACL,GAAGR,KADE;YAELkI,iBAAiB,EAAE,CAFd;YAGLC,kBAAkB,EAAE,IAHf;YAILjC,SAAS,EAAE,CAAA8B,YAAA,GAAAF,MAAM,CAAC3H,IAAT,YAAA6H,YAAA,GAAiB,IAJrB;YAKLxH,WAAW,EAAE4H,QAAQ,CAAC,IAAK,CAAA5I,OAAL,CAAaqI,WAAd,CAAR,GACT,UADS,GAET,QAPC;YAQL,IAAI,CAAC7H,KAAK,CAACgB,aAAP,IAAwB;cAC1B+D,KAAK,EAAE,IADmB;cAE1BsD,MAAM,EAAE;aAFV;WARF;QAaF,KAAK,SAAL;UACE,OAAO;YACL,GAAGrI,KADE;YAELY,IAAI,EAAEkH,MAAM,CAAClH,IAFR;YAGL0H,eAAe,EAAEtI,KAAK,CAACsI,eAAN,GAAwB,CAHpC;YAILtH,aAAa,GAAAiH,qBAAA,GAAEH,MAAM,CAAC9G,aAAT,YAAAiH,qBAAA,GAA0BM,IAAI,CAACC,GAAL,EAJlC;YAKLzD,KAAK,EAAE,IALF;YAMLvC,aAAa,EAAE,KANV;YAOL6F,MAAM,EAAE,SAPH;YAQL,IAAI,CAACP,MAAM,CAAC5G,MAAR,IAAkB;cACpBV,WAAW,EAAE,MADO;cAEpB0H,iBAAiB,EAAE,CAFC;cAGpBC,kBAAkB,EAAE;aAHtB;WARF;QAcF,KAAK,OAAL;UACE,MAAMpD,KAAK,GAAG+C,MAAM,CAAC/C,KAArB;UAEA,IAAIsB,gBAAgB,CAACtB,KAAD,CAAhB,IAA2BA,KAAK,CAACf,MAAjC,IAA2C,IAAK,CAAAiC,WAApD,EAAiE;YAC/D,OAAO;cAAE,GAAG,KAAKA,WAAV;cAAuBzF,WAAW,EAAE;aAA3C;UACD;UAED,OAAO;YACL,GAAGR,KADE;YAEL+E,KAAK,EAAEA,KAFF;YAGL0D,gBAAgB,EAAEzI,KAAK,CAACyI,gBAAN,GAAyB,CAHtC;YAILC,cAAc,EAAEH,IAAI,CAACC,GAAL,EAJX;YAKLN,iBAAiB,EAAElI,KAAK,CAACkI,iBAAN,GAA0B,CALxC;YAMLC,kBAAkB,EAAEpD,KANf;YAOLvE,WAAW,EAAE,MAPR;YAQL6H,MAAM,EAAE;WARV;QAUF,KAAK,YAAL;UACE,OAAO;YACL,GAAGrI,KADE;YAELwC,aAAa,EAAE;WAFjB;QAIF,KAAK,UAAL;UACE,OAAO;YACL,GAAGxC,KADE;YAEL,GAAG8H,MAAM,CAAC9H;WAFZ;MArEJ;KAHF;IA+EA,KAAKA,KAAL,GAAa+H,OAAO,CAAC,KAAK/H,KAAN,CAApB;IAEA2I,aAAa,CAACC,KAAd,CAAoB,MAAM;MACxB,KAAKnJ,SAAL,CAAeoJ,OAAf,CAAwB1G,QAAD,IAAc;QACnCA,QAAQ,CAAC2G,aAAT,CAAuBhB,MAAvB;OADF;MAIA,IAAK,CAAApI,KAAL,CAAWkE,MAAX,CAAkB;QAAEC,KAAK,EAAE,IAAT;QAAe9C,IAAI,EAAE,SAArB;QAAgC+G;OAAlD;KALF;EAOD;AAnciB;AAscpB,SAAS7H,eAATA,CAMET,OANF,EAO6B;EAC3B,MAAMoB,IAAI,GACR,OAAOpB,OAAO,CAACuJ,WAAf,KAA+B,UAA/B,GACKvJ,OAAO,CAACuJ,WAAT,EADJ,GAEIvJ,OAAO,CAACuJ,WAHd;EAKA,MAAMC,OAAO,GAAG,OAAOpI,IAAP,KAAgB,WAAhC;EAEA,MAAMqI,oBAAoB,GAAGD,OAAO,GAChC,OAAOxJ,OAAO,CAACyJ,oBAAf,KAAwC,UAAxC,GACGzJ,OAAO,CAACyJ,oBAAT,EADF,GAEEzJ,OAAO,CAACyJ,oBAHsB,GAIhC,CAJJ;EAMA,OAAO;IACLrI,IADK;IAEL0H,eAAe,EAAE,CAFZ;IAGLtH,aAAa,EAAEgI,OAAO,GAAGC,oBAAH,IAAG,OAAAA,oBAAH,GAA2BV,IAAI,CAACC,GAAL,EAA3B,GAAwC,CAHzD;IAILzD,KAAK,EAAE,IAJF;IAKL0D,gBAAgB,EAAE,CALb;IAMLC,cAAc,EAAE,CANX;IAOLR,iBAAiB,EAAE,CAPd;IAQLC,kBAAkB,EAAE,IARf;IASLjC,SAAS,EAAE,IATN;IAUL1D,aAAa,EAAE,KAVV;IAWL6F,MAAM,EAAEW,OAAO,GAAG,SAAH,GAAe,SAXzB;IAYLxI,WAAW,EAAE;GAZf;AAcD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}