{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M7 20h10v1H7zm0-2.48V18h10v-.48c-1.47-.99-3.22-1.52-5-1.52s-3.53.53-5 1.52\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"1\",\n  opacity: \".3\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M7 3h10v1H7z\",\n  opacity: \".3\"\n}, \"2\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M17 1H7c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2m0 20H7v-1h10zm0-3H7v-.48c1.47-.99 3.22-1.52 5-1.52s3.53.53 5 1.52zm0-2.79c-1.5-.77-3.2-1.21-5-1.21s-3.5.44-5 1.21V6h10zM17 4H7V3h10z\"\n}, \"3\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 13c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3m0-4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1\"\n}, \"4\")], 'RememberMeTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity", "cx", "cy", "r"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/node_modules/@mui/icons-material/esm/RememberMeTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M7 20h10v1H7zm0-2.48V18h10v-.48c-1.47-.99-3.22-1.52-5-1.52s-3.53.53-5 1.52\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"1\",\n  opacity: \".3\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M7 3h10v1H7z\",\n  opacity: \".3\"\n}, \"2\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M17 1H7c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-2-2-2m0 20H7v-1h10zm0-3H7v-.48c1.47-.99 3.22-1.52 5-1.52s3.53.53 5 1.52zm0-2.79c-1.5-.77-3.2-1.21-5-1.21s-3.5.44-5 1.21V6h10zM17 4H7V3h10z\"\n}, \"3\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 13c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3m0-4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1\"\n}, \"4\")], 'RememberMeTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,4EAA4E;EAC/EC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,QAAQ,EAAE;EACnCG,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE,GAAG;EACNH,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE,cAAc;EACjBC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}