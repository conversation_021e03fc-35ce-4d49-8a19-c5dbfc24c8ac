"""
Django Admin for Billing App
Financial management and invoicing for DentFlow
"""

from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Sum
from django.urls import reverse
from apps.billing.models import Invoice, InvoiceItem, Payment, PriceList, ServicePrice


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    """Admin interface for Invoices"""
    list_display = [
        'invoice_number', 'clinic', 'tenant', 'status_display', 
        'total_amount', 'due_date', 'created_at', 'payment_status'
    ]
    list_filter = ['status', 'due_date', 'created_at', 'tenant']
    search_fields = ['invoice_number', 'clinic__name', 'notes']
    readonly_fields = ['invoice_number', 'total_amount', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Invoice Information', {
            'fields': ('invoice_number', 'tenant', 'clinic', 'status')
        }),
        ('Financial Details', {
            'fields': ('subtotal', 'tax_amount', 'total_amount', 'currency')
        }),
        ('Dates', {
            'fields': ('due_date', 'created_at', 'updated_at')
        }),
        ('Additional Information', {
            'fields': ('notes', 'payment_terms'),
            'classes': ('collapse',)
        }),
    )
    
    inlines = ['InvoiceItemInline']
    
    def status_display(self, obj):
        """Display invoice status with color coding"""
        colors = {
            'draft': '#666',
            'sent': '#2196f3',
            'paid': '#4caf50',
            'overdue': '#f44336',
            'cancelled': '#757575'
        }
        color = colors.get(obj.status, '#666')
        return format_html(
            '<span style="background: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.status.upper()
        )
    status_display.short_description = 'Status'
    
    def payment_status(self, obj):
        """Show payment status with amount paid"""
        payments = Payment.objects.filter(invoice=obj)
        total_paid = payments.aggregate(Sum('amount'))['amount__sum'] or 0
        
        if total_paid >= obj.total_amount:
            return format_html('<span style="color: #4caf50;">✅ Paid ({})</span>', 
                             total_paid)
        elif total_paid > 0:
            return format_html('<span style="color: #ff9800;">⚠️ Partial ({})</span>', 
                             total_paid)
        else:
            return format_html('<span style="color: #f44336;">❌ Unpaid</span>')
    payment_status.short_description = 'Payment Status'
    
    actions = ['mark_as_sent', 'mark_as_paid', 'mark_as_overdue']
    
    def mark_as_sent(self, request, queryset):
        """Mark selected invoices as sent"""
        updated = queryset.update(status='sent')
        self.message_user(request, f'{updated} invoices marked as sent.')
    mark_as_sent.short_description = 'Mark as sent'
    
    def mark_as_paid(self, request, queryset):
        """Mark selected invoices as paid"""
        updated = queryset.update(status='paid')
        self.message_user(request, f'{updated} invoices marked as paid.')
    mark_as_paid.short_description = 'Mark as paid'
    
    def mark_as_overdue(self, request, queryset):
        """Mark selected invoices as overdue"""
        updated = queryset.update(status='overdue')
        self.message_user(request, f'{updated} invoices marked as overdue.')
    mark_as_overdue.short_description = 'Mark as overdue'


class InvoiceItemInline(admin.TabularInline):
    """Inline for invoice items"""
    model = InvoiceItem
    extra = 1
    fields = ['case', 'service_type', 'description', 'quantity', 'unit_price', 'total_price']
    readonly_fields = ['total_price']


@admin.register(InvoiceItem)
class InvoiceItemAdmin(admin.ModelAdmin):
    """Admin interface for Invoice Items"""
    list_display = [
        'invoice', 'case', 'service_type', 'quantity', 
        'unit_price', 'total_price', 'description'
    ]
    list_filter = ['service_type', 'invoice__status']
    search_fields = ['invoice__invoice_number', 'case__id', 'description']
    readonly_fields = ['total_price']
    
    def get_queryset(self, request):
        """Optimize queries"""
        return super().get_queryset(request).select_related('invoice', 'case')


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    """Admin interface for Payments"""
    list_display = [
        'payment_id', 'invoice', 'amount', 'payment_method', 
        'status_display', 'payment_date', 'created_at'
    ]
    list_filter = ['payment_method', 'status', 'payment_date']
    search_fields = ['payment_id', 'invoice__invoice_number', 'reference_number']
    readonly_fields = ['payment_id', 'created_at', 'updated_at']
    date_hierarchy = 'payment_date'
    
    fieldsets = (
        ('Payment Information', {
            'fields': ('payment_id', 'invoice', 'amount', 'currency')
        }),
        ('Payment Details', {
            'fields': ('payment_method', 'status', 'payment_date')
        }),
        ('Transaction Details', {
            'fields': ('reference_number', 'processor_response'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def status_display(self, obj):
        """Display payment status with color coding"""
        colors = {
            'pending': '#ff9800',
            'completed': '#4caf50',
            'failed': '#f44336',
            'refunded': '#9c27b0'
        }
        color = colors.get(obj.status, '#666')
        return format_html(
            '<span style="background: {}; color: white; padding: 2px 8px; '
            'border-radius: 12px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.status.upper()
        )
    status_display.short_description = 'Status'

@admin.register(PriceList)
class PriceListAdmin(admin.ModelAdmin):
    """Admin interface for Price Lists"""
    list_display = [
        'name', 'tenant', 'is_active', 'currency', 'service_count', 
        'effective_date', 'created_at'
    ]
    list_filter = ['is_active', 'currency', 'effective_date', 'tenant']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Price List Information', {
            'fields': ('name', 'tenant', 'description', 'is_active')
        }),
        ('Financial Settings', {
            'fields': ('currency', 'effective_date')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    inlines = ['ServicePriceInline']
    
    def service_count(self, obj):
        """Show number of services in this price list"""
        count = obj.serviceprices.count()
        if count > 0:
            url = reverse('admin:billing_serviceprice_changelist') + f'?price_list__id__exact={obj.id}'
            return format_html('<a href="{}">{} services</a>', url, count)
        return '0 services'
    service_count.short_description = 'Services'


class ServicePriceInline(admin.TabularInline):
    """Inline for service prices"""
    model = ServicePrice
    extra = 1
    fields = ['service_type', 'description', 'base_price', 'is_active']


@admin.register(ServicePrice)
class ServicePriceAdmin(admin.ModelAdmin):
    """Admin interface for Service Prices"""
    list_display = [
        'service_type', 'price_list', 'base_price', 'currency_display', 
        'is_active', 'updated_at'
    ]
    list_filter = ['service_type', 'is_active', 'price_list__currency']
    search_fields = ['service_type', 'description', 'price_list__name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Service Information', {
            'fields': ('price_list', 'service_type', 'description')
        }),
        ('Pricing', {
            'fields': ('base_price', 'is_active')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def currency_display(self, obj):
        """Show currency from price list"""
        return obj.price_list.currency
    currency_display.short_description = 'Currency'
    
    actions = ['activate_prices', 'deactivate_prices']
    
    def activate_prices(self, request, queryset):
        """Activate selected service prices"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} service prices activated.')
    activate_prices.short_description = 'Activate selected prices'
    
    def deactivate_prices(self, request, queryset):
        """Deactivate selected service prices"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} service prices deactivated.')
    deactivate_prices.short_description = 'Deactivate selected prices'


# Custom admin actions for financial reports
def generate_revenue_report(modeladmin, request, queryset):
    """Generate revenue report for selected invoices"""
    total = queryset.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    modeladmin.message_user(request, f'Total revenue: ${total:,.2f}')
generate_revenue_report.short_description = 'Generate revenue report'

# Add custom action to InvoiceAdmin
InvoiceAdmin.actions.append(generate_revenue_report)