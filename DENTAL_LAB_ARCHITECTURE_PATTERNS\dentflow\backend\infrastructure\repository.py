"""
Case Repository Implementation - Following Repository Pattern
Abstracts database operations and maintains aggregate consistency
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Set
from django.db import transaction

from domain.model import Case as DomainCase
from apps.cases.models import Case as DjangoCase


class AbstractCaseRepository(ABC):
    """Abstract repository for Case aggregate"""
    
    def __init__(self):
        self.seen: Set[DomainCase] = set()
    
    def add(self, case: DomainCase):
        """Add a case to be tracked by repository"""
        self._add(case)
        self.seen.add(case)
    
    def get(self, case_id: str) -> Optional[DomainCase]:
        """Get case by ID"""
        case = self._get(case_id)
        if case:
            self.seen.add(case)
        return case
    
    @abstractmethod
    def _add(self, case: DomainCase):
        pass
    
    @abstractmethod
    def _get(self, case_id: str) -> Optional[DomainCase]:
        pass
    
    @abstractmethod
    def list_by_status(self, status: str, tenant_id: str) -> List[DomainCase]:
        pass


class DjangoCaseRepository(AbstractCaseRepository):
    """Django ORM implementation of Case repository"""
    
    def _add(self, case: DomainCase):
        """Add case to database"""
        django_case = DjangoCase.from_domain(case)
        django_case.save()
    
    def _get(self, case_id: str) -> Optional[DomainCase]:
        """Get case by ID from database"""
        try:
            django_case = DjangoCase.objects.get(id=case_id)
            return django_case.to_domain()
        except DjangoCase.DoesNotExist:
            return None
    
    def list_by_status(self, status: str, tenant_id: str) -> List[DomainCase]:
        """List cases by status for a tenant"""
        django_cases = DjangoCase.objects.filter(
            status=status, 
            tenant_id=tenant_id,
            deleted_at__isnull=True
        ).order_by('created_at')
        
        return [case.to_domain() for case in django_cases]
    
    def list_overdue(self, tenant_id: str) -> List[DomainCase]:
        """List overdue cases for a tenant"""
        from django.utils import timezone
        
        django_cases = DjangoCase.objects.filter(
            tenant_id=tenant_id,
            due_date__lt=timezone.now(),
            status__in=['received', 'design', 'milling', 'sintering', 'qc'],
            deleted_at__isnull=True
        ).order_by('due_date')
        
        return [case.to_domain() for case in django_cases]
    
    def update(self, case: DomainCase):
        """Update existing case in database"""
        try:
            django_case = DjangoCase.objects.get(id=case.case_id.value)
            django_case.update_from_domain(case)
            django_case.save()
        except DjangoCase.DoesNotExist:
            # Case doesn't exist, add it
            self._add(case)


class FakeCaseRepository(AbstractCaseRepository):
    """In-memory fake repository for testing"""
    
    def __init__(self):
        super().__init__()
        self._cases = {}
    
    def _add(self, case: DomainCase):
        self._cases[case.case_id.value] = case
    
    def _get(self, case_id: str) -> Optional[DomainCase]:
        return self._cases.get(case_id)
    
    def list_by_status(self, status: str, tenant_id: str) -> List[DomainCase]:
        return [
            case for case in self._cases.values()
            if case.status.value == status and case.tenant_id == tenant_id
        ]
    
    def list_overdue(self, tenant_id: str) -> List[DomainCase]:
        from datetime import datetime
        now = datetime.utcnow()
        
        return [
            case for case in self._cases.values()
            if (case.tenant_id == tenant_id and 
                case.due_date and 
                case.due_date < now and
                case.status.value in ['received', 'design', 'milling', 'sintering', 'qc'])
        ]