"""
DentFlow Message Bus - The central communication hub
Connects all domain events and commands throughout the system
"""

from typing import Dict, List, Callable, Union, Any, Type
from dataclasses import dataclass, field
import logging
from abc import ABC, abstractmethod

from . import events, commands


logger = logging.getLogger(__name__)


Message = Union[events.Event, commands.Command]
Handler = Callable[[Message], None]


class MessageBus:
    """
    The MessageBus is the central nervous system of DentFlow.
    It coordinates all communication between layers and ensures
    proper event sourcing and command handling.
    """
    
    def __init__(self,
                 event_handlers: Dict[Type[events.Event], List[Handler]],
                 command_handlers: Dict[Type[commands.Command], Handler],
                 uow):
        self.event_handlers = event_handlers
        self.command_handlers = command_handlers
        self.uow = uow
        self.queue: List[Message] = []
    
    def handle(self, message: Message) -> Any:
        """
        Main entry point for all messages.
        Handles both commands and events with proper error handling.
        """
        logger.info(f"Handling message: {type(message).__name__}")
        
        self.queue = [message]
        results = []
        
        while self.queue:
            message = self.queue.pop(0)
            
            if isinstance(message, events.Event):
                self._handle_event(message)
            elif isinstance(message, commands.Command):
                result = self._handle_command(message)
                results.append(result)
            else:
                raise ValueError(f"Unknown message type: {type(message)}")
        
        return results[0] if len(results) == 1 else results    
    def _handle_event(self, event: events.Event):
        """Handle domain events - can have multiple handlers"""
        handlers = self.event_handlers.get(type(event), [])
        
        for handler in handlers:
            try:
                logger.debug(f"Handling event {type(event).__name__} with {handler.__name__}")
                handler(event)
                
                # Collect any new events generated during handling
                self._collect_new_events()
                
            except Exception as e:
                logger.error(f"Error handling event {type(event).__name__}: {e}")
                # Continue with other handlers even if one fails
                continue
    
    def _handle_command(self, command: commands.Command) -> Any:
        """Handle commands - should have exactly one handler"""
        handler = self.command_handlers.get(type(command))
        
        if not handler:
            raise ValueError(f"No handler found for command {type(command).__name__}")
        
        try:
            logger.debug(f"Handling command {type(command).__name__}")
            result = handler(command)
            
            # Collect any events generated during command handling
            self._collect_new_events()
            
            return result
            
        except Exception as e:
            logger.error(f"Error handling command {type(command).__name__}: {e}")
            raise
    
    def _collect_new_events(self):
        """Collect new events from the Unit of Work"""
        if hasattr(self.uow, 'collect_new_events'):
            new_events = self.uow.collect_new_events()
            self.queue.extend(new_events)


class TenantAwareMessageBus(MessageBus):
    """
    Extension of MessageBus that ensures tenant isolation
    for all messages flowing through the system.
    """
    
    def __init__(self, *args, tenant_id: str, **kwargs):
        super().__init__(*args, **kwargs)
        self.tenant_id = tenant_id
    
    def handle(self, message: Message) -> Any:
        """Override to ensure tenant context is set"""
        
        # Ensure message has tenant context
        if hasattr(message, 'tenant_id') and message.tenant_id != self.tenant_id:
            raise ValueError(
                f"Message tenant_id {message.tenant_id} doesn't match "
                f"bus tenant_id {self.tenant_id}"
            )
        
        # Set tenant context for the duration of message handling
        with self.uow.tenant_context(self.tenant_id):
            return super().handle(message)