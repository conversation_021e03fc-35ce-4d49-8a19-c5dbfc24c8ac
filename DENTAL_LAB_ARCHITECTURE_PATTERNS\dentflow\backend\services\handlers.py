"""
DentFlow Service Layer Handlers
These handlers orchestrate domain operations and side effects
All dependencies are injected to maintain clean architecture
"""

from typing import Optional, List
import logging
from datetime import datetime

from domain import model, events, commands
from domain.model import Case, CaseId, ToothNumber, Priority, WorkflowStage
from services import unit_of_work


logger = logging.getLogger(__name__)


# Command Handlers - these modify state
def create_case(cmd: commands.CreateCase, uow: unit_of_work.AbstractUnitOfWork) -> str:
    """
    Create a new case and assign initial workflow
    Returns: case_id
    """
    with uow:
        # Create case aggregate
        case_id = CaseId.generate()
        tooth_number = ToothNumber(cmd.tooth_number)
        priority = Priority(cmd.priority)
        
        case = Case(
            case_id=case_id,
            clinic_id=cmd.clinic_id,
            patient_name=cmd.patient_name,
            tooth_number=tooth_number,
            service_type=cmd.service_type,
            tenant_id=cmd.tenant_id,
            priority=priority
        )
        
        # Assign workflow based on service type
        workflow = _get_workflow_for_service(cmd.service_type)
        case.assign_workflow(workflow)
        
        # Add files if provided
        if cmd.files:
            for file_id in cmd.files:
                case.files.append(file_id)
        
        uow.cases.add(case)
        uow.commit()
        
        logger.info(f"Created case {case_id.value} for clinic {cmd.clinic_id}")
        return case_id.value


def advance_case(cmd: commands.AdvanceCase, uow: unit_of_work.AbstractUnitOfWork) -> None:
    """Advance case to next workflow stage"""
    with uow:
        case = uow.cases.get(cmd.case_id)
        if not case:
            raise ValueError(f"Case {cmd.case_id} not found")
        
        # Business rule: must have technician assigned to advance
        if not case.assigned_technician_id:
            raise ValueError("Cannot advance case without assigned technician")
        
        case.advance_to_next_stage(
            technician_id=case.assigned_technician_id,
            notes=cmd.notes
        )
        
        uow.commit()
        logger.info(f"Advanced case {cmd.case_id} to {case.status.value}")


def assign_technician(cmd: commands.AssignTechnician, uow: unit_of_work.AbstractUnitOfWork) -> None:
    """Assign technician to case stage"""
    with uow:
        case = uow.cases.get(cmd.case_id)
        if not case:
            raise ValueError(f"Case {cmd.case_id} not found")
        
        # Verify technician exists and has required skills
        technician = uow.technicians.get(cmd.technician_id)
        if not technician:
            raise ValueError(f"Technician {cmd.technician_id} not found")
        
        # Business rule: technician must have skills for current stage
        if not _technician_can_handle_stage(technician, cmd.stage):
            raise ValueError(f"Technician {cmd.technician_id} cannot handle stage {cmd.stage}")
        
        case.assign_technician(cmd.technician_id, "system")  # TODO: track who assigned
        
        uow.commit()
        logger.info(f"Assigned technician {cmd.technician_id} to case {cmd.case_id}")


def upload_file(cmd: commands.UploadFile, file_storage, uow: unit_of_work.AbstractUnitOfWork) -> str:
    """Upload file and associate with case"""
    with uow:
        case = uow.cases.get(cmd.case_id)
        if not case:
            raise ValueError(f"Case {cmd.case_id} not found")
        
        # Store file using file storage service
        file_id = file_storage.store(
            content=cmd.file_content,
            filename=cmd.filename,
            content_type=cmd.file_type
        )
        
        # Add file to case
        case.upload_file(
            file_id=file_id,
            filename=cmd.filename,
            file_type=cmd.file_type,
            size_bytes=len(cmd.file_content),
            uploaded_by=cmd.uploaded_by
        )
        
        uow.commit()
        logger.info(f"Uploaded file {cmd.filename} to case {cmd.case_id}")
        return file_id


# Event Handlers - these handle side effects
def send_case_confirmation_email(event: events.CaseCreated, email_service):
    """Send confirmation email when case is created"""
    try:
        email_service.send_case_confirmation(
            clinic_email=event.clinic_id,  # TODO: resolve clinic email
            case_id=event.case_id,
            patient_name=event.patient_name,
            service_type=event.service_type
        )
        logger.info(f"Sent confirmation email for case {event.case_id}")
    except Exception as e:
        logger.error(f"Failed to send confirmation email for case {event.case_id}: {e}")


def auto_assign_technician(event: events.CaseCreated, uow: unit_of_work.AbstractUnitOfWork):
    """Auto-assign technician based on workflow rules"""
    with uow:
        case = uow.cases.get(event.case_id)
        if not case or not case.workflow_stages:
            return
        
        first_stage = case.workflow_stages[0]
        if first_stage.auto_assign:
            # Find available technician for this stage
            technician_id = _find_available_technician(first_stage.department, event.tenant_id)
            if technician_id:
                case.assign_technician(technician_id, "auto_assignment")
                uow.commit()
                logger.info(f"Auto-assigned technician {technician_id} to case {event.case_id}")


def notify_status_change(event: events.CaseStatusChanged, email_service):
    """Notify relevant parties of status changes"""
    try:
        email_service.send_status_change_notification(
            case_id=event.case_id,
            from_status=event.from_status,
            to_status=event.to_status,
            tenant_id=event.tenant_id
        )
        logger.info(f"Sent status change notification for case {event.case_id}")
    except Exception as e:
        logger.error(f"Failed to send status notification for case {event.case_id}: {e}")


def update_sla_tracking(event: events.CaseStatusChanged, uow: unit_of_work.AbstractUnitOfWork):
    """Update SLA tracking when case status changes"""
    # Implementation for SLA tracking
    logger.info(f"Updated SLA tracking for case {event.case_id}")


# Helper Functions
def _get_workflow_for_service(service_type: str) -> List[WorkflowStage]:
    """Get workflow stages for a service type"""
    workflows = {
        'crown': [
            WorkflowStage(
                name='Design',
                department='CAD',
                estimated_duration_minutes=45,
                auto_assign=True
            ),
            WorkflowStage(
                name='Milling', 
                department='CAM',
                estimated_duration_minutes=15,
                auto_assign=True
            ),
            WorkflowStage(
                name='Sintering',
                department='Furnace', 
                estimated_duration_minutes=480,
                auto_assign=False,
                machine_required='furnace_3'
            ),
        ],
        'bridge': [
            WorkflowStage(
                name='Design',
                department='CAD',
                estimated_duration_minutes=90,
                auto_assign=True
            ),
            WorkflowStage(
                name='Milling',
                department='CAM', 
                estimated_duration_minutes=30,
                auto_assign=True
            ),
            WorkflowStage(
                name='Sintering',
                department='Furnace',
                estimated_duration_minutes=480,
                auto_assign=False,
                machine_required='furnace_3'
            ),
        ],
        'denture': [
            WorkflowStage(
                name='Design',
                department='CAD',
                estimated_duration_minutes=120,
                auto_assign=True
            ),
            WorkflowStage(
                name='3D_Print',
                department='Printing',
                estimated_duration_minutes=240,
                auto_assign=True
            ),
        ]
    }
    
    return workflows.get(service_type, workflows['crown'])


def _find_available_technician(department: str, tenant_id: str) -> Optional[str]:
    """Find available technician for a department"""
    # This would integrate with technician scheduling system
    # For now, return a mock technician ID
    mock_technicians = {
        'CAD': 'tech_001',
        'CAM': 'tech_002', 
        'Furnace': 'tech_003',
        'Printing': 'tech_004'
    }
    
    return mock_technicians.get(department)


def _technician_can_handle_stage(technician, stage: str) -> bool:
    """Check if technician has skills for a stage"""
    # This would check technician skills/certifications
    # For now, return True
    return True