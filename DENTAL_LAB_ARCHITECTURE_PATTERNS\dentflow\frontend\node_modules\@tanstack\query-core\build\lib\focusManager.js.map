{"version": 3, "file": "focusManager.js", "sources": ["../../src/focusManager.ts"], "sourcesContent": ["import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void,\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable {\n  private focused?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onFocus) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibillitychange and focus\n        window.addEventListener('visibilitychange', listener, false)\n        window.addEventListener('focus', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n          window.removeEventListener('focus', listener)\n        }\n      }\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((focused) => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    const changed = this.focused !== focused\n    if (changed) {\n      this.focused = focused\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.focused === 'boolean') {\n      return this.focused\n    }\n\n    // document global can be unavailable in react native\n    if (typeof document === 'undefined') {\n      return true\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(\n      document.visibilityState,\n    )\n  }\n}\n\nexport const focusManager = new FocusManager()\n"], "names": ["FocusManager", "Subscribable", "constructor", "setup", "onFocus", "isServer", "window", "addEventListener", "listener", "removeEventListener", "onSubscribe", "cleanup", "setEventListener", "onUnsubscribe", "hasListeners", "undefined", "focused", "setFocused", "changed", "listeners", "for<PERSON>ach", "isFocused", "document", "includes", "visibilityState", "focusManager"], "mappings": ";;;;;;;AAOO,MAAMA,YAAN,SAA2BC,yBAA3B,CAAwC;AAM7CC,EAAAA,WAAW,GAAG;AACZ,IAAA,KAAA,EAAA,CAAA;;IACA,IAAKC,CAAAA,KAAL,GAAcC,OAAD,IAAa;AACxB;AACA;AACA,MAAA,IAAI,CAACC,cAAD,IAAaC,MAAM,CAACC,gBAAxB,EAA0C;AACxC,QAAA,MAAMC,QAAQ,GAAG,MAAMJ,OAAO,EAA9B,CADwC;;;AAGxCE,QAAAA,MAAM,CAACC,gBAAP,CAAwB,kBAAxB,EAA4CC,QAA5C,EAAsD,KAAtD,CAAA,CAAA;AACAF,QAAAA,MAAM,CAACC,gBAAP,CAAwB,OAAxB,EAAiCC,QAAjC,EAA2C,KAA3C,CAAA,CAAA;AAEA,QAAA,OAAO,MAAM;AACX;AACAF,UAAAA,MAAM,CAACG,mBAAP,CAA2B,kBAA3B,EAA+CD,QAA/C,CAAA,CAAA;AACAF,UAAAA,MAAM,CAACG,mBAAP,CAA2B,OAA3B,EAAoCD,QAApC,CAAA,CAAA;SAHF,CAAA;AAKD,OAAA;;AACD,MAAA,OAAA;KAfF,CAAA;AAiBD,GAAA;;AAESE,EAAAA,WAAW,GAAS;IAC5B,IAAI,CAAC,IAAKC,CAAAA,OAAV,EAAmB;MACjB,IAAKC,CAAAA,gBAAL,CAAsB,IAAA,CAAKT,KAA3B,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAESU,EAAAA,aAAa,GAAG;AACxB,IAAA,IAAI,CAAC,IAAA,CAAKC,YAAL,EAAL,EAA0B;AAAA,MAAA,IAAA,aAAA,CAAA;;AACxB,MAAA,CAAA,aAAA,GAAA,IAAA,CAAKH,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;MACA,IAAKA,CAAAA,OAAL,GAAeI,SAAf,CAAA;AACD,KAAA;AACF,GAAA;;EAEDH,gBAAgB,CAACT,KAAD,EAAuB;AAAA,IAAA,IAAA,cAAA,CAAA;;IACrC,IAAKA,CAAAA,KAAL,GAAaA,KAAb,CAAA;AACA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAKQ,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AACA,IAAA,IAAA,CAAKA,OAAL,GAAeR,KAAK,CAAEa,OAAD,IAAa;AAChC,MAAA,IAAI,OAAOA,OAAP,KAAmB,SAAvB,EAAkC;QAChC,IAAKC,CAAAA,UAAL,CAAgBD,OAAhB,CAAA,CAAA;AACD,OAFD,MAEO;AACL,QAAA,IAAA,CAAKZ,OAAL,EAAA,CAAA;AACD,OAAA;AACF,KANmB,CAApB,CAAA;AAOD,GAAA;;EAEDa,UAAU,CAACD,OAAD,EAA0B;AAClC,IAAA,MAAME,OAAO,GAAG,IAAKF,CAAAA,OAAL,KAAiBA,OAAjC,CAAA;;AACA,IAAA,IAAIE,OAAJ,EAAa;MACX,IAAKF,CAAAA,OAAL,GAAeA,OAAf,CAAA;AACA,MAAA,IAAA,CAAKZ,OAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDA,EAAAA,OAAO,GAAS;AACd,IAAA,IAAA,CAAKe,SAAL,CAAeC,OAAf,CAAuB,CAAC;AAAEZ,MAAAA,QAAAA;AAAF,KAAD,KAAkB;MACvCA,QAAQ,EAAA,CAAA;KADV,CAAA,CAAA;AAGD,GAAA;;AAEDa,EAAAA,SAAS,GAAY;AACnB,IAAA,IAAI,OAAO,IAAA,CAAKL,OAAZ,KAAwB,SAA5B,EAAuC;AACrC,MAAA,OAAO,KAAKA,OAAZ,CAAA;AACD,KAHkB;;;AAMnB,IAAA,IAAI,OAAOM,QAAP,KAAoB,WAAxB,EAAqC;AACnC,MAAA,OAAO,IAAP,CAAA;AACD,KAAA;;AAED,IAAA,OAAO,CAACP,SAAD,EAAY,SAAZ,EAAuB,WAAvB,CAAoCQ,CAAAA,QAApC,CACLD,QAAQ,CAACE,eADJ,CAAP,CAAA;AAGD,GAAA;;AA/E4C,CAAA;AAkFlCC,MAAAA,YAAY,GAAG,IAAIzB,YAAJ;;;;;"}