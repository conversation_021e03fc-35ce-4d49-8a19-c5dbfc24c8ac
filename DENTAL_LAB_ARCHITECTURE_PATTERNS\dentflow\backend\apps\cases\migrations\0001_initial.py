# Generated by Django 4.2.7

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Case',
            fields=[
                ('id', models.CharField(default=uuid.uuid4, max_length=50, primary_key=True, serialize=False)),
                ('tenant_id', models.Char<PERSON>ield(max_length=50)),
                ('clinic_id', models.CharField(max_length=50)),
                ('patient_name', models.Char<PERSON>ield(max_length=200)),
                ('tooth_number', models.CharField(max_length=10)),
                ('service_type', models.Char<PERSON>ield(max_length=100)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('urgent', 'Urgent'), ('stat', 'STAT')], default='normal', max_length=20)),
                ('status', models.Char<PERSON>ield(choices=[('received', 'Received'), ('design', 'Design'), ('milling', 'Milling'), ('sintering', 'Sintering'), ('qc', 'Quality Control'), ('shipped', 'Shipped'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled')], default='received', max_length=50)),
                ('current_stage_index', models.IntegerField(default=0)),
                ('workflow_stages', models.JSONField(default=list)),
                ('assigned_technician_id', models.CharField(blank=True, max_length=50, null=True)),
                ('due_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('notes', models.JSONField(default=list)),
                ('files', models.JSONField(default=list)),
            ],
            options={
                'db_table': 'cases',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['tenant_id'], name='cases_tenant_idx'), models.Index(fields=['status'], name='cases_status_idx'), models.Index(fields=['priority'], name='cases_priority_idx'), models.Index(fields=['due_date'], name='cases_due_date_idx')],
            },
        ),
        migrations.CreateModel(
            name='CaseEvent',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('case_id', models.CharField(max_length=50)),
                ('tenant_id', models.CharField(max_length=50)),
                ('event_type', models.CharField(max_length=100)),
                ('event_data', models.JSONField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'case_events',
                'ordering': ['-created_at'],
            },
        ),
    ]
