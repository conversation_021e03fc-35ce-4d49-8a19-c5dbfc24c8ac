"""
File Management Models for DentFlow
Handles file uploads, storage, and sharing for dental cases
"""

from django.db import models
from django.contrib.auth.models import User
from django.core.validators import FileExtensionValidator
import uuid
import hashlib
import os


class FileCategory(models.Model):
    """
    Categories for organizing files
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='file_categories')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name_plural = 'File Categories'
        unique_together = ['tenant', 'name']
        indexes = [
            models.Index(fields=['tenant', 'is_active']),
        ]
    
    def __str__(self):
        return f'{self.name} ({self.tenant.name})'


def get_file_upload_path(instance, filename):
    """
    Generate file upload path based on tenant and case
    Format: tenant_id/case_id/year/month/filename
    """
    from datetime import datetime
    now = datetime.now()
    return f'{instance.case.tenant_id}/{instance.case.id}/{now.year}/{now.month:02d}/{filename}'


class FileUpload(models.Model):
    """
    File uploads associated with dental cases
    """
    
    FILE_TYPE_CHOICES = [
        ('image', 'Image'),
        ('pdf', 'PDF Document'),
        ('stl', '3D Model (STL)'),
        ('ply', '3D Model (PLY)'),
        ('obj', '3D Model (OBJ)'),
        ('zip', 'Compressed Archive'),
        ('doc', 'Document'),
        ('video', 'Video'),
        ('audio', 'Audio'),
        ('other', 'Other'),
    ]
    
    STORAGE_BACKEND_CHOICES = [
        ('local', 'Local Storage'),
        ('s3', 'Amazon S3'),
        ('gcs', 'Google Cloud Storage'),
        ('azure', 'Azure Blob Storage'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    case = models.ForeignKey('cases.Case', on_delete=models.CASCADE, related_name='files')
    category = models.ForeignKey(FileCategory, on_delete=models.SET_NULL, null=True, blank=True)
    
    # File information
    filename = models.CharField(max_length=255)  # Stored filename
    original_filename = models.CharField(max_length=255)  # Original upload filename
    file_path = models.CharField(max_length=500)  # Full path to file
    file_size = models.PositiveIntegerField()  # Size in bytes
    file_type = models.CharField(max_length=20, choices=FILE_TYPE_CHOICES)
    file_hash = models.CharField(max_length=64, blank=True)  # SHA-256 hash
    
    # Storage details
    storage_backend = models.CharField(max_length=20, choices=STORAGE_BACKEND_CHOICES, default='local')
    
    # Metadata
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    description = models.TextField(blank=True)
    tags = models.JSONField(default=list, blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['case', 'is_active']),
            models.Index(fields=['file_type', 'is_active']),
            models.Index(fields=['uploaded_by', 'created_at']),
            models.Index(fields=['file_hash']),
        ]
        ordering = ['-created_at']
    
    def save(self, *args, **kwargs):
        """Auto-detect file type and generate hash"""
        if self.original_filename and not self.file_type:
            self.file_type = self._detect_file_type(self.original_filename)
        
        super().save(*args, **kwargs)
    
    def _detect_file_type(self, filename):
        """Detect file type based on extension"""
        ext = os.path.splitext(filename.lower())[1]
        
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
        model_extensions = ['.stl', '.ply', '.obj']
        document_extensions = ['.pdf', '.doc', '.docx', '.txt']
        video_extensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv']
        audio_extensions = ['.mp3', '.wav', '.ogg', '.m4a']
        archive_extensions = ['.zip', '.rar', '.7z', '.tar', '.gz']
        
        if ext in image_extensions:
            return 'image'
        elif ext in model_extensions:
            return 'stl' if ext == '.stl' else 'other'
        elif ext == '.pdf':
            return 'pdf'
        elif ext in document_extensions:
            return 'doc'
        elif ext in video_extensions:
            return 'video'
        elif ext in audio_extensions:
            return 'audio'
        elif ext in archive_extensions:
            return 'zip'
        else:
            return 'other'
    
    def generate_file_hash(self, file_content):
        """Generate SHA-256 hash of file content"""
        return hashlib.sha256(file_content).hexdigest()
    
    def get_file_url(self):
        """Get URL for accessing the file"""
        if self.storage_backend == 'local':
            return f'/media/{self.file_path}'
        elif self.storage_backend == 's3':
            return f'https://s3.amazonaws.com/bucket/{self.file_path}'
        # Add other storage backends as needed
        return self.file_path
    
    def __str__(self):
        return f'{self.original_filename} ({self.case.id})'


class FileShare(models.Model):
    """
    File sharing links for external access
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    file_upload = models.ForeignKey(FileUpload, on_delete=models.CASCADE, related_name='shares')
    share_token = models.CharField(max_length=64, unique=True)
    
    # Sharing details
    shared_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    shared_with_email = models.EmailField(blank=True)  # Optional: specific email
    expires_at = models.DateTimeField(null=True, blank=True)
    
    # Access tracking
    download_count = models.PositiveIntegerField(default=0)
    last_accessed = models.DateTimeField(null=True, blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['share_token']),
            models.Index(fields=['file_upload', 'is_active']),
            models.Index(fields=['expires_at']),
        ]
        ordering = ['-created_at']
    
    def save(self, *args, **kwargs):
        """Auto-generate share token"""
        if not self.share_token:
            self.share_token = self._generate_share_token()
        super().save(*args, **kwargs)
    
    def _generate_share_token(self):
        """Generate unique share token"""
        import secrets
        return secrets.token_urlsafe(32)
    
    def is_expired(self):
        """Check if share link has expired"""
        if not self.expires_at:
            return False
        from django.utils import timezone
        return timezone.now() > self.expires_at
    
    def increment_download_count(self):
        """Increment download counter"""
        from django.utils import timezone
        self.download_count += 1
        self.last_accessed = timezone.now()
        self.save(update_fields=['download_count', 'last_accessed'])
    
    def __str__(self):
        return f'Share: {self.file_upload.filename} ({self.share_token[:8]}...)'