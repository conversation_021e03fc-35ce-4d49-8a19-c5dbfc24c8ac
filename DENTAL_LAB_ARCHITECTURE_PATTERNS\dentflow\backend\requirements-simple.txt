# Django Core
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-extensions==3.2.3
django-debug-toolbar==4.2.0

# Authentication & Authorization
djangorestframework-simplejwt==5.3.0
django-guardian==2.4.0

# API Documentation
drf-spectacular==0.26.5

# File Storage - Updated for Python 3.13 compatibility
Pillow>=10.2.0

# Environment Management
python-decouple==3.8

# Data Validation
marshmallow==3.20.1
pydantic==2.5.0

# Testing
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0
factory-boy==3.3.0
model-bakery==1.17.0
pytest-mock==3.12.0

# Monitoring & Logging
structlog==23.2.0

# Security
cryptography>=41.0.7
django-ratelimit==4.1.0
django-csp==3.7

# Utils
python-slugify==8.0.1
python-dateutil==2.8.2
pytz==2023.3

# Development
pre-commit==3.6.0
black==23.11.0
isort==5.12.0
flake8==6.1.0

# Deployment
gunicorn==21.2.0
whitenoise==6.6.0

# File Processing (for STL/dental files)
numpy>=1.24.4

# Business Intelligence  
pandas>=2.1.4
reportlab==4.0.7
