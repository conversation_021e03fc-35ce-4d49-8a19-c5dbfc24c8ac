{"ast": null, "code": "export { default } from './composeClasses';", "map": {"version": 3, "names": ["default"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/node_modules/@mui/utils/esm/composeClasses/index.js"], "sourcesContent": ["export { default } from './composeClasses';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}