{"version": 3, "file": "infiniteQueryBehavior.esm.js", "sources": ["../../src/infiniteQueryBehavior.ts"], "sourcesContent": ["import type { QueryBehavior } from './query'\n\nimport type {\n  InfiniteData,\n  QueryFunctionContext,\n  QueryOptions,\n  RefetchQueryFilters,\n} from './types'\n\nexport function infiniteQueryBehavior<\n  TQueryFnData,\n  TError,\n  TData,\n>(): QueryBehavior<TQueryFnData, TError, InfiniteData<TData>> {\n  return {\n    onFetch: (context) => {\n      context.fetchFn = () => {\n        const refetchPage: RefetchQueryFilters['refetchPage'] | undefined =\n          context.fetchOptions?.meta?.refetchPage\n        const fetchMore = context.fetchOptions?.meta?.fetchMore\n        const pageParam = fetchMore?.pageParam\n        const isFetchingNextPage = fetchMore?.direction === 'forward'\n        const isFetchingPreviousPage = fetchMore?.direction === 'backward'\n        const oldPages = context.state.data?.pages || []\n        const oldPageParams = context.state.data?.pageParams || []\n        let newPageParams = oldPageParams\n        let cancelled = false\n\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal?.aborted) {\n                cancelled = true\n              } else {\n                context.signal?.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        // Get query function\n        const queryFn =\n          context.options.queryFn ||\n          (() =>\n            Promise.reject(\n              `Missing queryFn for queryKey '${context.options.queryHash}'`,\n            ))\n\n        const buildNewPages = (\n          pages: unknown[],\n          param: unknown,\n          page: unknown,\n          previous?: boolean,\n        ) => {\n          newPageParams = previous\n            ? [param, ...newPageParams]\n            : [...newPageParams, param]\n          return previous ? [page, ...pages] : [...pages, page]\n        }\n\n        // Create function to fetch a page\n        const fetchPage = (\n          pages: unknown[],\n          manual?: boolean,\n          param?: unknown,\n          previous?: boolean,\n        ): Promise<unknown[]> => {\n          if (cancelled) {\n            return Promise.reject('Cancelled')\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages)\n          }\n\n          const queryFnContext: QueryFunctionContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta,\n          }\n\n          addSignalProperty(queryFnContext)\n\n          const queryFnResult = queryFn(queryFnContext)\n\n          const promise = Promise.resolve(queryFnResult).then((page) =>\n            buildNewPages(pages, param, page, previous),\n          )\n\n          return promise\n        }\n\n        let promise: Promise<unknown[]>\n\n        // Fetch first page?\n        if (!oldPages.length) {\n          promise = fetchPage([])\n        }\n\n        // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getNextPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param)\n        }\n\n        // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getPreviousPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param, true)\n        }\n\n        // Refetch pages\n        else {\n          newPageParams = []\n\n          const manual = typeof context.options.getNextPageParam === 'undefined'\n\n          const shouldFetchFirstPage =\n            refetchPage && oldPages[0]\n              ? refetchPage(oldPages[0], 0, oldPages)\n              : true\n\n          // Fetch first page\n          promise = shouldFetchFirstPage\n            ? fetchPage([], manual, oldPageParams[0])\n            : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0]))\n\n          // Fetch remaining pages\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then((pages) => {\n              const shouldFetchNextPage =\n                refetchPage && oldPages[i]\n                  ? refetchPage(oldPages[i], i, oldPages)\n                  : true\n\n              if (shouldFetchNextPage) {\n                const param = manual\n                  ? oldPageParams[i]\n                  : getNextPageParam(context.options, pages)\n                return fetchPage(pages, manual, param)\n              }\n              return Promise.resolve(\n                buildNewPages(pages, oldPageParams[i], oldPages[i]),\n              )\n            })\n          }\n        }\n\n        const finalPromise = promise.then((pages) => ({\n          pages,\n          pageParams: newPageParams,\n        }))\n\n        return finalPromise\n      }\n    },\n  }\n}\n\nexport function getNextPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getNextPageParam?.(pages[pages.length - 1], pages)\n}\n\nexport function getPreviousPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getPreviousPageParam?.(pages[0], pages)\n}\n\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasNextPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages)\n    return (\n      typeof nextPageParam !== 'undefined' &&\n      nextPageParam !== null &&\n      nextPageParam !== false\n    )\n  }\n  return\n}\n\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasPreviousPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages)\n    return (\n      typeof previousPageParam !== 'undefined' &&\n      previousPageParam !== null &&\n      previousPageParam !== false\n    )\n  }\n  return\n}\n"], "names": ["infiniteQueryBehavior", "onFetch", "context", "fetchFn", "refetchPage", "fetchOptions", "meta", "fetchMore", "pageParam", "isFetchingNextPage", "direction", "isFetchingPreviousPage", "oldPages", "state", "data", "pages", "oldPageParams", "pageParams", "newPageParams", "cancelled", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "signal", "aborted", "addEventListener", "queryFn", "options", "Promise", "reject", "queryHash", "buildNewPages", "param", "page", "previous", "fetchPage", "manual", "length", "resolve", "queryFnContext", "query<PERSON><PERSON>", "queryFnResult", "promise", "then", "getNextPageParam", "getPreviousPageParam", "shouldFetchFirstPage", "i", "shouldFetchNextPage", "finalPromise", "hasNextPage", "Array", "isArray", "nextPageParam", "hasPreviousPage", "previousPageParam"], "mappings": "AASO,SAASA,qBAAT,GAIuD;EAC5D,OAAO;IACLC,OAAO,EAAGC,OAAD,IAAa;MACpBA,OAAO,CAACC,OAAR,GAAkB,MAAM;AAAA,QAAA,IAAA,qBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,mBAAA,EAAA,oBAAA,CAAA;;QACtB,MAAMC,WAA2D,GAC/DF,CAAAA,qBAAAA,GAAAA,OAAO,CAACG,YADuD,+CAC/D,qBAAsBC,CAAAA,IADyC,KAC/D,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAA4BF,WAD9B,CAAA;QAEA,MAAMG,SAAS,GAAGL,CAAAA,sBAAAA,GAAAA,OAAO,CAACG,YAAX,+CAAG,sBAAsBC,CAAAA,IAAzB,KAAG,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAA4BC,SAA9C,CAAA;AACA,QAAA,MAAMC,SAAS,GAAGD,SAAH,IAAGA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,SAAS,CAAEC,SAA7B,CAAA;QACA,MAAMC,kBAAkB,GAAG,CAAAF,SAAS,IAAA,IAAT,YAAAA,SAAS,CAAEG,SAAX,MAAyB,SAApD,CAAA;QACA,MAAMC,sBAAsB,GAAG,CAAAJ,SAAS,IAAA,IAAT,YAAAA,SAAS,CAAEG,SAAX,MAAyB,UAAxD,CAAA;QACA,MAAME,QAAQ,GAAG,CAAA,CAAA,mBAAA,GAAAV,OAAO,CAACW,KAAR,CAAcC,IAAd,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,mBAAA,CAAoBC,KAApB,KAA6B,EAA9C,CAAA;QACA,MAAMC,aAAa,GAAG,CAAA,CAAA,oBAAA,GAAAd,OAAO,CAACW,KAAR,CAAcC,IAAd,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,oBAAA,CAAoBG,UAApB,KAAkC,EAAxD,CAAA;QACA,IAAIC,aAAa,GAAGF,aAApB,CAAA;QACA,IAAIG,SAAS,GAAG,KAAhB,CAAA;;QAEA,MAAMC,iBAAiB,GAAIC,MAAD,IAAqB;AAC7CC,UAAAA,MAAM,CAACC,cAAP,CAAsBF,MAAtB,EAA8B,QAA9B,EAAwC;AACtCG,YAAAA,UAAU,EAAE,IAD0B;AAEtCC,YAAAA,GAAG,EAAE,MAAM;AAAA,cAAA,IAAA,eAAA,CAAA;;AACT,cAAA,IAAA,CAAA,eAAA,GAAIvB,OAAO,CAACwB,MAAZ,KAAI,IAAA,IAAA,eAAA,CAAgBC,OAApB,EAA6B;AAC3BR,gBAAAA,SAAS,GAAG,IAAZ,CAAA;AACD,eAFD,MAEO;AAAA,gBAAA,IAAA,gBAAA,CAAA;;gBACL,CAAAjB,gBAAAA,GAAAA,OAAO,CAACwB,MAAR,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,gBAAA,CAAgBE,gBAAhB,CAAiC,OAAjC,EAA0C,MAAM;AAC9CT,kBAAAA,SAAS,GAAG,IAAZ,CAAA;iBADF,CAAA,CAAA;AAGD,eAAA;;cACD,OAAOjB,OAAO,CAACwB,MAAf,CAAA;AACD,aAAA;WAXH,CAAA,CAAA;AAaD,SAdD,CAZsB;;;AA6BtB,QAAA,MAAMG,OAAO,GACX3B,OAAO,CAAC4B,OAAR,CAAgBD,OAAhB,KACC,MACCE,OAAO,CAACC,MAAR,oCACmC9B,OAAO,CAAC4B,OAAR,CAAgBG,SADnD,OAFF,CADF,CAAA;;QAOA,MAAMC,aAAa,GAAG,CACpBnB,KADoB,EAEpBoB,KAFoB,EAGpBC,IAHoB,EAIpBC,QAJoB,KAKjB;AACHnB,UAAAA,aAAa,GAAGmB,QAAQ,GACpB,CAACF,KAAD,EAAQ,GAAGjB,aAAX,CADoB,GAEpB,CAAC,GAAGA,aAAJ,EAAmBiB,KAAnB,CAFJ,CAAA;AAGA,UAAA,OAAOE,QAAQ,GAAG,CAACD,IAAD,EAAO,GAAGrB,KAAV,CAAH,GAAsB,CAAC,GAAGA,KAAJ,EAAWqB,IAAX,CAArC,CAAA;AACD,SAVD,CApCsB;;;QAiDtB,MAAME,SAAS,GAAG,CAChBvB,KADgB,EAEhBwB,MAFgB,EAGhBJ,KAHgB,EAIhBE,QAJgB,KAKO;AACvB,UAAA,IAAIlB,SAAJ,EAAe;AACb,YAAA,OAAOY,OAAO,CAACC,MAAR,CAAe,WAAf,CAAP,CAAA;AACD,WAAA;;UAED,IAAI,OAAOG,KAAP,KAAiB,WAAjB,IAAgC,CAACI,MAAjC,IAA2CxB,KAAK,CAACyB,MAArD,EAA6D;AAC3D,YAAA,OAAOT,OAAO,CAACU,OAAR,CAAgB1B,KAAhB,CAAP,CAAA;AACD,WAAA;;AAED,UAAA,MAAM2B,cAAoC,GAAG;YAC3CC,QAAQ,EAAEzC,OAAO,CAACyC,QADyB;AAE3CnC,YAAAA,SAAS,EAAE2B,KAFgC;AAG3C7B,YAAAA,IAAI,EAAEJ,OAAO,CAAC4B,OAAR,CAAgBxB,IAAAA;WAHxB,CAAA;UAMAc,iBAAiB,CAACsB,cAAD,CAAjB,CAAA;AAEA,UAAA,MAAME,aAAa,GAAGf,OAAO,CAACa,cAAD,CAA7B,CAAA;UAEA,MAAMG,OAAO,GAAGd,OAAO,CAACU,OAAR,CAAgBG,aAAhB,CAA+BE,CAAAA,IAA/B,CAAqCV,IAAD,IAClDF,aAAa,CAACnB,KAAD,EAAQoB,KAAR,EAAeC,IAAf,EAAqBC,QAArB,CADC,CAAhB,CAAA;AAIA,UAAA,OAAOQ,OAAP,CAAA;SA5BF,CAAA;;QA+BA,IAAIA,OAAJ,CAhFsB;;AAmFtB,QAAA,IAAI,CAACjC,QAAQ,CAAC4B,MAAd,EAAsB;AACpBK,UAAAA,OAAO,GAAGP,SAAS,CAAC,EAAD,CAAnB,CAAA;AACD,SAFD;aAKK,IAAI7B,kBAAJ,EAAwB;AAC3B,UAAA,MAAM8B,MAAM,GAAG,OAAO/B,SAAP,KAAqB,WAApC,CAAA;AACA,UAAA,MAAM2B,KAAK,GAAGI,MAAM,GAChB/B,SADgB,GAEhBuC,gBAAgB,CAAC7C,OAAO,CAAC4B,OAAT,EAAkBlB,QAAlB,CAFpB,CAAA;UAGAiC,OAAO,GAAGP,SAAS,CAAC1B,QAAD,EAAW2B,MAAX,EAAmBJ,KAAnB,CAAnB,CAAA;AACD,SANI;aASA,IAAIxB,sBAAJ,EAA4B;AAC/B,UAAA,MAAM4B,MAAM,GAAG,OAAO/B,SAAP,KAAqB,WAApC,CAAA;AACA,UAAA,MAAM2B,KAAK,GAAGI,MAAM,GAChB/B,SADgB,GAEhBwC,oBAAoB,CAAC9C,OAAO,CAAC4B,OAAT,EAAkBlB,QAAlB,CAFxB,CAAA;UAGAiC,OAAO,GAAGP,SAAS,CAAC1B,QAAD,EAAW2B,MAAX,EAAmBJ,KAAnB,EAA0B,IAA1B,CAAnB,CAAA;AACD,SANI;aASA;AACHjB,UAAAA,aAAa,GAAG,EAAhB,CAAA;UAEA,MAAMqB,MAAM,GAAG,OAAOrC,OAAO,CAAC4B,OAAR,CAAgBiB,gBAAvB,KAA4C,WAA3D,CAAA;UAEA,MAAME,oBAAoB,GACxB7C,WAAW,IAAIQ,QAAQ,CAAC,CAAD,CAAvB,GACIR,WAAW,CAACQ,QAAQ,CAAC,CAAD,CAAT,EAAc,CAAd,EAAiBA,QAAjB,CADf,GAEI,IAHN,CALG;;AAWHiC,UAAAA,OAAO,GAAGI,oBAAoB,GAC1BX,SAAS,CAAC,EAAD,EAAKC,MAAL,EAAavB,aAAa,CAAC,CAAD,CAA1B,CADiB,GAE1Be,OAAO,CAACU,OAAR,CAAgBP,aAAa,CAAC,EAAD,EAAKlB,aAAa,CAAC,CAAD,CAAlB,EAAuBJ,QAAQ,CAAC,CAAD,CAA/B,CAA7B,CAFJ,CAXG;;AAgBH,UAAA,KAAK,IAAIsC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtC,QAAQ,CAAC4B,MAA7B,EAAqCU,CAAC,EAAtC,EAA0C;AACxCL,YAAAA,OAAO,GAAGA,OAAO,CAACC,IAAR,CAAc/B,KAAD,IAAW;cAChC,MAAMoC,mBAAmB,GACvB/C,WAAW,IAAIQ,QAAQ,CAACsC,CAAD,CAAvB,GACI9C,WAAW,CAACQ,QAAQ,CAACsC,CAAD,CAAT,EAAcA,CAAd,EAAiBtC,QAAjB,CADf,GAEI,IAHN,CAAA;;AAKA,cAAA,IAAIuC,mBAAJ,EAAyB;AACvB,gBAAA,MAAMhB,KAAK,GAAGI,MAAM,GAChBvB,aAAa,CAACkC,CAAD,CADG,GAEhBH,gBAAgB,CAAC7C,OAAO,CAAC4B,OAAT,EAAkBf,KAAlB,CAFpB,CAAA;AAGA,gBAAA,OAAOuB,SAAS,CAACvB,KAAD,EAAQwB,MAAR,EAAgBJ,KAAhB,CAAhB,CAAA;AACD,eAAA;;AACD,cAAA,OAAOJ,OAAO,CAACU,OAAR,CACLP,aAAa,CAACnB,KAAD,EAAQC,aAAa,CAACkC,CAAD,CAArB,EAA0BtC,QAAQ,CAACsC,CAAD,CAAlC,CADR,CAAP,CAAA;AAGD,aAfS,CAAV,CAAA;AAgBD,WAAA;AACF,SAAA;;AAED,QAAA,MAAME,YAAY,GAAGP,OAAO,CAACC,IAAR,CAAc/B,KAAD,KAAY;UAC5CA,KAD4C;AAE5CE,UAAAA,UAAU,EAAEC,aAAAA;AAFgC,SAAZ,CAAb,CAArB,CAAA;AAKA,QAAA,OAAOkC,YAAP,CAAA;OAnJF,CAAA;AAqJD,KAAA;GAvJH,CAAA;AAyJD,CAAA;AAEM,SAASL,gBAAT,CACLjB,OADK,EAELf,KAFK,EAGgB;AACrB,EAAA,OAAOe,OAAO,CAACiB,gBAAf,oBAAOjB,OAAO,CAACiB,gBAAR,CAA2BhC,KAAK,CAACA,KAAK,CAACyB,MAAN,GAAe,CAAhB,CAAhC,EAAoDzB,KAApD,CAAP,CAAA;AACD,CAAA;AAEM,SAASiC,oBAAT,CACLlB,OADK,EAELf,KAFK,EAGgB;AACrB,EAAA,OAAOe,OAAO,CAACkB,oBAAf,IAAA,IAAA,GAAA,KAAA,CAAA,GAAOlB,OAAO,CAACkB,oBAAR,CAA+BjC,KAAK,CAAC,CAAD,CAApC,EAAyCA,KAAzC,CAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;;AACO,SAASsC,WAAT,CACLvB,OADK,EAELf,KAFK,EAGgB;EACrB,IAAIe,OAAO,CAACiB,gBAAR,IAA4BO,KAAK,CAACC,OAAN,CAAcxC,KAAd,CAAhC,EAAsD;AACpD,IAAA,MAAMyC,aAAa,GAAGT,gBAAgB,CAACjB,OAAD,EAAUf,KAAV,CAAtC,CAAA;IACA,OACE,OAAOyC,aAAP,KAAyB,WAAzB,IACAA,aAAa,KAAK,IADlB,IAEAA,aAAa,KAAK,KAHpB,CAAA;AAKD,GAAA;;AACD,EAAA,OAAA;AACD,CAAA;AAED;AACA;AACA;AACA;;AACO,SAASC,eAAT,CACL3B,OADK,EAELf,KAFK,EAGgB;EACrB,IAAIe,OAAO,CAACkB,oBAAR,IAAgCM,KAAK,CAACC,OAAN,CAAcxC,KAAd,CAApC,EAA0D;AACxD,IAAA,MAAM2C,iBAAiB,GAAGV,oBAAoB,CAAClB,OAAD,EAAUf,KAAV,CAA9C,CAAA;IACA,OACE,OAAO2C,iBAAP,KAA6B,WAA7B,IACAA,iBAAiB,KAAK,IADtB,IAEAA,iBAAiB,KAAK,KAHxB,CAAA;AAKD,GAAA;;AACD,EAAA,OAAA;AACD;;;;"}