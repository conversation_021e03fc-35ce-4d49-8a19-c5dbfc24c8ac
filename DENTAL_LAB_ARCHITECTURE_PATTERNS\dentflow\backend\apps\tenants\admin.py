"""
Django Admin for Tenants App
Multi-tenant management for DentFlow
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from apps.tenants.models import Tenant, TenantSettings, TenantSubscription


@admin.register(Tenant)
class TenantAdmin(admin.ModelAdmin):
    """Admin interface for Tenants (Dental Labs)"""
    list_display = [
        'name', 'subdomain', 'is_active', 'subscription_status', 
        'user_count', 'case_count', 'created_at'
    ]
    list_filter = ['is_active', 'created_at', 'plan']
    search_fields = ['name', 'subdomain', 'contact_email']
    readonly_fields = ['id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'subdomain', 'contact_email', 'is_active')
        }),
        ('Subscription', {
            'fields': ('plan', 'max_users', 'max_cases_per_month')
        }),
        ('Contact Information', {
            'fields': ('phone', 'address', 'website')
        }),
        ('System Information', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def subscription_status(self, obj):
        """Display subscription status with color coding"""
        if not obj.is_active:
            return format_html('<span style="color: #f44336;">❌ Inactive</span>')
        
        # Check if subscription exists and is valid
        try:
            subscription = obj.subscription
            if subscription.is_active:
                return format_html('<span style="color: #4caf50;">✅ Active</span>')
            else:
                return format_html('<span style="color: #ff9800;">⚠️ Expired</span>')
        except:
            return format_html('<span style="color: #666;">No subscription</span>')
    subscription_status.short_description = 'Subscription'
    
    def user_count(self, obj):
        """Show number of users for this tenant"""
        # This would connect to User model when properly implemented
        count = 0  # Placeholder
        return f'{count} users'
    user_count.short_description = 'Users'
    
    def case_count(self, obj):
        """Show number of cases for this tenant"""
        from apps.cases.models import Case
        count = Case.objects.filter(tenant=obj).count()
        if count > 0:
            url = reverse('admin:cases_case_changelist') + f'?tenant__id__exact={obj.id}'
            return format_html('<a href="{}">{} cases</a>', url, count)
        return '0 cases'
    case_count.short_description = 'Cases'
    
    actions = ['activate_tenants', 'deactivate_tenants']
    
    def activate_tenants(self, request, queryset):
        """Activate selected tenants"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} tenants activated.')
    activate_tenants.short_description = 'Activate selected tenants'
    
    def deactivate_tenants(self, request, queryset):
        """Deactivate selected tenants"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} tenants deactivated.')
    deactivate_tenants.short_description = 'Deactivate selected tenants'


@admin.register(TenantSettings)
class TenantSettingsAdmin(admin.ModelAdmin):
    """Admin interface for Tenant Settings"""
    list_display = ['tenant', 'timezone', 'currency', 'language', 'updated_at']
    list_filter = ['timezone', 'currency', 'language']
    search_fields = ['tenant__name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Tenant', {
            'fields': ('tenant',)
        }),
        ('Localization', {
            'fields': ('timezone', 'currency', 'language', 'date_format')
        }),
        ('Business Settings', {
            'fields': ('business_hours', 'default_workflow')
        }),
        ('Notifications', {
            'fields': ('email_notifications', 'sms_notifications', 'slack_webhook')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(TenantSubscription)
class TenantSubscriptionAdmin(admin.ModelAdmin):
    """Admin interface for Tenant Subscriptions"""
    list_display = [
        'tenant', 'plan', 'status_display', 'start_date', 'end_date', 
        'monthly_price', 'is_trial'
    ]
    list_filter = ['plan', 'is_active', 'is_trial', 'start_date']
    search_fields = ['tenant__name']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'start_date'
    
    fieldsets = (
        ('Subscription Details', {
            'fields': ('tenant', 'plan', 'is_active', 'is_trial')
        }),
        ('Billing Period', {
            'fields': ('start_date', 'end_date', 'monthly_price')
        }),
        ('Payment Information', {
            'fields': ('stripe_subscription_id', 'last_payment_date', 'next_billing_date')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def status_display(self, obj):
        """Display subscription status with color coding"""
        if obj.is_active:
            if obj.is_trial:
                return format_html('<span style="color: #ff9800;">🆓 Trial</span>')
            else:
                return format_html('<span style="color: #4caf50;">✅ Active</span>')
        else:
            return format_html('<span style="color: #f44336;">❌ Inactive</span>')
    status_display.short_description = 'Status'
    
    actions = ['activate_subscriptions', 'deactivate_subscriptions']
    
    def activate_subscriptions(self, request, queryset):
        """Activate selected subscriptions"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} subscriptions activated.')
    activate_subscriptions.short_description = 'Activate selected subscriptions'
    
    def deactivate_subscriptions(self, request, queryset):
        """Deactivate selected subscriptions"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} subscriptions deactivated.')
    deactivate_subscriptions.short_description = 'Deactivate selected subscriptions'