{"version": 3, "file": "consistent-type-assertions.js", "sourceRoot": "", "sources": ["../../src/rules/consistent-type-assertions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAoBhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,4BAA4B;IAClC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,IAAI,EAAE;YACJ,WAAW,EAAE,6CAA6C;YAC1D,WAAW,EAAE,QAAQ;SACtB;QACD,QAAQ,EAAE;YACR,EAAE,EAAE,4CAA4C;YAChD,eAAe,EAAE,4CAA4C;YAC7D,KAAK,EAAE,iCAAiC;YACxC,6BAA6B,EAAE,qCAAqC;YACpE,wCAAwC,EACtC,0CAA0C;YAC5C,uCAAuC,EACrC,mDAAmD;SACtD;QACD,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL;wBACE,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,cAAc,EAAE;gCACd,IAAI,EAAE,CAAC,OAAO,CAAC;6BAChB;yBACF;wBACD,oBAAoB,EAAE,KAAK;wBAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;qBAC7B;oBACD;wBACE,IAAI,EAAE,QAAQ;wBACd,UAAU,EAAE;4BACV,cAAc,EAAE;gCACd,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;6BAC9B;4BACD,2BAA2B,EAAE;gCAC3B,IAAI,EAAE,CAAC,OAAO,EAAE,oBAAoB,EAAE,OAAO,CAAC;6BAC/C;yBACF;wBACD,oBAAoB,EAAE,KAAK;wBAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;qBAC7B;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,cAAc,EAAE,IAAI;YACpB,2BAA2B,EAAE,OAAO;SACrC;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,SAAS,OAAO,CAAC,IAAuB;YACtC,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;gBAChD,OAAO,KAAK,CAAC;aACd;YAED,OAAO,CACL,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAChD,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,CAC/B,CAAC;QACJ,CAAC;QAED,SAAS,sBAAsB,CAAC,IAAmB;YACjD,gDAAgD;YAChD,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE;gBAC1C,MAAM,gBAAgB,GAAG,UAAU,CAAC,cAAc,CAChD,IAAI,EACJ,IAAI,CAAC,mBAAmB,CACxB,CAAC;gBACH,MAAM,gBAAgB,GAAG,UAAU,CAAC,aAAa,CAC/C,IAAI,EACJ,IAAI,CAAC,mBAAmB,CACxB,CAAC;gBAEH,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxD,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACxD;YAED,OAAO,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;QAC3D,CAAC;QAED,SAAS,4BAA4B,CACnC,IAAwD;YAExD,MAAM,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC;YAEzC,0DAA0D;YAC1D,IAAI,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,SAAS,KAAK,OAAO,EAAE;gBACzD,OAAO;aACR;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI;gBACJ,SAAS;gBACT,IAAI,EACF,SAAS,KAAK,OAAO;oBACnB,CAAC,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;oBACnD,CAAC,CAAC,EAAE;gBACR,GAAG,EACD,SAAS,KAAK,IAAI;oBAChB,CAAC,CAAC,CAAC,KAAK,EAAsB,EAAE,CAAC;wBAC7B,KAAK,CAAC,WAAW,CACf,IAAI,EACJ,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CACxC;wBACD,KAAK,CAAC,eAAe,CACnB,IAAI,EACJ,OAAO,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CACrD;qBACF;oBACH,CAAC,CAAC,SAAS;aAChB,CAAC,CAAC;QACL,CAAC;QAED,SAAS,SAAS,CAAC,IAAuB;YACxC,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,sBAAc,CAAC,YAAY,CAAC;gBACjC,KAAK,sBAAc,CAAC,gBAAgB;oBAClC,OAAO,KAAK,CAAC;gBACf,KAAK,sBAAc,CAAC,eAAe;oBACjC,OAAO;oBACL,kCAAkC;oBAClC,CAAC,OAAO,CAAC,IAAI,CAAC;wBACd,uEAAuE;wBACvE,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,CACtD,CAAC;gBAEJ;oBACE,OAAO,IAAI,CAAC;aACf;QACH,CAAC;QAED,SAAS,eAAe,CACtB,IAAwD;;YAExD,IACE,OAAO,CAAC,cAAc,KAAK,OAAO;gBAClC,OAAO,CAAC,2BAA2B,KAAK,OAAO;gBAC/C,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EACxD;gBACA,OAAO;aACR;YAED,IACE,OAAO,CAAC,2BAA2B,KAAK,oBAAoB;gBAC5D,IAAI,CAAC,MAAM;gBACX,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;oBAChD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;oBAClD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;oBAClD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;oBACrD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB,CAAC,EAC7D;gBACA,OAAO;aACR;YAED,IACE,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC;gBAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EACxD;gBACA,MAAM,OAAO,GAA+C,EAAE,CAAC;gBAC/D,IACE,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,kBAAkB;oBACvD,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,EAC9B;oBACA,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;oBACxB,OAAO,CAAC,IAAI,CAAC;wBACX,SAAS,EAAE,0CAA0C;wBACrD,IAAI,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;wBACvD,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;4BACZ,KAAK,CAAC,eAAe,CACnB,MAAM,CAAC,EAAE,EACT,KAAK,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAC/C;4BACD,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;yBACjE;qBACF,CAAC,CAAC;iBACJ;gBACD,OAAO,CAAC,IAAI,CAAC;oBACX,SAAS,EAAE,yCAAyC;oBACpD,IAAI,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE;oBACvD,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;wBACZ,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAChE,KAAK,CAAC,eAAe,CACnB,IAAI,EACJ,cAAc,OAAO;6BAClB,aAAa,EAAE;6BACf,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAClC;qBACF;iBACF,CAAC,CAAC;gBAEH,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,+BAA+B;oBAC1C,OAAO;iBACR,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,eAAe,CAAC,IAAI;gBAClB,IAAI,OAAO,CAAC,cAAc,KAAK,eAAe,EAAE;oBAC9C,4BAA4B,CAAC,IAAI,CAAC,CAAC;oBACnC,OAAO;iBACR;gBAED,eAAe,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;YACD,cAAc,CAAC,IAAI;gBACjB,IAAI,OAAO,CAAC,cAAc,KAAK,IAAI,EAAE;oBACnC,4BAA4B,CAAC,IAAI,CAAC,CAAC;oBACnC,OAAO;iBACR;gBAED,eAAe,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}