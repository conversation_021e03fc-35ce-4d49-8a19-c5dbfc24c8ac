"""
Health Check and System Status Views
Provides endpoints for monitoring system health and status
"""

from django.http import JsonResponse
from django.db import connections
from django.core.cache import cache
from django.conf import settings
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
import redis
import logging
from datetime import datetime


logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """
    Basic health check endpoint
    Returns 200 if system is healthy
    """
    
    try:
        # Check database connectivity
        db_status = check_database()
        
        # Check cache connectivity
        cache_status = check_cache()
        
        # Check Celery (if available)
        celery_status = check_celery()
        
        # Overall status
        overall_healthy = all([
            db_status['healthy'],
            cache_status['healthy'],
            celery_status['healthy']
        ])
        
        response_data = {
            'status': 'healthy' if overall_healthy else 'unhealthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': getattr(settings, 'VERSION', '1.0.0'),
            'environment': 'development' if settings.DEBUG else 'production',
            'services': {
                'database': db_status,
                'cache': cache_status,
                'celery': celery_status,
            }
        }
        
        status_code = status.HTTP_200_OK if overall_healthy else status.HTTP_503_SERVICE_UNAVAILABLE
        
        return Response(response_data, status=status_code)
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return Response(
            {
                'status': 'unhealthy',
                'timestamp': datetime.utcnow().isoformat(),
                'error': str(e)
            },
            status=status.HTTP_503_SERVICE_UNAVAILABLE
        )


@api_view(['GET'])
@permission_classes([AllowAny])
def system_status(request):
    """
    Detailed system status endpoint
    Returns comprehensive system information
    """
    
    try:
        from apps.cases.models import Case, Tenant
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Basic system info
        system_info = {
            'timestamp': datetime.utcnow().isoformat(),
            'version': getattr(settings, 'VERSION', '1.0.0'),
            'environment': 'development' if settings.DEBUG else 'production',
            'python_version': get_python_version(),
            'django_version': get_django_version(),
        }
        
        # Database statistics
        database_stats = {
            'total_tenants': Tenant.objects.filter(is_active=True).count(),
            'total_users': User.objects.filter(is_active=True).count(),
            'total_cases': Case.objects.filter(deleted_at__isnull=True).count(),
            'active_cases': Case.objects.filter(
                status__in=['received', 'design', 'milling', 'sintering', 'qc'],
                deleted_at__isnull=True
            ).count(),
        }
        
        # Service status
        services = {
            'database': check_database(),
            'cache': check_cache(),
            'celery': check_celery(),
        }
        
        # Configuration status
        config_status = {
            'workflow_engine_enabled': getattr(settings, 'DENTFLOW_SETTINGS', {}).get('WORKFLOW_ENGINE_ENABLED', False),
            'auto_assignment_enabled': getattr(settings, 'DENTFLOW_SETTINGS', {}).get('AUTO_ASSIGNMENT_ENABLED', False),
            'sla_monitoring_enabled': getattr(settings, 'DENTFLOW_SETTINGS', {}).get('SLA_MONITORING_ENABLED', False),
            'tenant_isolation_enabled': getattr(settings, 'DENTFLOW_SETTINGS', {}).get('TENANT_ISOLATION_ENABLED', False),
        }
        
        return Response({
            'system_info': system_info,
            'database_stats': database_stats,
            'services': services,
            'configuration': config_status,
        })
        
    except Exception as e:
        logger.error(f"System status check failed: {e}")
        return Response(
            {
                'error': 'Failed to retrieve system status',
                'details': str(e),
                'timestamp': datetime.utcnow().isoformat()
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def check_database():
    """Check database connectivity and basic operations"""
    
    try:
        # Test default database connection
        connection = connections['default']
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        
        if result[0] == 1:
            return {
                'healthy': True,
                'message': 'Database connection successful',
                'response_time_ms': get_db_response_time()
            }
        else:
            return {
                'healthy': False,
                'message': 'Database query returned unexpected result'
            }
            
    except Exception as e:
        return {
            'healthy': False,
            'message': f'Database connection failed: {str(e)}'
        }


def check_cache():
    """Check cache connectivity (Redis)"""
    
    try:
        # Test cache set/get operation
        test_key = 'health_check_test'
        test_value = 'test_value'
        
        cache.set(test_key, test_value, timeout=10)
        retrieved_value = cache.get(test_key)
        
        if retrieved_value == test_value:
            cache.delete(test_key)
            return {
                'healthy': True,
                'message': 'Cache connection successful'
            }
        else:
            return {
                'healthy': False,
                'message': 'Cache operation failed'
            }
            
    except Exception as e:
        return {
            'healthy': False,
            'message': f'Cache connection failed: {str(e)}'
        }


def check_celery():
    """Check Celery worker connectivity"""
    
    try:
        from celery import current_app
        
        # Check if Celery is configured
        if not hasattr(current_app, 'control'):
            return {
                'healthy': False,
                'message': 'Celery not configured'
            }
        
        # Try to ping workers
        inspect = current_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            active_workers = len(stats)
            return {
                'healthy': True,
                'message': f'Celery workers active: {active_workers}',
                'active_workers': active_workers
            }
        else:
            return {
                'healthy': False,
                'message': 'No active Celery workers found'
            }
            
    except Exception as e:
        return {
            'healthy': False,
            'message': f'Celery check failed: {str(e)}'
        }


def get_db_response_time():
    """Measure database response time"""
    
    try:
        import time
        start_time = time.time()
        
        connection = connections['default']
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM django_migrations")
            cursor.fetchone()
        
        end_time = time.time()
        return round((end_time - start_time) * 1000, 2)  # Convert to milliseconds
        
    except Exception:
        return None


def get_python_version():
    """Get Python version info"""
    import sys
    return f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"


def get_django_version():
    """Get Django version info"""
    import django
    return django.get_version()


@api_view(['GET'])
@permission_classes([AllowAny])
def api_info(request):
    """
    API information endpoint
    Returns API version and available endpoints
    """
    
    return Response({
        'api_version': 'v1',
        'django_version': get_django_version(),
        'endpoints': {
            'health': '/health/',
            'system_status': '/health/status/',
            'api_info': '/health/info/',
            'api_docs': '/api/docs/',
            'api_schema': '/api/schema/',
            'cases': '/api/v1/cases/',
        },
        'authentication': {
            'type': 'JWT',
            'header': 'Authorization: Bearer <token>',
            'obtain_token': '/api/v1/auth/token/',
            'refresh_token': '/api/v1/auth/token/refresh/',
        },
        'documentation': {
            'swagger_ui': '/api/docs/',
            'redoc': '/api/redoc/',
            'openapi_schema': '/api/schema/',
        }
    })