"""
Django Models for Cases - Thin ORM layer
These models are adapters that translate between domain objects and database.
Following DDD principles: ORM depends on domain, not the other way around.
"""

from django.db import models
from django.utils import timezone
import uuid

from domain.model import Case as DomainCase, CaseId, <PERSON>th<PERSON>umber, Priority, CaseStatus, WorkflowStage


class Tenant(models.Model):
    """Multi-tenant isolation at DB level"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=255)
    subdomain = models.CharField(max_length=100, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
    def __str__(self):
        return self.name


class Clinic(models.Model):
    """Clinics that submit cases to labs"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    address = models.TextField()
    
    class Meta:
        unique_together = ['tenant', 'name']


class Case(models.Model):
    """
    Django ORM model for Cases - thin adapter over domain model
    This is NOT the business logic layer - that's in domain.model.Case
    """
    
    # Database-specific fields
    id = models.CharField(max_length=50, primary_key=True)  # Case ID like LAB-2025-07-24-0001
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE)
    clinic = models.ForeignKey(Clinic, on_delete=models.CASCADE)
    
    # Domain data fields
    patient_name = models.CharField(max_length=255)
    tooth_number = models.CharField(max_length=5)
    service_type = models.CharField(max_length=100)
    priority = models.CharField(max_length=20, default='normal')
    status = models.CharField(max_length=20, default='received')
    
    # Workflow tracking
    current_stage_index = models.IntegerField(default=0)
    workflow_stages = models.JSONField(default=list)  # Serialized WorkflowStage objects
    assigned_technician_id = models.CharField(max_length=50, null=True, blank=True)
    
    # Timestamps
    due_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)  # Soft delete
    
    # Additional data
    notes = models.JSONField(default=list)
    files = models.JSONField(default=list)
    
    class Meta:
        indexes = [
            models.Index(fields=['tenant', 'status', 'created_at']),
            models.Index(fields=['tenant', 'due_date']),
            models.Index(fields=['clinic', 'status']),
        ]
        # Row Level Security will be handled at middleware level
    
    @classmethod
    def from_domain(cls, domain_case: DomainCase) -> 'Case':
        """Convert domain Case to Django model"""
        return cls(
            id=domain_case.case_id.value,
            tenant_id=domain_case.tenant_id,
            clinic_id=domain_case.clinic_id,
            patient_name=domain_case.patient_name,
            tooth_number=domain_case.tooth_number.value,
            service_type=domain_case.service_type,
            priority=domain_case.priority.value,
            status=domain_case.status.value,
            current_stage_index=domain_case.current_stage_index,
            workflow_stages=[
                {
                    'name': stage.name,
                    'department': stage.department,
                    'estimated_duration_minutes': stage.estimated_duration_minutes,
                    'auto_assign': stage.auto_assign,
                    'requires_quality_check': stage.requires_quality_check,
                    'machine_required': stage.machine_required
                }
                for stage in domain_case.workflow_stages
            ],
            assigned_technician_id=domain_case.assigned_technician_id,
            due_date=domain_case.due_date,
            notes=domain_case.notes,
            files=domain_case.files,
            created_at=domain_case.created_at,
            updated_at=domain_case.updated_at,
        )
    
    def to_domain(self) -> DomainCase:
        """Convert Django model to domain Case"""
        from domain.model import Case as DomainCase, CaseId, ToothNumber, Priority
        
        # Reconstruct workflow stages
        workflow_stages = [
            WorkflowStage(
                name=stage_data['name'],
                department=stage_data['department'],
                estimated_duration_minutes=stage_data['estimated_duration_minutes'],
                auto_assign=stage_data.get('auto_assign', False),
                requires_quality_check=stage_data.get('requires_quality_check', False),
                machine_required=stage_data.get('machine_required')
            )
            for stage_data in self.workflow_stages
        ]
        
        # Create domain case
        domain_case = DomainCase(
            case_id=CaseId(self.id),
            clinic_id=str(self.clinic_id),
            patient_name=self.patient_name,
            tooth_number=ToothNumber(self.tooth_number),
            service_type=self.service_type,
            tenant_id=str(self.tenant_id),
            priority=Priority(self.priority)
        )        
        # Set internal state (normally set by constructor)
        domain_case.status = CaseStatus(self.status)
        domain_case.current_stage_index = self.current_stage_index
        domain_case.workflow_stages = workflow_stages
        domain_case.assigned_technician_id = self.assigned_technician_id
        domain_case.due_date = self.due_date
        domain_case.notes = self.notes
        domain_case.files = self.files
        domain_case.created_at = self.created_at
        domain_case.updated_at = self.updated_at
        
        # Clear events to avoid duplicate processing
        domain_case.events = []
        
        return domain_case
    
    def update_from_domain(self, domain_case: DomainCase):
        """Update Django model from domain Case (for persistence)"""
        self.patient_name = domain_case.patient_name
        self.tooth_number = domain_case.tooth_number.value
        self.priority = domain_case.priority.value
        self.status = domain_case.status.value
        self.current_stage_index = domain_case.current_stage_index
        self.assigned_technician_id = domain_case.assigned_technician_id
        self.due_date = domain_case.due_date
        self.notes = domain_case.notes
        self.files = domain_case.files
        self.updated_at = domain_case.updated_at
        
        # Update workflow stages
        self.workflow_stages = [
            {
                'name': stage.name,
                'department': stage.department,
                'estimated_duration_minutes': stage.estimated_duration_minutes,
                'auto_assign': stage.auto_assign,
                'requires_quality_check': stage.requires_quality_check,
                'machine_required': stage.machine_required
            }
            for stage in domain_case.workflow_stages
        ]
    
    def __str__(self):
        return f"Case {self.id} - {self.patient_name} ({self.status})"


class CaseEvent(models.Model):
    """Event sourcing table for audit trail"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='events')
    event_type = models.CharField(max_length=100)
    event_data = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['case', 'created_at']),
            models.Index(fields=['event_type', 'created_at']),
        ]