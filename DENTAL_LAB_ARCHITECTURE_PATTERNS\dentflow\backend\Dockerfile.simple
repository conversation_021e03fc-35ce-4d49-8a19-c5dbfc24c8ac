# Simplified Docker build for DentFlow Backend (SQLite + No Redis)
FROM python:3.11-slim AS base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONIOENCODING=utf-8 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies (removed postgresql-client)
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    netcat-openbsd \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/*

# Create application directory
WORKDIR /app

# Stage: Development
FROM base AS development

# Install Python dependencies from simplified requirements
COPY requirements-simple.txt ./requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Install development dependencies
RUN pip install --no-cache-dir \
    ipython \
    django-debug-toolbar \
    pytest-django \
    pytest-cov

# Copy application code
COPY . .

# Create directories for logs, media and database
RUN mkdir -p logs media data

# Create SQLite database file with proper permissions
RUN touch /app/db.sqlite3 && chmod 664 /app/db.sqlite3

# Create non-root user
RUN groupadd -r dentflow && useradd -r -g dentflow dentflow
RUN chown -R dentflow:dentflow /app
USER dentflow

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/admin/ || exit 1

# Default command
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
