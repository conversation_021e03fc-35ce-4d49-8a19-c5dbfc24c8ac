"""
DentFlow Unit of Work Pattern
Manages transactions and coordinates between repositories
Ensures tenant isolation at the database level
"""

from abc import ABC, abstractmethod
from typing import List, Optional
import logging
from contextlib import contextmanager
from django.db import transaction

from domain import events
from infrastructure.repository import AbstractCaseRepository, DjangoCaseRepository, FakeCaseRepository


logger = logging.getLogger(__name__)


class AbstractUnitOfWork(ABC):
    """
    Unit of Work pattern ensures:
    1. All repository operations happen in same transaction
    2. Domain events are collected and published atomically
    3. Tenant isolation is enforced at database level
    """
    
    cases: AbstractCaseRepository
    
    def __enter__(self):
        return self
    
    def __exit__(self, *args):
        self.rollback()
    
    @abstractmethod
    def commit(self):
        """Commit the transaction and publish events"""
        pass
    
    @abstractmethod
    def rollback(self):
        """Rollback the transaction"""
        pass
    
    def collect_new_events(self):
        """Collect events from all aggregates that passed through repositories"""
        events_list = []
        for case in self.cases.seen:
            events_list.extend(case.events)
            case.events.clear()  # Clear events after collecting
        return events_list


class DjangoUnitOfWork(AbstractUnitOfWork):
    """Django implementation of Unit of Work using database transactions"""
    
    def __init__(self, tenant_id: Optional[str] = None):
        self.tenant_id = tenant_id
    
    def __enter__(self):
        # Start Django transaction
        transaction.set_autocommit(False)
        
        # Initialize repositories
        self.cases = DjangoCaseRepository()
        
        # Set tenant context for Row Level Security
        if self.tenant_id:
            self._set_tenant_context(self.tenant_id)
        
        return super().__enter__()
    
    def __exit__(self, *args):
        super().__exit__(*args)
        transaction.set_autocommit(True)
    
    def commit(self):
        """Commit transaction and update all seen aggregates"""
        # Update all aggregates that were modified
        for case in self.cases.seen:
            self.cases.update(case)
        
        # Commit the database transaction
        transaction.commit()
        
        logger.info(f"Committed transaction for tenant {self.tenant_id}")
    
    def rollback(self):
        """Rollback the database transaction"""
        transaction.rollback()
        logger.info(f"Rolled back transaction for tenant {self.tenant_id}")
    
    def _set_tenant_context(self, tenant_id: str):
        """Set PostgreSQL tenant context for Row Level Security"""
        from django.db import connection
        
        with connection.cursor() as cursor:
            cursor.execute("SET app.current_tenant = %s", [tenant_id])
    
    @contextmanager
    def tenant_context(self, tenant_id: str):
        """Context manager for tenant isolation"""
        old_tenant = self.tenant_id
        self.tenant_id = tenant_id
        self._set_tenant_context(tenant_id)
        try:
            yield
        finally:
            self.tenant_id = old_tenant
            if old_tenant:
                self._set_tenant_context(old_tenant)


class FakeUnitOfWork(AbstractUnitOfWork):
    """In-memory Unit of Work for testing"""
    
    def __init__(self, tenant_id: Optional[str] = None):
        self.tenant_id = tenant_id
        self.committed = False
    
    def __enter__(self):
        self.cases = FakeCaseRepository()
        return super().__enter__()
    
    def commit(self):
        self.committed = True
        logger.info(f"Fake commit for tenant {self.tenant_id}")
    
    def rollback(self):
        logger.info(f"Fake rollback for tenant {self.tenant_id}")
    
    @contextmanager 
    def tenant_context(self, tenant_id: str):
        """Mock tenant context for testing"""
        old_tenant = self.tenant_id
        self.tenant_id = tenant_id
        try:
            yield
        finally:
            self.tenant_id = old_tenant