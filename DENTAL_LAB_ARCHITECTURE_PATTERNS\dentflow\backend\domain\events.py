"""
DentFlow Domain Events
Events represent things that have happened in the domain
"""

from dataclasses import dataclass
from typing import Optional, List
from datetime import datetime
from abc import ABC


class Event(ABC):
    """Base class for all domain events"""
    pass


@dataclass
class CaseCreated(Event):
    case_id: str
    clinic_id: str
    patient_name: str
    tooth_number: str
    service_type: str
    priority: str
    tenant_id: str
    created_at: datetime
    files: List[str] = None


@dataclass
class CaseStatusChanged(Event):
    case_id: str
    from_status: str
    to_status: str
    technician_id: Optional[str]
    note: str
    tenant_id: str
    changed_at: datetime


@dataclass
class TechnicianAssigned(Event):
    case_id: str
    technician_id: str
    stage: str
    assigned_by: str
    tenant_id: str
    assigned_at: datetime


@dataclass
class FileUploaded(Event):
    case_id: str
    file_id: str
    filename: str
    file_type: str
    size_bytes: int
    uploaded_by: str
    tenant_id: str
    uploaded_at: datetime


@dataclass
class WorkflowStageCompleted(Event):
    case_id: str
    stage: str
    technician_id: str
    duration_minutes: int
    quality_score: Optional[int]
    notes: str
    tenant_id: str
    completed_at: datetime


@dataclass
class SLABreached(Event):
    case_id: str
    original_due_date: datetime
    actual_completion: datetime
    delay_hours: int
    stage: str
    tenant_id: str


@dataclass
class InvoiceGenerated(Event):
    invoice_id: str
    case_ids: List[str]
    clinic_id: str
    total_amount_cents: int
    tenant_id: str
    generated_at: datetime


@dataclass
class PaymentReceived(Event):
    invoice_id: str
    amount_cents: int
    payment_method: str
    stripe_payment_intent_id: str
    tenant_id: str
    received_at: datetime