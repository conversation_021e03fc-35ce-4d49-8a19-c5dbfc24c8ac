{"ast": null, "code": "var _jsxFileName = \"C:\\\\GPT4_PROJECTS\\\\DENTAL_LAB_ARCHITECTURE_PATTERNS\\\\dentflow\\\\frontend\\\\src\\\\components\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\n/**\n * DentFlow Dashboard Component\n * Displays real-time dental lab metrics and case summaries\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { CasesService } from '../api/casesService.ts';\nimport { useAuth } from '../context/AuthContext.tsx';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [stats, setStats] = useState({\n    active_cases: 0,\n    completed_today: 0,\n    pending_qc: 0,\n    revenue_today: 0,\n    overdue_cases: 0\n  });\n  const [recentCases, setRecentCases] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [backendStatus, setBackendStatus] = useState({\n    available: false,\n    mode: 'mock'\n  });\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const [dashboardStats, cases] = await Promise.all([CasesService.getDashboardStats(), CasesService.getCases()]);\n      setStats(dashboardStats);\n      setRecentCases(cases.slice(0, 5)); // Show last 5 cases\n    } catch (err) {\n      console.error('Error loading dashboard:', err);\n      setError('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  const getStatusColor = status => {\n    const colors = {\n      'received': '#2196f3',\n      'design': '#ff9800',\n      'milling': '#9c27b0',\n      'sintering': '#e91e63',\n      'qc': '#ff5722',\n      'shipped': '#4caf50',\n      'delivered': '#8bc34a',\n      'cancelled': '#757575'\n    };\n    return colors[status] || '#666';\n  };\n  const getPriorityColor = priority => {\n    const colors = {\n      'low': '#4caf50',\n      'normal': '#2196f3',\n      'urgent': '#ff9800',\n      'stat': '#f44336'\n    };\n    return colors[priority] || '#666';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '1.2rem',\n          color: '#666'\n        },\n        children: \"Loading dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '2rem',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#f44336',\n          marginBottom: '1rem'\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadDashboardData,\n        style: {\n          background: '#1976d2',\n          color: 'white',\n          border: 'none',\n          padding: '0.5rem 1rem',\n          borderRadius: '4px',\n          cursor: 'pointer'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '2rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          margin: 0,\n          color: '#1976d2'\n        },\n        children: \"\\uD83E\\uDDB7 DentFlow Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: '0.5rem 0',\n          color: '#666'\n        },\n        children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \" - \", user === null || user === void 0 ? void 0 : user.tenant_name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '1rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Active Cases\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#1976d2'\n          },\n          children: stats.active_cases\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"In production\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Completed Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#4caf50'\n          },\n          children: stats.completed_today\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Cases finished\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Pending QC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#ff9800'\n          },\n          children: stats.pending_qc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Quality control needed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Revenue Today\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#2196f3'\n          },\n          children: formatCurrency(stats.revenue_today)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Daily earnings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), stats.overdue_cases > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...cardStyle,\n          borderLeft: '4px solid #f44336'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 0.5rem 0',\n            color: '#666'\n          },\n          children: \"Overdue Cases\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '2rem',\n            fontWeight: 'bold',\n            color: '#f44336'\n          },\n          children: stats.overdue_cases\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            color: '#666'\n          },\n          children: \"Immediate attention needed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          marginBottom: '1rem',\n          color: '#333'\n        },\n        children: \"Recent Cases\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this), recentCases.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: cardStyle,\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#666',\n            textAlign: 'center'\n          },\n          children: \"No cases found. Create your first case to get started!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this) : recentCases.map(case_ => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          ...cardStyle,\n          marginBottom: '1rem',\n          borderLeft: `4px solid ${getStatusColor(case_.status)}`\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'flex-start',\n            marginBottom: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                margin: '0 0 0.25rem 0',\n                color: '#333'\n              },\n              children: [\"Case \", case_.id]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: 0,\n                color: '#666'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Patient:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 21\n              }, this), \" \", case_.patient_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'right'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                background: getPriorityColor(case_.priority),\n                color: 'white',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '4px',\n                fontSize: '0.8rem',\n                textTransform: 'uppercase'\n              },\n              children: case_.priority\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n            gap: '1rem',\n            color: '#666',\n            fontSize: '0.9rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 19\n            }, this), \" \", case_.service_type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Tooth:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 19\n            }, this), \" #\", case_.tooth_number]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 19\n            }, this), ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: getStatusColor(case_.status)\n              },\n              children: case_.current_stage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Due:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 19\n            }, this), ' ', case_.due_date ? /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: case_.is_overdue ? '#f44336' : '#666'\n              },\n              children: case_.days_until_due !== null ? case_.days_until_due >= 0 ? `${case_.days_until_due} days` : `${Math.abs(case_.days_until_due)} days overdue` : 'Today'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 21\n            }, this) : 'Not set']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 15\n        }, this)]\n      }, case_.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '2rem',\n        padding: '1rem',\n        background: '#e8f5e8',\n        borderRadius: '4px',\n        border: '1px solid #4caf50'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          color: '#2e7d32',\n          margin: '0 0 0.5rem 0'\n        },\n        children: \"\\u2705 Backend Integration: Connected\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: 0,\n          color: '#2e7d32'\n        },\n        children: \"React frontend successfully connected to Django REST API backend. Real-time data loading from DentFlow case management system.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 5\n  }, this);\n};\n\n// Styles\n_s(Dashboard, \"bf7iL0sBDWMd8ILPF7n0z3T4H+U=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nconst cardStyle = {\n  background: '#fff',\n  padding: '1.5rem',\n  borderRadius: '8px',\n  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  border: '1px solid #e0e0e0'\n};\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "CasesService", "useAuth", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "stats", "setStats", "active_cases", "completed_today", "pending_qc", "revenue_today", "overdue_cases", "recentCases", "setRecentCases", "loading", "setLoading", "error", "setError", "backendStatus", "setBackendStatus", "available", "mode", "loadDashboardData", "dashboardStats", "cases", "Promise", "all", "getDashboardStats", "getCases", "slice", "err", "console", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getStatusColor", "status", "colors", "getPriorityColor", "priority", "padding", "textAlign", "children", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "onClick", "background", "border", "borderRadius", "cursor", "margin", "username", "tenant_name", "display", "gridTemplateColumns", "gap", "cardStyle", "fontWeight", "borderLeft", "length", "map", "case_", "justifyContent", "alignItems", "id", "patient_name", "textTransform", "service_type", "tooth_number", "current_stage", "due_date", "is_overdue", "days_until_due", "Math", "abs", "marginTop", "_c", "boxShadow", "$RefreshReg$"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/src/components/Dashboard.tsx"], "sourcesContent": ["/**\n * DentFlow Dashboard Component\n * Displays real-time dental lab metrics and case summaries\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { CasesService } from '../api/casesService.ts';\nimport { Case } from '../api/types.ts';\nimport { useAuth } from '../context/AuthContext.tsx';\n\ninterface DashboardStats {\n  active_cases: number;\n  completed_today: number;\n  pending_qc: number;\n  revenue_today: number;\n  overdue_cases: number;\n}\n\nconst Dashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<DashboardStats>({\n    active_cases: 0,\n    completed_today: 0,\n    pending_qc: 0,\n    revenue_today: 0,\n    overdue_cases: 0,\n  });\n  const [recentCases, setRecentCases] = useState<Case[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n  const [backendStatus, setBackendStatus] = useState<{available: boolean; mode: string}>({\n    available: false,\n    mode: 'mock'\n  });\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      \n      const [dashboardStats, cases] = await Promise.all([\n        CasesService.getDashboardStats(),\n        CasesService.getCases(),\n      ]);\n      \n      setStats(dashboardStats);\n      setRecentCases(cases.slice(0, 5)); // Show last 5 cases\n    } catch (err: any) {\n      console.error('Error loading dashboard:', err);\n      setError('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(amount);\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors: Record<string, string> = {\n      'received': '#2196f3',\n      'design': '#ff9800',\n      'milling': '#9c27b0',\n      'sintering': '#e91e63',\n      'qc': '#ff5722',\n      'shipped': '#4caf50',\n      'delivered': '#8bc34a',\n      'cancelled': '#757575',\n    };\n    return colors[status] || '#666';\n  };\n\n  const getPriorityColor = (priority: string) => {\n    const colors: Record<string, string> = {\n      'low': '#4caf50',\n      'normal': '#2196f3',\n      'urgent': '#ff9800',\n      'stat': '#f44336',\n    };\n    return colors[priority] || '#666';\n  };\n\n  if (loading) {\n    return (\n      <div style={{ padding: '2rem', textAlign: 'center' }}>\n        <div style={{ fontSize: '1.2rem', color: '#666' }}>\n          Loading dashboard...\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{ padding: '2rem', textAlign: 'center' }}>\n        <div style={{ color: '#f44336', marginBottom: '1rem' }}>\n          {error}\n        </div>\n        <button \n          onClick={loadDashboardData}\n          style={{\n            background: '#1976d2',\n            color: 'white',\n            border: 'none',\n            padding: '0.5rem 1rem',\n            borderRadius: '4px',\n            cursor: 'pointer',\n          }}\n        >\n          Retry\n        </button>\n      </div>\n    );\n  }\n  return (\n    <div style={{ padding: '2rem' }}>\n      {/* Header */}\n      <div style={{ marginBottom: '2rem' }}>\n        <h1 style={{ margin: 0, color: '#1976d2' }}>\n          🦷 DentFlow Dashboard\n        </h1>\n        <p style={{ margin: '0.5rem 0', color: '#666' }}>\n          Welcome back, {user?.username} - {user?.tenant_name}\n        </p>\n      </div>\n\n      {/* Stats Grid */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n        gap: '1rem',\n        marginBottom: '2rem',\n      }}>\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Active Cases</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1976d2' }}>\n            {stats.active_cases}\n          </div>\n          <small style={{ color: '#666' }}>In production</small>\n        </div>\n\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Completed Today</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#4caf50' }}>\n            {stats.completed_today}\n          </div>\n          <small style={{ color: '#666' }}>Cases finished</small>\n        </div>\n\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Pending QC</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ff9800' }}>\n            {stats.pending_qc}\n          </div>\n          <small style={{ color: '#666' }}>Quality control needed</small>\n        </div>\n\n        <div style={cardStyle}>\n          <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Revenue Today</h3>\n          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2196f3' }}>\n            {formatCurrency(stats.revenue_today)}\n          </div>\n          <small style={{ color: '#666' }}>Daily earnings</small>\n        </div>\n\n        {stats.overdue_cases > 0 && (\n          <div style={{ ...cardStyle, borderLeft: '4px solid #f44336' }}>\n            <h3 style={{ margin: '0 0 0.5rem 0', color: '#666' }}>Overdue Cases</h3>\n            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f44336' }}>\n              {stats.overdue_cases}\n            </div>\n            <small style={{ color: '#666' }}>Immediate attention needed</small>\n          </div>\n        )}\n      </div>\n      {/* Recent Cases */}\n      <div>\n        <h2 style={{ marginBottom: '1rem', color: '#333' }}>Recent Cases</h2>\n        {recentCases.length === 0 ? (\n          <div style={cardStyle}>\n            <p style={{ margin: 0, color: '#666', textAlign: 'center' }}>\n              No cases found. Create your first case to get started!\n            </p>\n          </div>\n        ) : (\n          recentCases.map((case_) => (\n            <div key={case_.id} style={{\n              ...cardStyle,\n              marginBottom: '1rem',\n              borderLeft: `4px solid ${getStatusColor(case_.status)}`,\n            }}>\n              <div style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start',\n                marginBottom: '0.5rem',\n              }}>\n                <div>\n                  <h4 style={{ margin: '0 0 0.25rem 0', color: '#333' }}>\n                    Case {case_.id}\n                  </h4>\n                  <p style={{ margin: 0, color: '#666' }}>\n                    <strong>Patient:</strong> {case_.patient_name}\n                  </p>\n                </div>\n                <div style={{ textAlign: 'right' }}>\n                  <span style={{\n                    background: getPriorityColor(case_.priority),\n                    color: 'white',\n                    padding: '0.25rem 0.5rem',\n                    borderRadius: '4px',\n                    fontSize: '0.8rem',\n                    textTransform: 'uppercase',\n                  }}>\n                    {case_.priority}\n                  </span>\n                </div>\n              </div>\n              \n              <div style={{\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',\n                gap: '1rem',\n                color: '#666',\n                fontSize: '0.9rem',\n              }}>\n                <div>\n                  <strong>Type:</strong> {case_.service_type}\n                </div>\n                <div>\n                  <strong>Tooth:</strong> #{case_.tooth_number}\n                </div>\n                <div>\n                  <strong>Status:</strong>{' '}\n                  <span style={{ color: getStatusColor(case_.status) }}>\n                    {case_.current_stage}\n                  </span>\n                </div>\n                <div>\n                  <strong>Due:</strong>{' '}\n                  {case_.due_date ? (\n                    <span style={{ \n                      color: case_.is_overdue ? '#f44336' : '#666' \n                    }}>\n                      {case_.days_until_due !== null ? (\n                        case_.days_until_due >= 0 ? \n                          `${case_.days_until_due} days` : \n                          `${Math.abs(case_.days_until_due!)} days overdue`\n                      ) : 'Today'}\n                    </span>\n                  ) : (\n                    'Not set'\n                  )}\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      <div style={{\n        marginTop: '2rem',\n        padding: '1rem',\n        background: '#e8f5e8',\n        borderRadius: '4px',\n        border: '1px solid #4caf50',\n      }}>\n        <h4 style={{ color: '#2e7d32', margin: '0 0 0.5rem 0' }}>\n          ✅ Backend Integration: Connected\n        </h4>\n        <p style={{ margin: 0, color: '#2e7d32' }}>\n          React frontend successfully connected to Django REST API backend.\n          Real-time data loading from DentFlow case management system.\n        </p>\n      </div>\n    </div>\n  );\n};\n\n// Styles\nconst cardStyle: React.CSSProperties = {\n  background: '#fff',\n  padding: '1.5rem',\n  borderRadius: '8px',\n  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  border: '1px solid #e0e0e0',\n};\n\nexport default Dashboard;"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,wBAAwB;AAErD,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUrD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACM,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAiB;IACjDW,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,CAAC;IAClBC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAqC;IACrFwB,SAAS,EAAE,KAAK;IAChBC,IAAI,EAAE;EACR,CAAC,CAAC;EAEFxB,SAAS,CAAC,MAAM;IACdyB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAM,CAACM,cAAc,EAAEC,KAAK,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChD5B,YAAY,CAAC6B,iBAAiB,CAAC,CAAC,EAChC7B,YAAY,CAAC8B,QAAQ,CAAC,CAAC,CACxB,CAAC;MAEFtB,QAAQ,CAACiB,cAAc,CAAC;MACxBV,cAAc,CAACW,KAAK,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBC,OAAO,CAACf,KAAK,CAAC,0BAA0B,EAAEc,GAAG,CAAC;MAC9Cb,QAAQ,CAAC,+BAA+B,CAAC;IAC3C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAMiB,cAAc,GAAIC,MAAc,IAAK;IACzC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,MAAM,CAAC;EACnB,CAAC;EAED,MAAMM,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,MAA8B,GAAG;MACrC,UAAU,EAAE,SAAS;MACrB,QAAQ,EAAE,SAAS;MACnB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,IAAI,EAAE,SAAS;MACf,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,MAAM,CAAC,IAAI,MAAM;EACjC,CAAC;EAED,MAAME,gBAAgB,GAAIC,QAAgB,IAAK;IAC7C,MAAMF,MAA8B,GAAG;MACrC,KAAK,EAAE,SAAS;MAChB,QAAQ,EAAE,SAAS;MACnB,QAAQ,EAAE,SAAS;MACnB,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,MAAM,CAACE,QAAQ,CAAC,IAAI,MAAM;EACnC,CAAC;EAED,IAAI7B,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKmC,KAAK,EAAE;QAAEQ,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,eACnD7C,OAAA;QAAKmC,KAAK,EAAE;UAAEW,QAAQ,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAEnD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIpC,KAAK,EAAE;IACT,oBACEf,OAAA;MAAKmC,KAAK,EAAE;QAAEQ,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACnD7C,OAAA;QAAKmC,KAAK,EAAE;UAAEY,KAAK,EAAE,SAAS;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAP,QAAA,EACpD9B;MAAK;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnD,OAAA;QACEqD,OAAO,EAAEhC,iBAAkB;QAC3Bc,KAAK,EAAE;UACLmB,UAAU,EAAE,SAAS;UACrBP,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdZ,OAAO,EAAE,aAAa;UACtBa,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAZ,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EACA,oBACEnD,OAAA;IAAKmC,KAAK,EAAE;MAAEQ,OAAO,EAAE;IAAO,CAAE;IAAAE,QAAA,gBAE9B7C,OAAA;MAAKmC,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAO,CAAE;MAAAP,QAAA,gBACnC7C,OAAA;QAAImC,KAAK,EAAE;UAAEuB,MAAM,EAAE,CAAC;UAAEX,KAAK,EAAE;QAAU,CAAE;QAAAF,QAAA,EAAC;MAE5C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLnD,OAAA;QAAGmC,KAAK,EAAE;UAAEuB,MAAM,EAAE,UAAU;UAAEX,KAAK,EAAE;QAAO,CAAE;QAAAF,QAAA,GAAC,gBACjC,EAAC1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,QAAQ,EAAC,KAAG,EAACxD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,WAAW;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnD,OAAA;MAAKmC,KAAK,EAAE;QACV0B,OAAO,EAAE,MAAM;QACfC,mBAAmB,EAAE,sCAAsC;QAC3DC,GAAG,EAAE,MAAM;QACXX,YAAY,EAAE;MAChB,CAAE;MAAAP,QAAA,gBACA7C,OAAA;QAAKmC,KAAK,EAAE6B,SAAU;QAAAnB,QAAA,gBACpB7C,OAAA;UAAImC,KAAK,EAAE;YAAEuB,MAAM,EAAE,cAAc;YAAEX,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEnD,OAAA;UAAKmC,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpEzC,KAAK,CAACE;QAAY;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACNnD,OAAA;UAAOmC,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENnD,OAAA;QAAKmC,KAAK,EAAE6B,SAAU;QAAAnB,QAAA,gBACpB7C,OAAA;UAAImC,KAAK,EAAE;YAAEuB,MAAM,EAAE,cAAc;YAAEX,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EnD,OAAA;UAAKmC,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpEzC,KAAK,CAACG;QAAe;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACNnD,OAAA;UAAOmC,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAENnD,OAAA;QAAKmC,KAAK,EAAE6B,SAAU;QAAAnB,QAAA,gBACpB7C,OAAA;UAAImC,KAAK,EAAE;YAAEuB,MAAM,EAAE,cAAc;YAAEX,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEnD,OAAA;UAAKmC,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpEzC,KAAK,CAACI;QAAU;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACNnD,OAAA;UAAOmC,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAsB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eAENnD,OAAA;QAAKmC,KAAK,EAAE6B,SAAU;QAAAnB,QAAA,gBACpB7C,OAAA;UAAImC,KAAK,EAAE;YAAEuB,MAAM,EAAE,cAAc;YAAEX,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEnD,OAAA;UAAKmC,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpEd,cAAc,CAAC3B,KAAK,CAACK,aAAa;QAAC;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNnD,OAAA;UAAOmC,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,EAEL/C,KAAK,CAACM,aAAa,GAAG,CAAC,iBACtBV,OAAA;QAAKmC,KAAK,EAAE;UAAE,GAAG6B,SAAS;UAAEE,UAAU,EAAE;QAAoB,CAAE;QAAArB,QAAA,gBAC5D7C,OAAA;UAAImC,KAAK,EAAE;YAAEuB,MAAM,EAAE,cAAc;YAAEX,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxEnD,OAAA;UAAKmC,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEmB,UAAU,EAAE,MAAM;YAAElB,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EACpEzC,KAAK,CAACM;QAAa;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNnD,OAAA;UAAOmC,KAAK,EAAE;YAAEY,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAA0B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENnD,OAAA;MAAA6C,QAAA,gBACE7C,OAAA;QAAImC,KAAK,EAAE;UAAEiB,YAAY,EAAE,MAAM;UAAEL,KAAK,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACpExC,WAAW,CAACwD,MAAM,KAAK,CAAC,gBACvBnE,OAAA;QAAKmC,KAAK,EAAE6B,SAAU;QAAAnB,QAAA,eACpB7C,OAAA;UAAGmC,KAAK,EAAE;YAAEuB,MAAM,EAAE,CAAC;YAAEX,KAAK,EAAE,MAAM;YAAEH,SAAS,EAAE;UAAS,CAAE;UAAAC,QAAA,EAAC;QAE7D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,GAENxC,WAAW,CAACyD,GAAG,CAAEC,KAAK,iBACpBrE,OAAA;QAAoBmC,KAAK,EAAE;UACzB,GAAG6B,SAAS;UACZZ,YAAY,EAAE,MAAM;UACpBc,UAAU,EAAE,aAAa5B,cAAc,CAAC+B,KAAK,CAAC9B,MAAM,CAAC;QACvD,CAAE;QAAAM,QAAA,gBACA7C,OAAA;UAAKmC,KAAK,EAAE;YACV0B,OAAO,EAAE,MAAM;YACfS,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE,YAAY;YACxBnB,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,gBACA7C,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAImC,KAAK,EAAE;gBAAEuB,MAAM,EAAE,eAAe;gBAAEX,KAAK,EAAE;cAAO,CAAE;cAAAF,QAAA,GAAC,OAChD,EAACwB,KAAK,CAACG,EAAE;YAAA;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLnD,OAAA;cAAGmC,KAAK,EAAE;gBAAEuB,MAAM,EAAE,CAAC;gBAAEX,KAAK,EAAE;cAAO,CAAE;cAAAF,QAAA,gBACrC7C,OAAA;gBAAA6C,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACkB,KAAK,CAACI,YAAY;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNnD,OAAA;YAAKmC,KAAK,EAAE;cAAES,SAAS,EAAE;YAAQ,CAAE;YAAAC,QAAA,eACjC7C,OAAA;cAAMmC,KAAK,EAAE;gBACXmB,UAAU,EAAEb,gBAAgB,CAAC4B,KAAK,CAAC3B,QAAQ,CAAC;gBAC5CK,KAAK,EAAE,OAAO;gBACdJ,OAAO,EAAE,gBAAgB;gBACzBa,YAAY,EAAE,KAAK;gBACnBV,QAAQ,EAAE,QAAQ;gBAClB4B,aAAa,EAAE;cACjB,CAAE;cAAA7B,QAAA,EACCwB,KAAK,CAAC3B;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnD,OAAA;UAAKmC,KAAK,EAAE;YACV0B,OAAO,EAAE,MAAM;YACfC,mBAAmB,EAAE,sCAAsC;YAC3DC,GAAG,EAAE,MAAM;YACXhB,KAAK,EAAE,MAAM;YACbD,QAAQ,EAAE;UACZ,CAAE;UAAAD,QAAA,gBACA7C,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAA6C,QAAA,EAAQ;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACkB,KAAK,CAACM,YAAY;UAAA;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNnD,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAA6C,QAAA,EAAQ;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,MAAE,EAACkB,KAAK,CAACO,YAAY;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNnD,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAA6C,QAAA,EAAQ;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,eAC5BnD,OAAA;cAAMmC,KAAK,EAAE;gBAAEY,KAAK,EAAET,cAAc,CAAC+B,KAAK,CAAC9B,MAAM;cAAE,CAAE;cAAAM,QAAA,EAClDwB,KAAK,CAACQ;YAAa;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNnD,OAAA;YAAA6C,QAAA,gBACE7C,OAAA;cAAA6C,QAAA,EAAQ;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EACxBkB,KAAK,CAACS,QAAQ,gBACb9E,OAAA;cAAMmC,KAAK,EAAE;gBACXY,KAAK,EAAEsB,KAAK,CAACU,UAAU,GAAG,SAAS,GAAG;cACxC,CAAE;cAAAlC,QAAA,EACCwB,KAAK,CAACW,cAAc,KAAK,IAAI,GAC5BX,KAAK,CAACW,cAAc,IAAI,CAAC,GACvB,GAAGX,KAAK,CAACW,cAAc,OAAO,GAC9B,GAAGC,IAAI,CAACC,GAAG,CAACb,KAAK,CAACW,cAAe,CAAC,eAAe,GACjD;YAAO;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,GAEP,SACD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GApEEkB,KAAK,CAACG,EAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqEb,CACN,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENnD,OAAA;MAAKmC,KAAK,EAAE;QACVgD,SAAS,EAAE,MAAM;QACjBxC,OAAO,EAAE,MAAM;QACfW,UAAU,EAAE,SAAS;QACrBE,YAAY,EAAE,KAAK;QACnBD,MAAM,EAAE;MACV,CAAE;MAAAV,QAAA,gBACA7C,OAAA;QAAImC,KAAK,EAAE;UAAEY,KAAK,EAAE,SAAS;UAAEW,MAAM,EAAE;QAAe,CAAE;QAAAb,QAAA,EAAC;MAEzD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLnD,OAAA;QAAGmC,KAAK,EAAE;UAAEuB,MAAM,EAAE,CAAC;UAAEX,KAAK,EAAE;QAAU,CAAE;QAAAF,QAAA,EAAC;MAG3C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAjD,EAAA,CA5QMD,SAAmB;EAAA,QACNH,OAAO;AAAA;AAAAsF,EAAA,GADpBnF,SAAmB;AA6QzB,MAAM+D,SAA8B,GAAG;EACrCV,UAAU,EAAE,MAAM;EAClBX,OAAO,EAAE,QAAQ;EACjBa,YAAY,EAAE,KAAK;EACnB6B,SAAS,EAAE,2BAA2B;EACtC9B,MAAM,EAAE;AACV,CAAC;AAED,eAAetD,SAAS;AAAC,IAAAmF,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}