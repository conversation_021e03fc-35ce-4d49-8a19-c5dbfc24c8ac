"""
Django management command to generate test cases
Creates realistic test data for development and testing
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from apps.cases.models import Tenant, Clinic
from apps.users.models import User
from bootstrap import get_message_bus
from domain import commands
import random
from datetime import datetime, timedelta


class Command(BaseCommand):
    help = 'Generate test cases for development and testing'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-subdomain',
            type=str,
            default='demo',
            help='Subdomain of the tenant to create cases for'
        )
        parser.add_argument(
            '--count',
            type=int,
            default=50,
            help='Number of test cases to create'
        )
        parser.add_argument(
            '--days-back',
            type=int,
            default=30,
            help='Create cases spanning this many days back'
        )
    
    def handle(self, *args, **options):
        """Generate test cases"""
        
        try:
            # Get tenant
            tenant = Tenant.objects.get(
                subdomain=options['tenant_subdomain'],
                is_active=True
            )
            
            # Get clinics for this tenant
            clinics = list(Clinic.objects.filter(tenant=tenant))
            if not clinics:
                self.stdout.write(
                    self.style.ERROR('No clinics found for tenant. Run setup_demo first.')
                )
                return
            
            # Generate test cases
            created_count = self._generate_test_cases(
                tenant,
                clinics,
                options['count'],
                options['days_back']
            )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created {created_count} test cases for {tenant.name}'
                )
            )
            
        except Tenant.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(
                    f'Tenant with subdomain "{options["tenant_subdomain"]}" not found'
                )
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to generate test cases: {e}')
            )
            raise
    
    def _generate_test_cases(self, tenant, clinics, count, days_back):
        """Generate test cases with realistic data"""
        
        # Test data patterns
        service_types = ['crown', 'bridge', 'denture', 'implant', 'veneer']
        priorities = ['low', 'normal', 'urgent', 'stat']
        priority_weights = [0.2, 0.6, 0.15, 0.05]  # Most cases are normal priority
        
        # Realistic patient names
        first_names = [
            'John', 'Jane', 'Michael', 'Sarah', 'David', 'Emily', 'Robert', 'Lisa',
            'James', 'Maria', 'William', 'Jennifer', 'Richard', 'Linda', 'Charles',
            'Patricia', 'Joseph', 'Susan', 'Thomas', 'Nancy'
        ]
        last_names = [
            'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller',
            'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez',
            'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin'
        ]
        
        # Valid tooth numbers (ISO 3950)
        tooth_numbers = [str(i) for i in range(11, 49)]
        
        message_bus = get_message_bus(tenant_id=str(tenant.id))
        created_count = 0
        
        for i in range(count):
            try:
                # Random creation date within the specified range
                days_ago = random.randint(0, days_back)
                created_date = datetime.now() - timedelta(days=days_ago)
                
                # Generate case data
                clinic = random.choice(clinics)
                patient_name = f"{random.choice(first_names)} {random.choice(last_names)}"
                tooth_number = random.choice(tooth_numbers)
                service_type = random.choice(service_types)
                priority = random.choices(priorities, weights=priority_weights)[0]
                
                # Create command
                command = commands.CreateCase(
                    clinic_id=str(clinic.id),
                    patient_name=patient_name,
                    tooth_number=tooth_number,
                    service_type=service_type,
                    priority=priority,
                    notes=f"Test case #{i+1} - Generated automatically",
                    tenant_id=str(tenant.id)
                )
                
                # Execute command
                case_id = message_bus.handle(command)
                created_count += 1
                
                if (i + 1) % 10 == 0:
                    self.stdout.write(f'Created {i + 1}/{count} cases...')
                
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'Failed to create case #{i+1}: {e}')
                )
                continue
        
        return created_count