# Django Core
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-extensions==3.2.3
django-debug-toolbar==4.2.0

# Database
psycopg2-binary==2.9.9
django-redis==5.4.0

# Authentication & Authorization
djangorestframework-simplejwt==5.3.0
django-guardian==2.4.0

# API Documentation
drf-spectacular==0.26.5

# File Storage
boto3==1.34.0
django-storages==1.14.2
Pillow==10.1.0

# Async Tasks
celery==5.3.4
redis==5.0.1
django-celery-beat==2.5.0
django-celery-results==2.5.1

# Environment Management
python-decouple==3.8
dj-database-url==2.1.0

# Data Validation
marshmallow==3.20.1
pydantic==2.5.0

# Testing
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0
factory-boy==3.3.0
model-bakery==1.17.0
pytest-mock==3.12.0

# Monitoring & Logging
sentry-sdk==1.38.0
django-prometheus==2.3.1
structlog==23.2.0

# Security
cryptography==41.0.7
django-ratelimit==4.1.0
django-csp==3.7

# Payments
stripe==7.8.0

# Email
django-anymail==10.2

# Utils
python-slugify==8.0.1
python-dateutil==2.8.2
pytz==2023.3
uuid==1.30

# Development
pre-commit==3.6.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
django-stubs==4.2.7

# Deployment
gunicorn==21.2.0
whitenoise==6.6.0
uvicorn==0.24.0

# Workflow Engine
croniter==2.0.1

# File Processing (for STL/dental files)
trimesh==4.0.5
numpy==1.24.4

# Business Intelligence
pandas==2.1.4
reportlab==4.0.7