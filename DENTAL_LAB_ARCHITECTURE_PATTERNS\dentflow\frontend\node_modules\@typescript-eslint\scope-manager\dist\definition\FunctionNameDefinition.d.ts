import type { TSESTree } from '@typescript-eslint/types';
import { DefinitionBase } from './DefinitionBase';
import { DefinitionType } from './DefinitionType';
declare class FunctionNameDefinition extends DefinitionBase<DefinitionType.FunctionName, TSESTree.FunctionDeclaration | TSESTree.FunctionExpression | TSESTree.TSDeclareFunction | TSESTree.TSEmptyBodyFunctionExpression, null, TSESTree.Identifier> {
    constructor(name: TSESTree.Identifier, node: FunctionNameDefinition['node']);
    readonly isTypeDefinition = false;
    readonly isVariableDefinition = true;
}
export { FunctionNameDefinition };
//# sourceMappingURL=FunctionNameDefinition.d.ts.map