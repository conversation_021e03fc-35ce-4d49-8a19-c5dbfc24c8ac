{"version": 3, "file": "no-unnecessary-type-assertion.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-type-assertion.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,qCAMiB;AACjB,+CAAiC;AAEjC,8CAAgC;AAShC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,uEAAuE;YACzE,WAAW,EAAE,OAAO;YACpB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,oBAAoB,EAClB,oFAAoF;YACtF,uBAAuB,EACrB,+FAA+F;SAClG;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,aAAa,EAAE;wBACb,WAAW,EAAE,iCAAiC;wBAC9C,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;iBACF;aACF;SACF;QACD,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,CAAC,EAAE,CAAC;IACpB,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,MAAM,eAAe,GAAG,cAAc,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAEpE;;;WAGG;QACH,SAAS,gBAAgB,CAAC,IAAmB;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAExC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC3B,OAAO,KAAK,CAAC;aACd;YACD,IAAI,CAAC,GAAG,CAAC,CAAC;YAEV,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACjC,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAEhC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;oBACtB,IAAI,CAAC,KAAK,CAAC,EAAE;wBACX,0DAA0D;wBAC1D,OAAO,KAAK,CAAC;qBACd;oBACD,MAAM;iBACP;aACF;YACD,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACjC,IAAI,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;oBACtD,OAAO,KAAK,CAAC,CAAC,iEAAiE;iBAChF;aACF;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;WAEG;QACH,SAAS,4BAA4B,CAAC,IAAmB;YACvD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACvD,IAAI,CAAC,WAAW,EAAE;gBAChB,+EAA+E;gBAC/E,OAAO,IAAI,CAAC;aACb;YAED;YACE,iEAAiE;YACjE,IAAA,uCAA6B,EAAC,eAAe,EAAE,kBAAkB,CAAC;gBAClE,2DAA2D;gBAC3D,sEAAsE;gBACtE,IAAA,+BAAqB,EAAC,WAAW,CAAC;gBAClC,2BAA2B;gBAC3B,WAAW,CAAC,WAAW,KAAK,SAAS;gBACrC,WAAW,CAAC,gBAAgB,KAAK,SAAS;gBAC1C,WAAW,CAAC,IAAI,KAAK,SAAS,EAC9B;gBACA,kEAAkE;gBAClE,MAAM,eAAe,GAAG,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACtE,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC9D,IAAI,eAAe,KAAK,IAAI,EAAE;oBAC5B,iDAAiD;oBACjD,6FAA6F;oBAC7F,EAAE;oBACF,6CAA6C;oBAC7C,uDAAuD;oBACvD,OAAO,IAAI,CAAC;iBACb;aACF;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,gBAAgB,CAAC,IAAuB;YAC/C,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC5C,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAChD,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,CAC/B,CAAC;QACJ,CAAC;QAED,OAAO;YACL,mBAAmB,CAAC,IAAI;;gBACtB,IACE,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,oBAAoB;oBACzD,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,GAAG,EAC5B;oBACA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;wBAC7B,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,yBAAyB;4BACpC,GAAG,CAAC,KAAK;gCACP,OAAO,KAAK,CAAC,WAAW,CAAC;oCACvB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;oCACxB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;iCACd,CAAC,CAAC;4BACL,CAAC;yBACF,CAAC,CAAC;qBACJ;oBACD,wDAAwD;oBACxD,2EAA2E;oBAC3E,8EAA8E;oBAC9E,qBAAqB;oBACrB,OAAO;iBACR;gBAED,MAAM,YAAY,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAEpE,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAC5C,OAAO,EACP,YAAY,CAAC,UAAU,CACxB,CAAC;gBAEF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBAC9B,IAAI,4BAA4B,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;wBACzD,OAAO;qBACR;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,sBAAsB;wBACjC,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,WAAW,CAAC;gCACvB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;gCACxB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;6BACd,CAAC,CAAC;wBACL,CAAC;qBACF,CAAC,CAAC;iBACJ;qBAAM;oBACL,+BAA+B;oBAC/B,+EAA+E;oBAE/E,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;oBACrE,IAAI,cAAc,EAAE;wBAClB,kFAAkF;wBAClF,sCAAsC;wBACtC,MAAM,qBAAqB,GAAG,IAAI,CAAC,aAAa,CAC9C,IAAI,EACJ,EAAE,CAAC,SAAS,CAAC,SAAS,CACvB,CAAC;wBACF,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CACzC,IAAI,EACJ,EAAE,CAAC,SAAS,CAAC,IAAI,CAClB,CAAC;wBAEF,MAAM,+BAA+B,GAAG,IAAI,CAAC,aAAa,CACxD,cAAc,EACd,EAAE,CAAC,SAAS,CAAC,SAAS,CACvB,CAAC;wBACF,MAAM,0BAA0B,GAAG,IAAI,CAAC,aAAa,CACnD,cAAc,EACd,EAAE,CAAC,SAAS,CAAC,IAAI,CAClB,CAAC;wBAEF,mDAAmD;wBACnD,gFAAgF;wBAChF,MAAM,gBAAgB,GAAG,qBAAqB;4BAC5C,CAAC,CAAC,+BAA+B;4BACjC,CAAC,CAAC,IAAI,CAAC;wBACT,MAAM,WAAW,GAAG,gBAAgB;4BAClC,CAAC,CAAC,0BAA0B;4BAC5B,CAAC,CAAC,IAAI,CAAC;wBAET,IAAI,gBAAgB,IAAI,WAAW,EAAE;4BACnC,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI;gCACJ,SAAS,EAAE,yBAAyB;gCACpC,GAAG,CAAC,KAAK;oCACP,OAAO,KAAK,CAAC,WAAW,CAAC;wCACvB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;wCACxB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;qCACd,CAAC,CAAC;gCACL,CAAC;6BACF,CAAC,CAAC;yBACJ;qBACF;iBACF;YACH,CAAC;YACD,iCAAiC,CAC/B,IAAwD;;gBAExD,IACE,CAAA,MAAA,OAAO,CAAC,aAAa,0CAAE,QAAQ,CAC7B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CACxC;oBACD,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,EACrC;oBACA,OAAO;iBACR;gBAED,MAAM,YAAY,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACpE,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAEzD,IACE,IAAA,uBAAa,EAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC;oBAC7C,CAAC,IAAA,sBAAY,EAAC,QAAQ,CAAC;wBACrB,CAAC,IAAA,yBAAe,EAAC,QAAQ,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;4BAC9C,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAChC;oBACA,mEAAmE;oBACnE,+DAA+D;oBAC/D,OAAO;iBACR;gBAED,MAAM,UAAU,GAAG,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAEtE,IAAI,UAAU,KAAK,QAAQ,EAAE;oBAC3B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,sBAAsB;wBACjC,GAAG,CAAC,KAAK;4BACP,IAAI,YAAY,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,uBAAuB,EAAE;gCAC/D,MAAM,mBAAmB,GAAG,UAAU,CAAC,aAAa,CAClD,IAAI,CAAC,cAAc,CACpB,CAAC;gCACF,OAAO,CAAA,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,CAAE,KAAK,MAAK,GAAG;oCACvC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC;wCAChB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;wCACb,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;qCAC7B,CAAC;oCACJ,CAAC,CAAC,IAAI,CAAC;6BACV;4BACD,OAAO,KAAK,CAAC,WAAW,CAAC;gCACvB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;gCAC5B,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;6BACd,CAAC,CAAC;wBACL,CAAC;qBACF,CAAC,CAAC;iBACJ;gBAED,qDAAqD;YACvD,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}