{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"componentsProps\", \"max\", \"renderSurplus\", \"slotProps\", \"spacing\", \"total\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { isFragment } from 'react-is';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Avatar, { avatarClasses } from '../Avatar';\nimport avatarGroupClasses, { getAvatarGroupUtilityClass } from './avatarGroupClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SPACINGS = {\n  small: -16,\n  medium: null\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    avatar: ['avatar']\n  };\n  return composeClasses(slots, getAvatarGroupUtilityClass, classes);\n};\nconst AvatarGroupRoot = styled('div', {\n  name: 'MuiAvatarGroup',\n  slot: 'Root',\n  overridesResolver: (props, styles) => _extends({\n    [`& .${avatarGroupClasses.avatar}`]: styles.avatar\n  }, styles.root)\n})(({\n  theme,\n  ownerState\n}) => {\n  const marginValue = ownerState.spacing && SPACINGS[ownerState.spacing] !== undefined ? SPACINGS[ownerState.spacing] : -ownerState.spacing;\n  return {\n    [`& .${avatarClasses.root}`]: {\n      border: `2px solid ${(theme.vars || theme).palette.background.default}`,\n      boxSizing: 'content-box',\n      marginLeft: marginValue != null ? marginValue : -8,\n      '&:last-child': {\n        marginLeft: 0\n      }\n    },\n    display: 'flex',\n    flexDirection: 'row-reverse'\n  };\n});\nconst AvatarGroup = /*#__PURE__*/React.forwardRef(function AvatarGroup(inProps, ref) {\n  var _slotProps$additional;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAvatarGroup'\n  });\n  const {\n      children: childrenProp,\n      className,\n      component = 'div',\n      componentsProps = {},\n      max = 5,\n      renderSurplus,\n      slotProps = {},\n      spacing = 'medium',\n      total,\n      variant = 'circular'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  let clampedMax = max < 2 ? 2 : max;\n  const ownerState = _extends({}, props, {\n    max,\n    spacing,\n    component,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const children = React.Children.toArray(childrenProp).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The AvatarGroup component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  });\n  const totalAvatars = total || children.length;\n  if (totalAvatars === clampedMax) {\n    clampedMax += 1;\n  }\n  clampedMax = Math.min(totalAvatars + 1, clampedMax);\n  const maxAvatars = Math.min(children.length, clampedMax - 1);\n  const extraAvatars = Math.max(totalAvatars - clampedMax, totalAvatars - maxAvatars, 0);\n  const extraAvatarsElement = renderSurplus ? renderSurplus(extraAvatars) : `+${extraAvatars}`;\n  const additionalAvatarSlotProps = (_slotProps$additional = slotProps.additionalAvatar) != null ? _slotProps$additional : componentsProps.additionalAvatar;\n  return /*#__PURE__*/_jsxs(AvatarGroupRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: [extraAvatars ? /*#__PURE__*/_jsx(Avatar, _extends({\n      variant: variant\n    }, additionalAvatarSlotProps, {\n      className: clsx(classes.avatar, additionalAvatarSlotProps == null ? void 0 : additionalAvatarSlotProps.className),\n      children: extraAvatarsElement\n    })) : null, children.slice(0, maxAvatars).reverse().map(child => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        className: clsx(child.props.className, classes.avatar),\n        variant: child.props.variant || variant\n      });\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? AvatarGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The avatars to stack.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Max avatars to show before +x.\n   * @default 5\n   */\n  max: chainPropTypes(PropTypes.number, props => {\n    if (props.max < 2) {\n      return new Error(['MUI: The prop `max` should be equal to 2 or above.', 'A value below is clamped to 2.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * custom renderer of extraAvatars\n   * @param {number} surplus number of extra avatars\n   * @returns {React.ReactNode} custom element to display\n   */\n  renderSurplus: PropTypes.func,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Spacing between avatars.\n   * @default 'medium'\n   */\n  spacing: PropTypes.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.number]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The total number of avatars. Used for calculating the number of extra avatars.\n   * @default children.length\n   */\n  total: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rounded', 'square']), PropTypes.string])\n} : void 0;\nexport default AvatarGroup;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "isFragment", "clsx", "chainPropTypes", "composeClasses", "styled", "useDefaultProps", "Avatar", "avatarClasses", "avatarGroupClasses", "getAvatarGroupUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "SPACINGS", "small", "medium", "useUtilityClasses", "ownerState", "classes", "slots", "root", "avatar", "AvatarGroupRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "marginValue", "spacing", "undefined", "border", "vars", "palette", "background", "default", "boxSizing", "marginLeft", "display", "flexDirection", "AvatarGroup", "forwardRef", "inProps", "ref", "_slotProps$additional", "children", "childrenProp", "className", "component", "componentsProps", "max", "renderSurplus", "slotProps", "total", "variant", "other", "clampedMax", "Children", "toArray", "filter", "child", "process", "env", "NODE_ENV", "console", "error", "join", "isValidElement", "totalAvatars", "length", "Math", "min", "maxAvatars", "extraAvatars", "extraAvatarsElement", "additionalAvatarSlotProps", "additionalAvatar", "as", "slice", "reverse", "map", "cloneElement", "propTypes", "node", "object", "string", "elementType", "shape", "number", "Error", "func", "oneOfType", "oneOf", "sx", "arrayOf", "bool"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/node_modules/@mui/material/AvatarGroup/AvatarGroup.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"componentsProps\", \"max\", \"renderSurplus\", \"slotProps\", \"spacing\", \"total\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { isFragment } from 'react-is';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Avatar, { avatarClasses } from '../Avatar';\nimport avatarGroupClasses, { getAvatarGroupUtilityClass } from './avatarGroupClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SPACINGS = {\n  small: -16,\n  medium: null\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    avatar: ['avatar']\n  };\n  return composeClasses(slots, getAvatarGroupUtilityClass, classes);\n};\nconst AvatarGroupRoot = styled('div', {\n  name: 'MuiAvatarGroup',\n  slot: 'Root',\n  overridesResolver: (props, styles) => _extends({\n    [`& .${avatarGroupClasses.avatar}`]: styles.avatar\n  }, styles.root)\n})(({\n  theme,\n  ownerState\n}) => {\n  const marginValue = ownerState.spacing && SPACINGS[ownerState.spacing] !== undefined ? SPACINGS[ownerState.spacing] : -ownerState.spacing;\n  return {\n    [`& .${avatarClasses.root}`]: {\n      border: `2px solid ${(theme.vars || theme).palette.background.default}`,\n      boxSizing: 'content-box',\n      marginLeft: marginValue != null ? marginValue : -8,\n      '&:last-child': {\n        marginLeft: 0\n      }\n    },\n    display: 'flex',\n    flexDirection: 'row-reverse'\n  };\n});\nconst AvatarGroup = /*#__PURE__*/React.forwardRef(function AvatarGroup(inProps, ref) {\n  var _slotProps$additional;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAvatarGroup'\n  });\n  const {\n      children: childrenProp,\n      className,\n      component = 'div',\n      componentsProps = {},\n      max = 5,\n      renderSurplus,\n      slotProps = {},\n      spacing = 'medium',\n      total,\n      variant = 'circular'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  let clampedMax = max < 2 ? 2 : max;\n  const ownerState = _extends({}, props, {\n    max,\n    spacing,\n    component,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const children = React.Children.toArray(childrenProp).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The AvatarGroup component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  });\n  const totalAvatars = total || children.length;\n  if (totalAvatars === clampedMax) {\n    clampedMax += 1;\n  }\n  clampedMax = Math.min(totalAvatars + 1, clampedMax);\n  const maxAvatars = Math.min(children.length, clampedMax - 1);\n  const extraAvatars = Math.max(totalAvatars - clampedMax, totalAvatars - maxAvatars, 0);\n  const extraAvatarsElement = renderSurplus ? renderSurplus(extraAvatars) : `+${extraAvatars}`;\n  const additionalAvatarSlotProps = (_slotProps$additional = slotProps.additionalAvatar) != null ? _slotProps$additional : componentsProps.additionalAvatar;\n  return /*#__PURE__*/_jsxs(AvatarGroupRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: [extraAvatars ? /*#__PURE__*/_jsx(Avatar, _extends({\n      variant: variant\n    }, additionalAvatarSlotProps, {\n      className: clsx(classes.avatar, additionalAvatarSlotProps == null ? void 0 : additionalAvatarSlotProps.className),\n      children: extraAvatarsElement\n    })) : null, children.slice(0, maxAvatars).reverse().map(child => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        className: clsx(child.props.className, classes.avatar),\n        variant: child.props.variant || variant\n      });\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? AvatarGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The avatars to stack.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Max avatars to show before +x.\n   * @default 5\n   */\n  max: chainPropTypes(PropTypes.number, props => {\n    if (props.max < 2) {\n      return new Error(['MUI: The prop `max` should be equal to 2 or above.', 'A value below is clamped to 2.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * custom renderer of extraAvatars\n   * @param {number} surplus number of extra avatars\n   * @returns {React.ReactNode} custom element to display\n   */\n  renderSurplus: PropTypes.func,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Spacing between avatars.\n   * @default 'medium'\n   */\n  spacing: PropTypes.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.number]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The total number of avatars. Used for calculating the number of extra avatars.\n   * @default children.length\n   */\n  total: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rounded', 'square']), PropTypes.string])\n} : void 0;\nexport default AvatarGroup;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,EAAE,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,CAAC;AAC/I,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,IAAIC,aAAa,QAAQ,WAAW;AACjD,OAAOC,kBAAkB,IAAIC,0BAA0B,QAAQ,sBAAsB;AACrF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,QAAQ,GAAG;EACfC,KAAK,EAAE,CAAC,EAAE;EACVC,MAAM,EAAE;AACV,CAAC;AACD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,MAAM,EAAE,CAAC,QAAQ;EACnB,CAAC;EACD,OAAOnB,cAAc,CAACiB,KAAK,EAAEX,0BAA0B,EAAEU,OAAO,CAAC;AACnE,CAAC;AACD,MAAMI,eAAe,GAAGnB,MAAM,CAAC,KAAK,EAAE;EACpCoB,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKhC,QAAQ,CAAC;IAC7C,CAAC,MAAMY,kBAAkB,CAACc,MAAM,EAAE,GAAGM,MAAM,CAACN;EAC9C,CAAC,EAAEM,MAAM,CAACP,IAAI;AAChB,CAAC,CAAC,CAAC,CAAC;EACFQ,KAAK;EACLX;AACF,CAAC,KAAK;EACJ,MAAMY,WAAW,GAAGZ,UAAU,CAACa,OAAO,IAAIjB,QAAQ,CAACI,UAAU,CAACa,OAAO,CAAC,KAAKC,SAAS,GAAGlB,QAAQ,CAACI,UAAU,CAACa,OAAO,CAAC,GAAG,CAACb,UAAU,CAACa,OAAO;EACzI,OAAO;IACL,CAAC,MAAMxB,aAAa,CAACc,IAAI,EAAE,GAAG;MAC5BY,MAAM,EAAE,aAAa,CAACJ,KAAK,CAACK,IAAI,IAAIL,KAAK,EAAEM,OAAO,CAACC,UAAU,CAACC,OAAO,EAAE;MACvEC,SAAS,EAAE,aAAa;MACxBC,UAAU,EAAET,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAG,CAAC,CAAC;MAClD,cAAc,EAAE;QACdS,UAAU,EAAE;MACd;IACF,CAAC;IACDC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE;EACjB,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,WAAW,GAAG,aAAa5C,KAAK,CAAC6C,UAAU,CAAC,SAASD,WAAWA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACnF,IAAIC,qBAAqB;EACzB,MAAMnB,KAAK,GAAGtB,eAAe,CAAC;IAC5BsB,KAAK,EAAEiB,OAAO;IACdpB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFuB,QAAQ,EAAEC,YAAY;MACtBC,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBC,eAAe,GAAG,CAAC,CAAC;MACpBC,GAAG,GAAG,CAAC;MACPC,aAAa;MACbC,SAAS,GAAG,CAAC,CAAC;MACdvB,OAAO,GAAG,QAAQ;MAClBwB,KAAK;MACLC,OAAO,GAAG;IACZ,CAAC,GAAG7B,KAAK;IACT8B,KAAK,GAAG9D,6BAA6B,CAACgC,KAAK,EAAE9B,SAAS,CAAC;EACzD,IAAI6D,UAAU,GAAGN,GAAG,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG;EAClC,MAAMlC,UAAU,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAE+B,KAAK,EAAE;IACrCyB,GAAG;IACHrB,OAAO;IACPmB,SAAS;IACTM;EACF,CAAC,CAAC;EACF,MAAMrC,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM6B,QAAQ,GAAGjD,KAAK,CAAC6D,QAAQ,CAACC,OAAO,CAACZ,YAAY,CAAC,CAACa,MAAM,CAACC,KAAK,IAAI;IACpE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIjE,UAAU,CAAC8D,KAAK,CAAC,EAAE;QACrBI,OAAO,CAACC,KAAK,CAAC,CAAC,sEAAsE,EAAE,sCAAsC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC5I;IACF;IACA,OAAO,aAAatE,KAAK,CAACuE,cAAc,CAACP,KAAK,CAAC;EACjD,CAAC,CAAC;EACF,MAAMQ,YAAY,GAAGf,KAAK,IAAIR,QAAQ,CAACwB,MAAM;EAC7C,IAAID,YAAY,KAAKZ,UAAU,EAAE;IAC/BA,UAAU,IAAI,CAAC;EACjB;EACAA,UAAU,GAAGc,IAAI,CAACC,GAAG,CAACH,YAAY,GAAG,CAAC,EAAEZ,UAAU,CAAC;EACnD,MAAMgB,UAAU,GAAGF,IAAI,CAACC,GAAG,CAAC1B,QAAQ,CAACwB,MAAM,EAAEb,UAAU,GAAG,CAAC,CAAC;EAC5D,MAAMiB,YAAY,GAAGH,IAAI,CAACpB,GAAG,CAACkB,YAAY,GAAGZ,UAAU,EAAEY,YAAY,GAAGI,UAAU,EAAE,CAAC,CAAC;EACtF,MAAME,mBAAmB,GAAGvB,aAAa,GAAGA,aAAa,CAACsB,YAAY,CAAC,GAAG,IAAIA,YAAY,EAAE;EAC5F,MAAME,yBAAyB,GAAG,CAAC/B,qBAAqB,GAAGQ,SAAS,CAACwB,gBAAgB,KAAK,IAAI,GAAGhC,qBAAqB,GAAGK,eAAe,CAAC2B,gBAAgB;EACzJ,OAAO,aAAajE,KAAK,CAACU,eAAe,EAAE3B,QAAQ,CAAC;IAClDmF,EAAE,EAAE7B,SAAS;IACbhC,UAAU,EAAEA,UAAU;IACtB+B,SAAS,EAAEhD,IAAI,CAACkB,OAAO,CAACE,IAAI,EAAE4B,SAAS,CAAC;IACxCJ,GAAG,EAAEA;EACP,CAAC,EAAEY,KAAK,EAAE;IACRV,QAAQ,EAAE,CAAC4B,YAAY,GAAG,aAAahE,IAAI,CAACL,MAAM,EAAEV,QAAQ,CAAC;MAC3D4D,OAAO,EAAEA;IACX,CAAC,EAAEqB,yBAAyB,EAAE;MAC5B5B,SAAS,EAAEhD,IAAI,CAACkB,OAAO,CAACG,MAAM,EAAEuD,yBAAyB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,yBAAyB,CAAC5B,SAAS,CAAC;MACjHF,QAAQ,EAAE6B;IACZ,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE7B,QAAQ,CAACiC,KAAK,CAAC,CAAC,EAAEN,UAAU,CAAC,CAACO,OAAO,CAAC,CAAC,CAACC,GAAG,CAACpB,KAAK,IAAI;MAC/D,OAAO,aAAahE,KAAK,CAACqF,YAAY,CAACrB,KAAK,EAAE;QAC5Cb,SAAS,EAAEhD,IAAI,CAAC6D,KAAK,CAACnC,KAAK,CAACsB,SAAS,EAAE9B,OAAO,CAACG,MAAM,CAAC;QACtDkC,OAAO,EAAEM,KAAK,CAACnC,KAAK,CAAC6B,OAAO,IAAIA;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvB,WAAW,CAAC0C,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACErC,QAAQ,EAAEhD,SAAS,CAACsF,IAAI;EACxB;AACF;AACA;EACElE,OAAO,EAAEpB,SAAS,CAACuF,MAAM;EACzB;AACF;AACA;EACErC,SAAS,EAAElD,SAAS,CAACwF,MAAM;EAC3B;AACF;AACA;AACA;EACErC,SAAS,EAAEnD,SAAS,CAACyF,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACErC,eAAe,EAAEpD,SAAS,CAAC0F,KAAK,CAAC;IAC/BX,gBAAgB,EAAE/E,SAAS,CAACuF;EAC9B,CAAC,CAAC;EACF;AACF;AACA;AACA;EACElC,GAAG,EAAElD,cAAc,CAACH,SAAS,CAAC2F,MAAM,EAAE/D,KAAK,IAAI;IAC7C,IAAIA,KAAK,CAACyB,GAAG,GAAG,CAAC,EAAE;MACjB,OAAO,IAAIuC,KAAK,CAAC,CAAC,oDAAoD,EAAE,gCAAgC,CAAC,CAACvB,IAAI,CAAC,IAAI,CAAC,CAAC;IACvH;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEf,aAAa,EAAEtD,SAAS,CAAC6F,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEtC,SAAS,EAAEvD,SAAS,CAAC0F,KAAK,CAAC;IACzBX,gBAAgB,EAAE/E,SAAS,CAACuF;EAC9B,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEvD,OAAO,EAAEhC,SAAS,CAAC8F,SAAS,CAAC,CAAC9F,SAAS,CAAC+F,KAAK,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAE/F,SAAS,CAAC2F,MAAM,CAAC,CAAC;EACtF;AACF;AACA;EACEK,EAAE,EAAEhG,SAAS,CAAC8F,SAAS,CAAC,CAAC9F,SAAS,CAACiG,OAAO,CAACjG,SAAS,CAAC8F,SAAS,CAAC,CAAC9F,SAAS,CAAC6F,IAAI,EAAE7F,SAAS,CAACuF,MAAM,EAAEvF,SAAS,CAACkG,IAAI,CAAC,CAAC,CAAC,EAAElG,SAAS,CAAC6F,IAAI,EAAE7F,SAAS,CAACuF,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACE/B,KAAK,EAAExD,SAAS,CAAC2F,MAAM;EACvB;AACF;AACA;AACA;EACElC,OAAO,EAAEzD,SAAS,CAAC,sCAAsC8F,SAAS,CAAC,CAAC9F,SAAS,CAAC+F,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE/F,SAAS,CAACwF,MAAM,CAAC;AAC3I,CAAC,GAAG,KAAK,CAAC;AACV,eAAe7C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}