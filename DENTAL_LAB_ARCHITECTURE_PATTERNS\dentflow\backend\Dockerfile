# Multi-stage Docker build for DentFlow Backend
# Stage 1: Base dependencies
FROM python:3.11-slim AS base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONIOENCODING=utf-8 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    postgresql-client \
    curl \
    netcat-openbsd \
    && rm -rf /var/lib/apt/lists/*

# Create application directory
WORKDIR /app

# Stage 2: Development
FROM base AS development

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install development dependencies
RUN pip install --no-cache-dir \
    ipython \
    django-debug-toolbar \
    pytest-django \
    pytest-cov

# Copy application code
COPY . .

# Create directories for logs and media
RUN mkdir -p logs media static

# Create non-root user
RUN groupadd -r dentflow && useradd -r -g dentflow dentflow
RUN chown -R dentflow:dentflow /app
USER dentflow

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health/ || exit 1

# Default command
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]

# Stage 3: Production
FROM base AS production

# Install gunicorn for production
RUN pip install --no-cache-dir gunicorn uvicorn

# Copy requirements and install
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create directories
RUN mkdir -p logs media static

# Collect static files (only if manage.py exists and Django is configured)
RUN python manage.py collectstatic --noinput || echo "Skipping collectstatic"

# Create non-root user
RUN groupadd -r dentflow && useradd -r -g dentflow dentflow
RUN chown -R dentflow:dentflow /app
USER dentflow

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health/ || exit 1

# Production command
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--worker-class", "sync", "--timeout", "120", "--keepalive", "2", "dentflow_project.wsgi:application"]
