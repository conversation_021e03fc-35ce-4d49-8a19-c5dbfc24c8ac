"""
Django Signal Handlers for DentFlow
Connects Django ORM events to domain event processing
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from apps.cases.models import Case as DjangoCase
from bootstrap import get_message_bus
from domain import events
import logging


logger = logging.getLogger(__name__)


@receiver(post_save, sender=DjangoCase)
def handle_case_saved(sender, instance, created, **kwargs):
    """Handle case save events and publish domain events"""
    
    try:
        # Get message bus for this tenant
        if not instance.tenant_id:
            return
        
        message_bus = get_message_bus(tenant_id=str(instance.tenant_id))
        
        if created:
            # Convert Django model to domain case to get events
            domain_case = instance.to_domain()
            
            # Process any events generated by the domain case
            for event in domain_case.events:
                try:
                    message_bus.handle(event)
                    logger.debug(f"Processed event {type(event).__name__} for case {instance.id}")
                except Exception as e:
                    logger.error(f"Failed to process event {type(event).__name__}: {e}")
            
            # Clear events to avoid reprocessing
            domain_case.events.clear()
            
        logger.info(f"Processed Django signals for case {instance.id}")
        
    except Exception as e:
        logger.error(f"Error in case signal handler: {e}")


@receiver(post_delete, sender=DjangoCase)
def handle_case_deleted(sender, instance, **kwargs):
    """Handle case deletion (soft delete should be used instead)"""
    
    try:
        logger.warning(f"Case {instance.id} was hard deleted - this should use soft delete")
        
        # In a real implementation, you might want to:
        # 1. Archive case data
        # 2. Update related records
        # 3. Send notifications
        
    except Exception as e:
        logger.error(f"Error in case deletion handler: {e}")


# Domain event handlers - these would be triggered by the message bus
def handle_case_created_event(event: events.CaseCreated):
    """Handle CaseCreated domain event"""
    
    try:
        logger.info(f"Processing CaseCreated event for case {event.case_id}")
        
        # Store event in event log for audit trail
        from apps.cases.models import CaseEvent
        
        CaseEvent.objects.create(
            case_id=event.case_id,
            event_type='CaseCreated',
            event_data={
                'clinic_id': event.clinic_id,
                'patient_name': event.patient_name,
                'tooth_number': event.tooth_number,
                'service_type': event.service_type,
                'priority': event.priority,
                'tenant_id': event.tenant_id,
            }
        )
        
        # Trigger notifications, integrations, etc.
        # send_case_created_notifications.delay(event.case_id)
        
    except Exception as e:
        logger.error(f"Error handling CaseCreated event: {e}")


def handle_case_status_changed_event(event: events.CaseStatusChanged):
    """Handle CaseStatusChanged domain event"""
    
    try:
        logger.info(f"Processing CaseStatusChanged event for case {event.case_id}")
        
        # Store event in event log
        from apps.cases.models import CaseEvent
        
        CaseEvent.objects.create(
            case_id=event.case_id,
            event_type='CaseStatusChanged',
            event_data={
                'from_status': event.from_status,
                'to_status': event.to_status,
                'technician_id': event.technician_id,
                'note': event.note,
                'tenant_id': event.tenant_id,
            }
        )
        
        # Trigger status-specific actions
        if event.to_status == 'qc':
            # send_qc_notification.delay(event.case_id)
            pass
        elif event.to_status == 'shipped':
            # send_shipping_notification.delay(event.case_id)
            pass
        
    except Exception as e:
        logger.error(f"Error handling CaseStatusChanged event: {e}")


def handle_technician_assigned_event(event: events.TechnicianAssigned):
    """Handle TechnicianAssigned domain event"""
    
    try:
        logger.info(f"Processing TechnicianAssigned event for case {event.case_id}")
        
        # Store event in event log
        from apps.cases.models import CaseEvent
        
        CaseEvent.objects.create(
            case_id=event.case_id,
            event_type='TechnicianAssigned',
            event_data={
                'technician_id': event.technician_id,
                'stage': event.stage,
                'assigned_by': event.assigned_by,
                'tenant_id': event.tenant_id,
            }
        )
        
        # Notify technician
        # send_assignment_notification.delay(event.technician_id, event.case_id)
        
    except Exception as e:
        logger.error(f"Error handling TechnicianAssigned event: {e}")


def handle_file_uploaded_event(event: events.FileUploaded):
    """Handle FileUploaded domain event"""
    
    try:
        logger.info(f"Processing FileUploaded event for case {event.case_id}")
        
        # Store event in event log
        from apps.cases.models import CaseEvent
        
        CaseEvent.objects.create(
            case_id=event.case_id,
            event_type='FileUploaded',
            event_data={
                'file_id': event.file_id,
                'filename': event.filename,
                'file_type': event.file_type,
                'size_bytes': event.size_bytes,
                'uploaded_by': event.uploaded_by,
                'tenant_id': event.tenant_id,
            }
        )
        
        # Process file (virus scan, metadata extraction, etc.)
        # process_uploaded_file.delay(event.file_id, event.case_id)
        
    except Exception as e:
        logger.error(f"Error handling FileUploaded event: {e}")


def handle_sla_breached_event(event: events.SLABreached):
    """Handle SLABreached domain event"""
    
    try:
        logger.warning(f"SLA breached for case {event.case_id}")
        
        # Store event in event log
        from apps.cases.models import CaseEvent
        
        CaseEvent.objects.create(
            case_id=event.case_id,
            event_type='SLABreached',
            event_data={
                'original_due_date': event.original_due_date.isoformat(),
                'actual_completion': event.actual_completion.isoformat(),
                'delay_hours': event.delay_hours,
                'stage': event.stage,
                'tenant_id': event.tenant_id,
            }
        )
        
        # Send escalation notifications
        # send_sla_breach_alert.delay(event.case_id, event.delay_hours)
        
    except Exception as e:
        logger.error(f"Error handling SLABreached event: {e}")