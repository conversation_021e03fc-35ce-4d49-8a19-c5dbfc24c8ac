# Backend .dockerignore
# Exclude unnecessary files from Docker build context

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Documentation
README.md
*.md

# Tests
.pytest_cache/
.coverage
htmlcov/

# Development files
.env.local
.env.development
.env.testing

# Media and static files (handled by volumes)
media/
staticfiles/

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
