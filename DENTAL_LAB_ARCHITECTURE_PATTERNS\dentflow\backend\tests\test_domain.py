"""
Unit Tests for Domain Model
These tests are fast and isolated - no database, no external dependencies
Testing the business logic in pure domain objects
"""

import pytest
from datetime import datetime, timedelta
from domain.model import (
    Case, CaseId, ToothNumber, Priority, CaseStatus, WorkflowStage,
    InvalidStageTransition, InvalidOperation
)
from domain import events


class TestCaseId:
    """Test Case ID value object"""
    
    def test_case_id_generation(self):
        """Test that case IDs are generated correctly"""
        case_id = CaseId.generate()
        assert case_id.value.startswith("LAB-")
        assert len(case_id.value) > 10
    
    def test_case_id_with_custom_prefix(self):
        """Test case ID generation with custom prefix"""
        case_id = CaseId.generate("DENTAL")
        assert case_id.value.startswith("DENTAL-")


class TestToothNumber:
    """Test Tooth Number value object"""
    
    def test_valid_tooth_numbers(self):
        """Test valid tooth numbers (ISO 3950)"""
        valid_numbers = ["11", "12", "21", "48"]
        for number in valid_numbers:
            tooth = ToothNumber(number)
            assert tooth.value == number
    
    def test_invalid_tooth_numbers(self):
        """Test invalid tooth numbers raise ValueError"""
        invalid_numbers = ["10", "49", "00", "abc", ""]
        for number in invalid_numbers:
            with pytest.raises(ValueError):
                ToothNumber(number)


class TestWorkflowStage:
    """Test Workflow Stage value object"""
    
    def test_workflow_stage_creation(self):
        """Test workflow stage creation"""
        stage = WorkflowStage(
            name="Design",
            department="CAD",
            estimated_duration_minutes=45,
            auto_assign=True
        )
        
        assert stage.name == "Design"
        assert stage.department == "CAD"
        assert stage.estimated_duration_minutes == 45
        assert stage.auto_assign is True
        assert stage.requires_quality_check is False


class TestCase:
    """Test Case aggregate root"""
    
    def test_case_creation(self):
        """Test creating a new case"""
        case_id = CaseId.generate()
        tooth_number = ToothNumber("11")
        
        case = Case(
            case_id=case_id,
            clinic_id="clinic-123",
            patient_name="John Doe",
            tooth_number=tooth_number,
            service_type="crown",
            tenant_id="tenant-123",
            priority=Priority.NORMAL
        )
        
        assert case.case_id == case_id
        assert case.clinic_id == "clinic-123"
        assert case.patient_name == "John Doe"
        assert case.tooth_number == tooth_number
        assert case.service_type == "crown"
        assert case.tenant_id == "tenant-123"
        assert case.priority == Priority.NORMAL
        assert case.status == CaseStatus.RECEIVED
        assert len(case.events) == 1  # CaseCreated event
        assert isinstance(case.events[0], events.CaseCreated)
    
    def test_assign_workflow(self):
        """Test assigning workflow to case"""
        case = self._create_test_case()
        stages = [
            WorkflowStage("Design", "CAD", 45),
            WorkflowStage("Milling", "CAM", 15),
        ]
        
        case.assign_workflow(stages)
        
        assert case.workflow_stages == stages
        assert case.due_date is not None
    
    def test_advance_to_next_stage(self):
        """Test advancing case to next stage"""
        case = self._create_test_case()
        stages = [
            WorkflowStage("Design", "CAD", 45),
            WorkflowStage("Milling", "CAM", 15),
        ]
        case.assign_workflow(stages)
        case.assigned_technician_id = "tech-001"
        
        # Clear initial events
        case.events.clear()
        
        case.advance_to_next_stage("tech-001", "Design completed")
        
        assert case.current_stage_index == 1
        assert case.assigned_technician_id is None  # Reset for next stage
        assert len(case.events) == 1
        assert isinstance(case.events[0], events.CaseStatusChanged)
    
    def test_cannot_advance_without_technician(self):
        """Test that case cannot advance without assigned technician"""
        case = self._create_test_case()
        stages = [WorkflowStage("Design", "CAD", 45)]
        case.assign_workflow(stages)
        
        with pytest.raises(InvalidStageTransition):
            case.advance_to_next_stage("tech-001")
    
    def test_assign_technician(self):
        """Test assigning technician to case"""
        case = self._create_test_case()
        case.events.clear()
        
        case.assign_technician("tech-001", "admin-user")
        
        assert case.assigned_technician_id == "tech-001"
        assert len(case.events) == 1
        assert isinstance(case.events[0], events.TechnicianAssigned)
    
    def test_cannot_assign_technician_to_completed_case(self):
        """Test that technician cannot be assigned to completed case"""
        case = self._create_test_case()
        case.status = CaseStatus.DELIVERED
        
        with pytest.raises(InvalidOperation):
            case.assign_technician("tech-001", "admin-user")
    
    def test_upload_file(self):
        """Test uploading file to case"""
        case = self._create_test_case()
        case.events.clear()
        
        case.upload_file(
            file_id="file-123",
            filename="impression.stl",
            file_type="application/stl",
            size_bytes=1024000,
            uploaded_by="dentist-001"
        )
        
        assert "file-123" in case.files
        assert len(case.events) == 1
        assert isinstance(case.events[0], events.FileUploaded)
    
    def test_is_overdue(self):
        """Test overdue detection"""
        case = self._create_test_case()
        
        # Not overdue without due date
        assert not case.is_overdue()
        
        # Set due date in the past
        case.due_date = datetime.utcnow() - timedelta(hours=1)
        assert case.is_overdue()
        
        # Set due date in the future
        case.due_date = datetime.utcnow() + timedelta(hours=1)
        assert not case.is_overdue()
    
    def test_days_until_due(self):
        """Test days until due calculation"""
        case = self._create_test_case()
        
        # No due date
        assert case.days_until_due() is None
        
        # Due in 3 days
        case.due_date = datetime.utcnow() + timedelta(days=3, hours=5)
        assert case.days_until_due() == 3
        
        # Overdue by 2 days
        case.due_date = datetime.utcnow() - timedelta(days=2)
        assert case.days_until_due() == -2
    
    def test_priority_affects_due_date_calculation(self):
        """Test that priority affects due date calculation"""
        case_urgent = self._create_test_case(priority=Priority.URGENT)
        case_normal = self._create_test_case(priority=Priority.NORMAL)
        case_low = self._create_test_case(priority=Priority.LOW)
        
        stages = [WorkflowStage("Design", "CAD", 60)]  # 1 hour
        
        case_urgent.assign_workflow(stages)
        case_normal.assign_workflow(stages)
        case_low.assign_workflow(stages)
        
        # Urgent cases should have earlier due dates
        assert case_urgent.due_date < case_normal.due_date
        assert case_normal.due_date < case_low.due_date
    
    def _create_test_case(self, priority=Priority.NORMAL):
        """Helper to create test case"""
        case_id = CaseId.generate()
        tooth_number = ToothNumber("11")
        
        return Case(
            case_id=case_id,
            clinic_id="clinic-123",
            patient_name="John Doe",
            tooth_number=tooth_number,
            service_type="crown",
            tenant_id="tenant-123",
            priority=priority
        )