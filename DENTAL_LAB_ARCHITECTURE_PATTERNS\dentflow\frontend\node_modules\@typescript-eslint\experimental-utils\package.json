{"name": "@typescript-eslint/experimental-utils", "version": "5.62.0", "description": "(Experimental) Utilities for working with TypeScript + ESLint together", "keywords": ["eslint", "typescript", "estree"], "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "files": ["dist", "_ts3.4", "package.json", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/experimental-utils"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -b tsconfig.build.json", "postbuild": "downlevel-dts dist _ts3.4/dist", "clean": "tsc -b tsconfig.build.json --clean", "postclean": "rimraf dist && rimraf _ts3.4 && rimraf coverage", "format": "prettier --write \"./**/*.{ts,mts,cts,tsx,js,mjs,cjs,jsx,json,md,css}\" --ignore-path ../../.prettierignore", "lint": "nx lint", "typecheck": "tsc -p tsconfig.json --noEmit"}, "dependencies": {"@typescript-eslint/utils": "5.62.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "devDependencies": {"typescript": "*"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "gitHead": "cba0d113bba1bbcee69149c954dc6bd4c658c714"}