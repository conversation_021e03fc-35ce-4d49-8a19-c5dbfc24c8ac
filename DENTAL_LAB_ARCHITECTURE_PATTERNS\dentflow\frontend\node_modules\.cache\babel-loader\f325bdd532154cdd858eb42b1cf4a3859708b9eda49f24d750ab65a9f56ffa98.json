{"ast": null, "code": "'use client';\n\nexport { default } from './Box';\nexport { default as boxClasses } from './boxClasses';\nexport * from './boxClasses';", "map": {"version": 3, "names": ["default", "boxClasses"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/node_modules/@mui/material/Box/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Box';\nexport { default as boxClasses } from './boxClasses';\nexport * from './boxClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,OAAO;AAC/B,SAASA,OAAO,IAAIC,UAAU,QAAQ,cAAc;AACpD,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}