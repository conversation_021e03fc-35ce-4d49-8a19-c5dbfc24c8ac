"""
Billing Models for DentFlow
Financial management, invoicing, and payment tracking
"""

from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid


class PriceList(models.Model):
    """
    Price lists for different services offered by dental lab
    """
    
    CURRENCY_CHOICES = [
        ('USD', 'US Dollar'),
        ('EUR', 'Euro'),
        ('GBP', 'British Pound'),
        ('CAD', 'Canadian Dollar'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='price_lists')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD')
    is_active = models.BooleanField(default=True)
    effective_date = models.DateField()
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['tenant', 'is_active']),
            models.Index(fields=['effective_date']),
        ]
        unique_together = ['tenant', 'name']
    
    def __str__(self):
        return f'{self.name} ({self.currency})'


class ServicePrice(models.Model):
    """
    Individual service pricing within a price list
    """
    
    SERVICE_TYPE_CHOICES = [
        ('crown', 'Crown'),
        ('bridge', 'Bridge'),
        ('denture', 'Denture'),
        ('implant', 'Implant Crown'),
        ('veneer', 'Veneer'),
        ('inlay', 'Inlay'),
        ('onlay', 'Onlay'),
        ('nightguard', 'Night Guard'),
        ('retainer', 'Retainer'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    price_list = models.ForeignKey(PriceList, on_delete=models.CASCADE, related_name='service_prices')
    service_type = models.CharField(max_length=50, choices=SERVICE_TYPE_CHOICES)
    description = models.CharField(max_length=255, blank=True)
    base_price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['price_list', 'service_type']),
            models.Index(fields=['is_active']),
        ]
        unique_together = ['price_list', 'service_type']
    
    def __str__(self):
        return f'{self.service_type} - ${self.base_price}'


class Invoice(models.Model):
    """
    Invoices sent to dental clinics
    """
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('cancelled', 'Cancelled'),
    ]
    
    CURRENCY_CHOICES = [
        ('USD', 'US Dollar'),
        ('EUR', 'Euro'),
        ('GBP', 'British Pound'),
        ('CAD', 'Canadian Dollar'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    invoice_number = models.CharField(max_length=50, unique=True)
    tenant = models.ForeignKey('tenants.Tenant', on_delete=models.CASCADE, related_name='invoices')
    clinic = models.ForeignKey('cases.Clinic', on_delete=models.CASCADE, related_name='invoices')
    
    # Financial details
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD')
    
    # Status and dates
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    due_date = models.DateField()
    
    # Additional information
    notes = models.TextField(blank=True)
    payment_terms = models.CharField(max_length=255, default='Net 30')
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['tenant', 'status']),
            models.Index(fields=['clinic', 'status']),
            models.Index(fields=['due_date']),
            models.Index(fields=['invoice_number']),
        ]
        ordering = ['-created_at']
    
    def save(self, *args, **kwargs):
        """Auto-generate invoice number if not provided"""
        if not self.invoice_number:
            # Generate invoice number: INV-YYYY-NNNN
            from django.utils import timezone
            year = timezone.now().year
            last_invoice = Invoice.objects.filter(
                invoice_number__startswith=f'INV-{year}'
            ).order_by('-invoice_number').first()
            
            if last_invoice:
                last_number = int(last_invoice.invoice_number.split('-')[-1])
                new_number = last_number + 1
            else:
                new_number = 1
            
            self.invoice_number = f'INV-{year}-{new_number:04d}'
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f'{self.invoice_number} - {self.clinic.name}'


class InvoiceItem(models.Model):
    """
    Individual line items on an invoice
    """
    
    SERVICE_TYPE_CHOICES = ServicePrice.SERVICE_TYPE_CHOICES
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='items')
    case = models.ForeignKey('cases.Case', on_delete=models.CASCADE, null=True, blank=True)
    
    # Item details
    service_type = models.CharField(max_length=50, choices=SERVICE_TYPE_CHOICES)
    description = models.CharField(max_length=255)
    quantity = models.PositiveIntegerField(default=1)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def save(self, *args, **kwargs):
        """Auto-calculate total price"""
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f'{self.description} - ${self.total_price}'


class Payment(models.Model):
    """
    Payment records for invoices
    """
    
    PAYMENT_METHOD_CHOICES = [
        ('credit_card', 'Credit Card'),
        ('debit_card', 'Debit Card'),
        ('bank_transfer', 'Bank Transfer'),
        ('check', 'Check'),
        ('cash', 'Cash'),
        ('other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ]
    
    CURRENCY_CHOICES = Invoice.CURRENCY_CHOICES
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    payment_id = models.CharField(max_length=50, unique=True)
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='payments')
    
    # Payment details
    amount = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)
    
    # Status and dates
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    payment_date = models.DateTimeField()
    
    # Transaction details
    reference_number = models.CharField(max_length=100, blank=True)
    processor_response = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['invoice', 'status']),
            models.Index(fields=['payment_date']),
            models.Index(fields=['payment_id']),
        ]
        ordering = ['-payment_date']
    
    def save(self, *args, **kwargs):
        """Auto-generate payment ID if not provided"""
        if not self.payment_id:
            # Generate payment ID: PAY-YYYYMMDD-NNNN
            from django.utils import timezone
            today = timezone.now().date()
            date_str = today.strftime('%Y%m%d')
            
            last_payment = Payment.objects.filter(
                payment_id__startswith=f'PAY-{date_str}'
            ).order_by('-payment_id').first()
            
            if last_payment:
                last_number = int(last_payment.payment_id.split('-')[-1])
                new_number = last_number + 1
            else:
                new_number = 1
            
            self.payment_id = f'PAY-{date_str}-{new_number:04d}'
        
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f'{self.payment_id} - ${self.amount}'