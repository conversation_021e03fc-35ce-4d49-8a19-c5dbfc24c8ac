{"version": 3, "file": "explicitReturnTypeUtils.js", "sourceRoot": "", "sources": ["../../src/util/explicitReturnTypeUtils.ts"], "names": [], "mappings": ";;;AACA,oDAAuE;AAEvE,yCAAsE;AACtE,6DAA0D;AAO1D;;;;;GAKG;AACH,SAAS,sCAAsC,CAC7C,IAAmB;IAEnB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAC5E,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAS,sCAAsC,CAC7C,IAAmB;IAEnB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CACzE,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,SAAS,qBAAqB,CAC5B,IAAmB;IAEnB,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,CAAC;AACpD,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,0BAA0B,CACjC,QAAmC;IAEnC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ,EAAE;QAC1D,OAAO,KAAK,CAAC;KACd;IACD,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,+CAA+C;IACnF,wBAAwB,CAAC,IACvB,CAAC,UAAU;QACX,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EACnD;QACA,OAAO,KAAK,CAAC;KACd;IAED,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,+CAA+C;IACjF,wBAAwB,CAAC,IAAI,CAAC,MAAM,EAAE;QACpC,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CACL,IAAA,0BAAe,EAAC,MAAM,CAAC;QACvB,sCAAsC,CAAC,MAAM,CAAC;QAC9C,sCAAsC,CAAC,MAAM,CAAC;QAC9C,kBAAkB,CAAC,MAAM,CAAC;QAC1B,0BAA0B,CAAC,MAAM,CAAC,CACnC,CAAC;AACJ,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAS,uCAAuC,CAAC,EAC/C,IAAI,GACS;IACb,0DAA0D;IAC1D,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE;QAClC,OAAO,KAAK,CAAC;KACd;IAED,mDAAmD;IACnD,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACzE,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;QAE9B,iEAAiE;QACjE,IACE,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;YACjD,CAAC,CAAC,SAAS,CAAC,QAAQ,EACpB;YACA,8CAA8C;YAC9C,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC;SAC3B;KACF;IAED,4DAA4D;IAC5D,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;QACpD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAChD,CAAC;AACJ,CAAC;AAoNC,0FAAuC;AAlNzC;;;;;GAKG;AACH,SAAS,kBAAkB,CACzB,MAAqB,EACrB,MAA2B;IAE3B,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QAC7C,+BAA+B;QAC/B,MAAM,CAAC,MAAM,KAAK,MAAM,CACzB,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAS,6BAA6B,CACpC,IAAsC;IAEtC,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IACtB,IAAI,IAAA,0BAAe,EAAC,IAAI,CAAC,EAAE;QACzB,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC;QAChC,IAAI,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;YAC1D,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,CAAC;YACpC,IACE,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC3C,QAAQ,CAAC,IAAI,KAAK,OAAO,EACzB;gBACA,OAAO,IAAI,CAAC;aACb;SACF;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AASD;;GAEG;AACH,SAAS,yBAAyB,CAChC,IAAwB,EACxB,OAAgB;IAEhB,MAAM,MAAM,GAAG,mBAAW,CAAC,UAAU,CACnC,IAAI,CAAC,MAAM,EACX,mBAAW,CAAC,iBAAiB,CAAC,aAAa,CAC5C,CAAC;IAEF,IAAI,CAAC,OAAO,CAAC,6BAA6B,EAAE;QAC1C,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CACL,IAAA,0BAAe,EAAC,MAAM,CAAC;QACvB,sCAAsC,CAAC,MAAM,CAAC;QAC9C,sCAAsC,CAAC,MAAM,CAAC;QAC9C,0BAA0B,CAAC,MAAM,CAAC;QAClC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC;QAChC,qBAAqB,CAAC,MAAM,CAAC,CAC9B,CAAC;AACJ,CAAC;AA2IC,8DAAyB;AAzI3B;;;GAGG;AACH,SAAS,mCAAmC,CAC1C,IAAwB,EACxB,OAAgB;IAEhB,IAAI,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;QAC5C,OAAO,IAAI,CAAC;KACb;IAED,MAAM,MAAM,GAAG,mBAAW,CAAC,UAAU,CACnC,IAAI,CAAC,MAAM,EACX,mBAAW,CAAC,iBAAiB,CAAC,aAAa,CAC5C,CAAC;IACF,IACE,OAAO,CAAC,gBAAgB;QACxB,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;QACjD,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC/C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB;QACvD,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EACjD;QACA,OAAO,IAAI,CAAC;KACb;IAED,oEAAoE;IACpE,OAAO,CACL,OAAO,CAAC,yCAAyC,KAAK,IAAI;QAC1D,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;QACpD,6BAA6B,CAAC,IAAI,CAAC,CACpC,CAAC;AACJ,CAAC;AA0GC,kFAAmC;AAxGrC;;GAEG;AACH,SAAS,yBAAyB,CAChC,IAAkB,EAClB,OAAgB;IAEhB,IACE,OAAO,CAAC,yBAAyB;QACjC,uCAAuC,CAAC,IAAI,CAAC,EAC7C;QACA,OAAO,IAAI,CAAC;KACb;IAED,OAAO,CACL,IAAI,CAAC,UAAU,IAAI,IAAI;QACvB,IAAA,wBAAa,EAAC,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAA,mBAAQ,EAAC,IAAI,CAAC,MAAM,CAAC,CACtB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAC9B,IAAkB,EAClB,OAAgB,EAChB,UAA+B,EAC/B,MAA8C;IAE9C,IAAI,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;QAC5C,OAAO;KACR;IAED,MAAM,CAAC,IAAA,uCAAkB,EAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;AAC/C,CAAC;AAgEC,0DAAuB;AA9DzB;;GAEG;AACH,SAAS,iCAAiC,CACxC,IAAwB,EACxB,OAAgB,EAChB,UAA+B,EAC/B,MAA8C;IAE9C,IAAI,mCAAmC,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;QACtD,OAAO;KACR;IAED,uBAAuB,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;AAC7D,CAAC;AA+CC,8EAAiC;AA7CnC;;GAEG;AACH,SAAS,qBAAqB,CAAC,IAAkB;IAC/C,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;IAE3B,IAAI,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,MAAK,sBAAc,CAAC,QAAQ,EAAE;QAC9C,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC;KAC3B;IAED,oGAAoG;IACpG,MAAM,iBAAiB,GAAG,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,MAAK,sBAAc,CAAC,eAAe,CAAC;IAC5E,MAAM,eAAe,GACnB,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,MAAK,sBAAc,CAAC,uBAAuB;QACzD,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;IACvD,IAAI,CAAC,iBAAiB,IAAI,CAAC,eAAe,EAAE;QAC1C,OAAO,KAAK,CAAC;KACd;IAED,OAAO,QAAQ,EAAE;QACf,QAAQ,QAAQ,CAAC,IAAI,EAAE;YACrB,KAAK,sBAAc,CAAC,uBAAuB,CAAC;YAC5C,KAAK,sBAAc,CAAC,kBAAkB,CAAC;YACvC,KAAK,sBAAc,CAAC,mBAAmB;gBACrC,IAAI,QAAQ,CAAC,UAAU,EAAE;oBACvB,OAAO,IAAI,CAAC;iBACb;gBACD,MAAM;YAER,2BAA2B;YAC3B,6DAA6D;YAC7D,KAAK,sBAAc,CAAC,kBAAkB;gBACpC,IAAI,QAAQ,CAAC,EAAE,CAAC,cAAc,EAAE;oBAC9B,OAAO,IAAI,CAAC;iBACb;gBACD,MAAM;SACT;QAED,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;KAC5B;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAUC,sDAAqB"}