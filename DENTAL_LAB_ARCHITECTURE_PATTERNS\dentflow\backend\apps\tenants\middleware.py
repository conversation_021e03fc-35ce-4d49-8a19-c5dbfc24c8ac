"""
Tenant Middleware for Multi-tenant Isolation
Ensures that all database operations are filtered by tenant
"""

from django.db import connection
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
import logging

logger = logging.getLogger(__name__)


class TenantMiddleware(MiddlewareMixin):
    """
    Middleware that enforces tenant isolation by:
    1. Extracting tenant context from user or subdomain
    2. Setting PostgreSQL session variable for Row Level Security
    3. Ensuring all database operations are tenant-scoped
    """
    
    def process_request(self, request):
        """Set tenant context for the request"""
        tenant_id = self._extract_tenant_id(request)
        
        if tenant_id:
            request.tenant_id = tenant_id
            self._set_tenant_context(tenant_id)
        else:
            # For public endpoints (health checks, etc.)
            request.tenant_id = None
        
        return None
    
    def process_response(self, request, response):
        """Clean up tenant context after request"""
        if hasattr(request, 'tenant_id') and request.tenant_id:
            self._clear_tenant_context()
        
        return response
    
    def process_exception(self, request, exception):
        """Clean up tenant context on exception"""
        if hasattr(request, 'tenant_id') and request.tenant_id:
            self._clear_tenant_context()
        
        return None
    
    def _extract_tenant_id(self, request):
        """Extract tenant ID from request"""
        
        # Method 1: From authenticated user
        if (hasattr(request, 'user') and 
            hasattr(request.user, 'tenant_id') and 
            request.user.is_authenticated):
            return str(request.user.tenant_id)
        
        # Method 2: From subdomain (tenant1.dentflow.app)
        host = request.get_host()
        if '.' in host:
            subdomain = host.split('.')[0]
            if subdomain and subdomain != 'www':
                tenant_id = self._get_tenant_by_subdomain(subdomain)
                if tenant_id:
                    return tenant_id
        
        # Method 3: From custom header (for API clients)
        tenant_header = request.META.get('HTTP_X_TENANT_ID')
        if tenant_header:
            return tenant_header
        
        # Method 4: From query parameter (for development/testing)
        tenant_param = request.GET.get('tenant_id')
        if tenant_param:
            return tenant_param
        
        return None
    
    def _get_tenant_by_subdomain(self, subdomain):
        """Get tenant ID from subdomain"""
        try:
            from apps.cases.models import Tenant
            tenant = Tenant.objects.get(subdomain=subdomain, is_active=True)
            return str(tenant.id)
        except Tenant.DoesNotExist:
            logger.warning(f"No active tenant found for subdomain: {subdomain}")
            return None
        except Exception as e:
            logger.error(f"Error fetching tenant for subdomain {subdomain}: {e}")
            return None
    
    def _set_tenant_context(self, tenant_id):
        """Set PostgreSQL session variable for Row Level Security"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SET app.current_tenant = %s", [tenant_id])
            logger.debug(f"Set tenant context: {tenant_id}")
        except Exception as e:
            logger.error(f"Failed to set tenant context {tenant_id}: {e}")
    
    def _clear_tenant_context(self):
        """Clear PostgreSQL session variable"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("RESET app.current_tenant")
            logger.debug("Cleared tenant context")
        except Exception as e:
            logger.error(f"Failed to clear tenant context: {e}")


class TenantNotFoundMiddleware(MiddlewareMixin):
    """
    Middleware that returns 404 for requests without valid tenant context
    Should be placed after TenantMiddleware
    """
    
    EXCLUDED_PATHS = [
        '/health/',
        '/admin/',
        '/api/docs/',
        '/api/schema/',
    ]
    
    def process_request(self, request):
        """Check if tenant context is required and available"""
        
        # Skip for excluded paths
        if any(request.path.startswith(path) for path in self.EXCLUDED_PATHS):
            return None
        
        # Skip for static/media files
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return None
        
        # Check if tenant context is required
        if self._requires_tenant_context(request):
            if not hasattr(request, 'tenant_id') or not request.tenant_id:
                return JsonResponse(
                    {'error': 'Tenant context required'},
                    status=404
                )
        
        return None
    
    def _requires_tenant_context(self, request):
        """Determine if request requires tenant context"""
        
        # API endpoints require tenant context
        if request.path.startswith('/api/'):
            return True
        
        # Add other patterns that require tenant context
        return False