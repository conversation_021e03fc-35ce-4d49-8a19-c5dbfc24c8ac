"""
Simplified URL configuration for dentflow_project.
Minimal setup for development without complex dependencies.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse

def health_check(request):
    """Simple health check endpoint"""
    return JsonResponse({
        'status': 'healthy',
        'service': 'DentFlow API',
        'version': '1.0.0-dev'
    })

def api_info(request):
    """API information endpoint"""
    return JsonResponse({
        'name': 'DentFlow API',
        'version': '1.0.0-dev',
        'description': 'Dental Laboratory Management System API',
        'endpoints': {
            'admin': '/admin/',
            'health': '/health/',
            'api_docs': '/api/docs/' if not settings.DEBUG else 'Not available in development'
        }
    })

urlpatterns = [
    # Django Admin
    path('admin/', admin.site.urls),
    
    # Health check endpoints
    path('health/', health_check, name='health_check'),
    path('api/info/', api_info, name='api_info'),
    
    # API placeholder
    path('api/v1/', include([])),  # Empty for now, add apps later
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
