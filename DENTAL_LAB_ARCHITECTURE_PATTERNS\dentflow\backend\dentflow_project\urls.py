"""
URL configuration for dentflow_project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)
from apps.cases.health import health_check, system_status, api_info

# Health check URLs
health_patterns = [
    path('', health_check, name='health_check'),
    path('status/', system_status, name='system_status'),
    path('info/', api_info, name='api_info'),
]

# API URL patterns
api_v1_patterns = [
    path('cases/', include('apps.cases.urls')),
    # path('auth/', include('apps.users.urls')),
    # path('tenants/', include('apps.tenants.urls')),
    # path('workflows/', include('apps.workflows.urls')),
    # path('billing/', include('apps.billing.urls')),
    # path('notifications/', include('apps.notifications.urls')),
    # path('files/', include('apps.files.urls')),
    # path('reports/', include('apps.reports.urls')),
]

urlpatterns = [
    # Django Admin
    path('admin/', admin.site.urls),
    
    # Health check endpoints
    path('health/', include(health_patterns)),
    
    # API Documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    
    # API v1
    path('api/v1/', include(api_v1_patterns)),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    
    # Debug toolbar
    if 'debug_toolbar' in settings.INSTALLED_APPS:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns

# Custom error handlers for production
# handler400 = 'apps.core.views.bad_request'
# handler403 = 'apps.core.views.permission_denied'
# handler404 = 'apps.core.views.page_not_found'
# handler500 = 'apps.core.views.server_error'