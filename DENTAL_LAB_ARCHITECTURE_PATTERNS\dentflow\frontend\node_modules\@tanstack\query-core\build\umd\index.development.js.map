{"version": 3, "file": "index.development.js", "sources": ["../../src/subscribable.ts", "../../src/utils.ts", "../../src/focusManager.ts", "../../src/onlineManager.ts", "../../src/retryer.ts", "../../src/logger.ts", "../../src/notifyManager.ts", "../../src/removable.ts", "../../src/query.ts", "../../src/queryCache.ts", "../../src/mutation.ts", "../../src/mutationCache.ts", "../../src/infiniteQueryBehavior.ts", "../../src/queryClient.ts", "../../src/queryObserver.ts", "../../src/queriesObserver.ts", "../../src/infiniteQueryObserver.ts", "../../src/mutationObserver.ts", "../../src/hydration.ts"], "sourcesContent": ["type Listener = () => void\n\nexport class Subscribable<TListener extends Function = Listener> {\n  protected listeners: Set<{ listener: TListener }>\n\n  constructor() {\n    this.listeners = new Set()\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    const identity = { listener }\n    this.listeners.add(identity)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(identity)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n", "import type { Mutation } from './mutation'\nimport type { Query } from './query'\nimport type {\n  FetchStatus,\n  MutationFunction,\n  MutationKey,\n  MutationOptions,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n} from './types'\n\n// TYPES\n\nexport interface QueryFilters {\n  /**\n   * Filter to active queries, inactive queries or all queries\n   */\n  type?: QueryTypeFilter\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: QueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include queries matching their fetchStatus\n   */\n  fetchStatus?: FetchStatus\n}\n\nexport interface MutationFilters {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (mutation: Mutation<any, any, any>) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: Mutation<PERSON>ey\n  /**\n   * Include or exclude fetching mutations\n   */\n  fetching?: boolean\n}\n\nexport type DataUpdateFunction<TInput, TOutput> = (input: TInput) => TOutput\n\nexport type Updater<TInput, TOutput> =\n  | TOutput\n  | DataUpdateFunction<TInput, TOutput>\n\nexport type QueryTypeFilter = 'all' | 'active' | 'inactive'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined' || 'Deno' in window\n\nexport function noop(): undefined {\n  return undefined\n}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput,\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as DataUpdateFunction<TInput, TOutput>)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function difference<T>(array1: T[], array2: T[]): T[] {\n  return array1.filter((x) => !array2.includes(x))\n}\n\nexport function replaceAt<T>(array: T[], index: number, value: T): T[] {\n  const copy = array.slice(0)\n  copy[index] = value\n  return copy\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function parseQueryArgs<\n  TOptions extends QueryOptions<any, any, any, TQueryKey>,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | TOptions,\n  arg2?: QueryFunction<any, TQueryKey> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (!isQueryKey(arg1)) {\n    return arg1 as TOptions\n  }\n\n  if (typeof arg2 === 'function') {\n    return { ...arg3, queryKey: arg1, queryFn: arg2 } as TOptions\n  }\n\n  return { ...arg2, queryKey: arg1 } as TOptions\n}\n\nexport function parseMutationArgs<\n  TOptions extends MutationOptions<any, any, any, any>,\n>(\n  arg1: MutationKey | MutationFunction<any, any> | TOptions,\n  arg2?: MutationFunction<any, any> | TOptions,\n  arg3?: TOptions,\n): TOptions {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return { ...arg3, mutationKey: arg1, mutationFn: arg2 } as TOptions\n    }\n    return { ...arg2, mutationKey: arg1 } as TOptions\n  }\n\n  if (typeof arg1 === 'function') {\n    return { ...arg2, mutationFn: arg1 } as TOptions\n  }\n\n  return { ...arg1 } as TOptions\n}\n\nexport function parseFilterArgs<\n  TFilters extends QueryFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1) ? [{ ...arg2, queryKey: arg1 }, arg3] : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function parseMutationFilterArgs<\n  TFilters extends MutationFilters,\n  TOptions = unknown,\n>(\n  arg1?: QueryKey | TFilters,\n  arg2?: TFilters | TOptions,\n  arg3?: TOptions,\n): [TFilters, TOptions | undefined] {\n  return (\n    isQueryKey(arg1)\n      ? [{ ...arg2, mutationKey: arg1 }, arg3]\n      : [arg1 || {}, arg2]\n  ) as [TFilters, TOptions]\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>,\n): boolean {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive()\n    if (type === 'active' && !isActive) {\n      return false\n    }\n    if (type === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (\n    typeof fetchStatus !== 'undefined' &&\n    fetchStatus !== query.state.fetchStatus\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>,\n): boolean {\n  const { exact, fetching, predicate, mutationKey } = filters\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (\n        hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)\n      ) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (\n    typeof fetching === 'boolean' &&\n    (mutation.state.status === 'loading') !== fetching\n  ) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: QueryOptions<any, any, any, TQueryKey>,\n): string {\n  const hashFn = options?.queryKeyHashFn || hashQueryKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query keys hash function.\n * Hashes the value into a stable hash.\n */\nexport function hashQueryKey(queryKey: QueryKey): string {\n  return JSON.stringify(queryKey, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val,\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean {\n  return partialDeepEqual(a, b)\n}\n\n/**\n * Checks if `b` partially matches with `a`.\n */\nexport function partialDeepEqual(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some((key) => !partialDeepEqual(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aSize = array ? a.length : Object.keys(a).length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      copy[key] = replaceEqualDeep(a[key], b[key])\n      if (copy[key] === a[key]) {\n        equalItems++\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\nexport function shallowEqualObjects<T>(a: T, b: T): boolean {\n  if ((a && !b) || (b && !a)) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nexport function isPlainArray(value: unknown) {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isQueryKey(value: unknown): value is QueryKey {\n  return Array.isArray(value)\n}\n\nexport function isError(value: any): value is Error {\n  return value instanceof Error\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout)\n  })\n}\n\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\nexport function scheduleMicrotask(callback: () => void) {\n  sleep(0).then(callback)\n}\n\nexport function getAbortController(): AbortController | undefined {\n  if (typeof AbortController === 'function') {\n    return new AbortController()\n  }\n  return\n}\n\nexport function replaceData<\n  TData,\n  TOptions extends QueryOptions<any, any, any, any>,\n>(prevData: TData | undefined, data: TData, options: TOptions): TData {\n  // Use prev data if an isDataEqual function is defined and returns `true`\n  if (options.isDataEqual?.(prevData, data)) {\n    return prevData as TData\n  } else if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data)\n  } else if (options.structuralSharing !== false) {\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data)\n  }\n  return data\n}\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void,\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable {\n  private focused?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onFocus) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibillitychange and focus\n        window.addEventListener('visibilitychange', listener, false)\n        window.addEventListener('focus', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n          window.removeEventListener('focus', listener)\n        }\n      }\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((focused) => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    const changed = this.focused !== focused\n    if (changed) {\n      this.focused = focused\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.focused === 'boolean') {\n      return this.focused\n    }\n\n    // document global can be unavailable in react native\n    if (typeof document === 'undefined') {\n      return true\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(\n      document.visibilityState,\n    )\n  }\n}\n\nexport const focusManager = new FocusManager()\n", "import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype SetupFn = (\n  setOnline: (online?: boolean) => void,\n) => (() => void) | undefined\n\nconst onlineEvents = ['online', 'offline'] as const\n\nexport class OnlineManager extends Subscribable {\n  private online?: boolean\n  private cleanup?: () => void\n\n  private setup: SetupFn\n\n  constructor() {\n    super()\n    this.setup = (onOnline) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onOnline()\n        // Listen to online\n        onlineEvents.forEach((event) => {\n          window.addEventListener(event, listener, false)\n        })\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          onlineEvents.forEach((event) => {\n            window.removeEventListener(event, listener)\n          })\n        }\n      }\n\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.cleanup?.()\n      this.cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.setup = setup\n    this.cleanup?.()\n    this.cleanup = setup((online?: boolean) => {\n      if (typeof online === 'boolean') {\n        this.setOnline(online)\n      } else {\n        this.onOnline()\n      }\n    })\n  }\n\n  setOnline(online?: boolean): void {\n    const changed = this.online !== online\n\n    if (changed) {\n      this.online = online\n      this.onOnline()\n    }\n  }\n\n  onOnline(): void {\n    this.listeners.forEach(({ listener }) => {\n      listener()\n    })\n  }\n\n  isOnline(): boolean {\n    if (typeof this.online === 'boolean') {\n      return this.online\n    }\n\n    if (\n      typeof navigator === 'undefined' ||\n      typeof navigator.onLine === 'undefined'\n    ) {\n      return true\n    }\n\n    return navigator.onLine\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n", "import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { sleep } from './utils'\nimport type { CancelOptions, NetworkMode } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = unknown> {\n  fn: () => TData | Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode: NetworkMode | undefined\n}\n\nexport interface Retryer<TData = unknown> {\n  promise: Promise<TData>\n  cancel: (cancelOptions?: CancelOptions) => void\n  continue: () => Promise<unknown>\n  cancelRetry: () => void\n  continueRetry: () => void\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError> = (\n  failureCount: number,\n  error: TError,\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = unknown> = (\n  failureCount: number,\n  error: TError,\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\nexport function canFetch(networkMode: NetworkMode | undefined): boolean {\n  return (networkMode ?? 'online') === 'online'\n    ? onlineManager.isOnline()\n    : true\n}\n\nexport class CancelledError {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\nexport function createRetryer<TData = unknown, TError = unknown>(\n  config: RetryerConfig<TData, TError>,\n): Retryer<TData> {\n  let isRetryCancelled = false\n  let failureCount = 0\n  let isResolved = false\n  let continueFn: ((value?: unknown) => boolean) | undefined\n  let promiseResolve: (data: TData) => void\n  let promiseReject: (error: TError) => void\n\n  const promise = new Promise<TData>((outerResolve, outerReject) => {\n    promiseResolve = outerResolve\n    promiseReject = outerReject\n  })\n\n  const cancel = (cancelOptions?: CancelOptions): void => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions))\n\n      config.abort?.()\n    }\n  }\n  const cancelRetry = () => {\n    isRetryCancelled = true\n  }\n\n  const continueRetry = () => {\n    isRetryCancelled = false\n  }\n\n  const shouldPause = () =>\n    !focusManager.isFocused() ||\n    (config.networkMode !== 'always' && !onlineManager.isOnline())\n\n  const resolve = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onSuccess?.(value)\n      continueFn?.()\n      promiseResolve(value)\n    }\n  }\n\n  const reject = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onError?.(value)\n      continueFn?.()\n      promiseReject(value)\n    }\n  }\n\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        const canContinue = isResolved || !shouldPause()\n        if (canContinue) {\n          continueResolve(value)\n        }\n        return canContinue\n      }\n      config.onPause?.()\n    }).then(() => {\n      continueFn = undefined\n      if (!isResolved) {\n        config.onContinue?.()\n      }\n    })\n  }\n\n  // Create loop function\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return\n    }\n\n    let promiseOrValue: any\n\n    // Execute query\n    try {\n      promiseOrValue = config.fn()\n    } catch (error) {\n      promiseOrValue = Promise.reject(error)\n    }\n\n    Promise.resolve(promiseOrValue)\n      .then(resolve)\n      .catch((error) => {\n        // Stop if the fetch is already resolved\n        if (isResolved) {\n          return\n        }\n\n        // Do we need to retry the request?\n        const retry = config.retry ?? 3\n        const retryDelay = config.retryDelay ?? defaultRetryDelay\n        const delay =\n          typeof retryDelay === 'function'\n            ? retryDelay(failureCount, error)\n            : retryDelay\n        const shouldRetry =\n          retry === true ||\n          (typeof retry === 'number' && failureCount < retry) ||\n          (typeof retry === 'function' && retry(failureCount, error))\n\n        if (isRetryCancelled || !shouldRetry) {\n          // We are done if the query does not need to be retried\n          reject(error)\n          return\n        }\n\n        failureCount++\n\n        // Notify on fail\n        config.onFail?.(failureCount, error)\n\n        // Delay\n        sleep(delay)\n          // Pause if the document is not visible or when the device is offline\n          .then(() => {\n            if (shouldPause()) {\n              return pause()\n            }\n            return\n          })\n          .then(() => {\n            if (isRetryCancelled) {\n              reject(error)\n            } else {\n              run()\n            }\n          })\n      })\n  }\n\n  // Start loop\n  if (canFetch(config.networkMode)) {\n    run()\n  } else {\n    pause().then(run)\n  }\n\n  return {\n    promise,\n    cancel,\n    continue: () => {\n      const didContinue = continueFn?.()\n      return didContinue ? promise : Promise.resolve()\n    },\n    cancelRetry,\n    continueRetry,\n  }\n}\n", "export interface Logger {\n  log: LogFunction\n  warn: LogFunction\n  error: LogFunction\n}\n\ntype LogFunction = (...args: any[]) => void\n\nexport const defaultLogger: Logger = console\n", "import { scheduleMicrotask } from './utils'\n\n// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\ntype BatchCallsCallback<T extends unknown[]> = (...args: T) => void\n\nexport function createNotifyManager() {\n  let queue: NotifyCallback[] = []\n  let transactions = 0\n  let notifyFn: NotifyFunction = (callback) => {\n    callback()\n  }\n  let batchNotifyFn: BatchNotifyFunction = (callback: () => void) => {\n    callback()\n  }\n\n  const batch = <T>(callback: () => T): T => {\n    let result\n    transactions++\n    try {\n      result = callback()\n    } finally {\n      transactions--\n      if (!transactions) {\n        flush()\n      }\n    }\n    return result\n  }\n\n  const schedule = (callback: NotifyCallback): void => {\n    if (transactions) {\n      queue.push(callback)\n    } else {\n      scheduleMicrotask(() => {\n        notifyFn(callback)\n      })\n    }\n  }\n\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  const batchCalls = <T extends unknown[]>(\n    callback: BatchCallsCallback<T>,\n  ): BatchCallsCallback<T> => {\n    return (...args) => {\n      schedule(() => {\n        callback(...args)\n      })\n    }\n  }\n\n  const flush = (): void => {\n    const originalQueue = queue\n    queue = []\n    if (originalQueue.length) {\n      scheduleMicrotask(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  const setNotifyFunction = (fn: NotifyFunction) => {\n    notifyFn = fn\n  }\n\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  const setBatchNotifyFunction = (fn: BatchNotifyFunction) => {\n    batchNotifyFn = fn\n  }\n\n  return {\n    batch,\n    batchCalls,\n    schedule,\n    setNotifyFunction,\n    setBatchNotifyFunction,\n  } as const\n}\n\n// SINGLETON\nexport const notifyManager = createNotifyManager()\n", "import { isServer, isValidTimeout } from './utils'\n\nexport abstract class Removable {\n  cacheTime!: number\n  private gcTimeout?: ReturnType<typeof setTimeout>\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.cacheTime)\n    }\n  }\n\n  protected updateCacheTime(newCacheTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no cache time is set\n    this.cacheTime = Math.max(\n      this.cacheTime || 0,\n      newCacheTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout)\n      this.gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n", "import { getAbortController, noop, replaceData, timeUntilStale } from './utils'\nimport { defaultLogger } from './logger'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type {\n  CancelOptions,\n  FetchStatus,\n  InitialDataFunction,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n} from './types'\nimport type { QueryCache } from './queryCache'\nimport type { QueryObserver } from './queryObserver'\nimport type { Logger } from './logger'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  cache: QueryCache\n  queryKey: TQueryKey\n  queryHash: string\n  logger?: Logger\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = unknown> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: any\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal?: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n  ) => void\n}\n\nexport interface FetchOptions {\n  cancelRefetch?: boolean\n  meta?: any\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: any\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  initialState: QueryState<TData, TError>\n  revertState?: QueryState<TData, TError>\n  state: QueryState<TData, TError>\n  isFetchingOptimistic?: boolean\n\n  private cache: QueryCache\n  private logger: Logger\n  private promise?: Promise<TData>\n  private retryer?: Retryer<TData>\n  private observers: QueryObserver<any, any, any, any, any>[]\n  private defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  private abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.abortSignalConsumed = false\n    this.defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.cache = config.cache\n    this.logger = config.logger || defaultLogger\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.initialState = config.state || getDefaultState(this.options)\n    this.state = this.initialState\n    this.scheduleGc()\n  }\n\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  private setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.promise\n    this.retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some((observer) => observer.options.enabled !== false)\n  }\n\n  isDisabled(): boolean {\n    return this.getObserversCount() > 0 && !this.isActive()\n  }\n\n  isStale(): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      this.observers.some((observer) => observer.getCurrentResult().isStale)\n    )\n  }\n\n  isStaleByTime(staleTime = 0): boolean {\n    return (\n      this.state.isInvalidated ||\n      !this.state.dataUpdatedAt ||\n      !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n    )\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    if (observer) {\n      observer.refetch({ cancelRefetch: false })\n    }\n\n    // Continue fetch if currently paused\n    this.retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.abortSignalConsumed) {\n            this.retryer.cancel({ revert: true })\n          } else {\n            this.retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.dataUpdatedAt && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({ silent: true })\n      } else if (this.promise) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.retryer?.continueRetry()\n        // Return current promise if we are already fetching\n        return this.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        this.logger.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = getAbortController()\n\n    // Create query function context\n    const queryFnContext: QueryFunctionContext<TQueryKey> = {\n      queryKey: this.queryKey,\n      pageParam: undefined,\n      meta: this.meta,\n    }\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          if (abortController) {\n            this.abortSignalConsumed = true\n            return abortController.signal\n          }\n          return undefined\n        },\n      })\n    }\n\n    addSignalProperty(queryFnContext)\n\n    // Create fetch function\n    const fetchFn = () => {\n      if (!this.options.queryFn) {\n        return Promise.reject(\n          `Missing queryFn for queryKey '${this.options.queryHash}'`,\n        )\n      }\n      this.abortSignalConsumed = false\n      return this.options.queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const context: FetchContext<TQueryFnData, TError, TData, TQueryKey> = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn,\n    }\n\n    addSignalProperty(context)\n\n    this.options.behavior?.onFetch(context)\n\n    // Store state in case the current fetch needs to be reverted\n    this.revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.cache.config.onError?.(error, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n      }\n\n      if (!this.isFetchingOptimistic) {\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      }\n      this.isFetchingOptimistic = false\n    }\n\n    // Try to fetch the data\n    this.retryer = createRetryer({\n      fn: context.fetchFn as () => TData,\n      abort: abortController?.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (typeof data === 'undefined') {\n          if (process.env.NODE_ENV !== 'production') {\n            this.logger.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        this.setData(data as TData)\n\n        // Notify cache callback\n        this.cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this as Query<any, any, any, any>,\n        )\n\n        if (!this.isFetchingOptimistic) {\n          // Schedule query gc after fetching\n          this.scheduleGc()\n        }\n        this.isFetchingOptimistic = false\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n    })\n\n    this.promise = this.retryer.promise\n\n    return this.promise\n  }\n\n  private dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            fetchFailureCount: 0,\n            fetchFailureReason: null,\n            fetchMeta: action.meta ?? null,\n            fetchStatus: canFetch(this.options.networkMode)\n              ? 'fetching'\n              : 'paused',\n            ...(!state.dataUpdatedAt && {\n              error: null,\n              status: 'loading',\n            }),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error as unknown\n\n          if (isCancelledError(error) && error.revert && this.revertState) {\n            return { ...this.revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error: error as TError,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error as TError,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate(action)\n      })\n\n      this.cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = typeof data !== 'undefined'\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'loading',\n    fetchStatus: 'idle',\n  }\n}\n", "import { hashQueryKeyByOptions, matchQuery, parseFilterArgs } from './utils'\nimport { Query } from './query'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport type { QueryFilters } from './utils'\nimport type { Action, QueryState } from './query'\nimport type { NotifyEvent, OmitKeyof, QueryKey, QueryOptions } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (error: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n}\n\ninterface QueryHashMap {\n  [hash: string]: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryAdded extends NotifyEvent {\n  type: 'added'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved extends NotifyEvent {\n  type: 'removed'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated extends NotifyEvent {\n  type: 'updated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventQueryObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverResultsUpdated extends NotifyEvent {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\nexport type QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventQueryObserverAdded\n  | NotifyEventQueryObserverRemoved\n  | NotifyEventQueryObserverResultsUpdated\n  | NotifyEventQueryObserverOptionsUpdated\n\ntype QueryCacheListener = (event: QueryCacheNotifyEvent) => void\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  config: QueryCacheConfig\n\n  private queries: Query<any, any, any, any>[]\n  private queriesMap: QueryHashMap\n\n  constructor(config?: QueryCacheConfig) {\n    super()\n    this.config = config || {}\n    this.queries = []\n    this.queriesMap = {}\n  }\n\n  build<TQueryFnData, TError, TData, TQueryKey extends QueryKey>(\n    client: QueryClient,\n    options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    state?: QueryState<TData, TError>,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey!\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        logger: client.getLogger(),\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query\n      this.queries.push(query)\n      this.notify({\n        type: 'added',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.queriesMap[query.queryHash]\n\n    if (queryInMap) {\n      query.destroy()\n\n      this.queries = this.queries.filter((x) => x !== query)\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash]\n      }\n\n      this.notify({ type: 'removed', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryHash: string,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> | undefined {\n    return this.queriesMap[queryHash]\n  }\n\n  getAll(): Query[] {\n    return this.queries\n  }\n\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    filters: QueryFilters,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    queryKey: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  find<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(\n    arg1: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const [filters] = parseFilterArgs(arg1, arg2)\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.queries.find((query) => matchQuery(filters, query))\n  }\n\n  findAll(filters?: QueryFilters): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  findAll(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): Query[] {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    return Object.keys(filters).length > 0\n      ? this.queries.filter((query) => matchQuery(filters, query))\n      : this.queries\n  }\n\n  notify(event: QueryCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.queries.forEach((query) => {\n        query.onOnline()\n      })\n    })\n  }\n}\n", "import { defaultLogger } from './logger'\nimport { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Removable } from './removable'\nimport { canFetch, createRetry<PERSON> } from './retryer'\nimport type { MutationMeta, MutationOptions, MutationStatus } from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { Logger } from './logger'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  logger?: Logger\n  defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n  meta?: MutationMeta\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface LoadingAction<TVariables, TContext> {\n  type: 'loading'\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError, TVariables, TContext> {\n  type: 'setState'\n  state: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | LoadingAction<TVariables, TContext>\n  | PauseAction\n  | SetStateAction<TData, TError, TVariables, TContext>\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  mutationId: number\n\n  private observers: MutationObserver<TData, TError, TVariables, TContext>[]\n  private defaultOptions?: MutationOptions<TData, TError, TVariables, TContext>\n  private mutationCache: MutationCache\n  private logger: Logger\n  private retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.defaultOptions = config.defaultOptions\n    this.mutationId = config.mutationId\n    this.mutationCache = config.mutationCache\n    this.logger = config.logger || defaultLogger\n    this.observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options?: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = { ...this.defaultOptions, ...options }\n\n    this.updateCacheTime(this.options.cacheTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  setState(state: MutationState<TData, TError, TVariables, TContext>): void {\n    this.dispatch({ type: 'setState', state })\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.observers = this.observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.status === 'loading') {\n        this.scheduleGc()\n      } else {\n        this.mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return this.retryer?.continue() ?? this.execute()\n  }\n\n  async execute(): Promise<TData> {\n    const executeMutation = () => {\n      this.retryer = createRetryer({\n        fn: () => {\n          if (!this.options.mutationFn) {\n            return Promise.reject('No mutationFn found')\n          }\n          return this.options.mutationFn(this.state.variables!)\n        },\n        onFail: (failureCount, error) => {\n          this.dispatch({ type: 'failed', failureCount, error })\n        },\n        onPause: () => {\n          this.dispatch({ type: 'pause' })\n        },\n        onContinue: () => {\n          this.dispatch({ type: 'continue' })\n        },\n        retry: this.options.retry ?? 0,\n        retryDelay: this.options.retryDelay,\n        networkMode: this.options.networkMode,\n      })\n\n      return this.retryer.promise\n    }\n\n    const restored = this.state.status === 'loading'\n    try {\n      if (!restored) {\n        this.dispatch({ type: 'loading', variables: this.options.variables! })\n        // Notify cache callback\n        await this.mutationCache.config.onMutate?.(\n          this.state.variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(this.state.variables!)\n        if (context !== this.state.context) {\n          this.dispatch({\n            type: 'loading',\n            context,\n            variables: this.state.variables,\n          })\n        }\n      }\n      const data = await executeMutation()\n\n      // Notify cache callback\n      await this.mutationCache.config.onSuccess?.(\n        data,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(\n        data,\n        this.state.variables!,\n        this.state.context!,\n      )\n\n      // Notify cache callback\n      await this.mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(\n        data,\n        null,\n        this.state.variables!,\n        this.state.context,\n      )\n\n      this.dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.mutationCache.config.onError?.(\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        if (process.env.NODE_ENV !== 'production') {\n          this.logger.error(error)\n        }\n\n        await this.options.onError?.(\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.mutationCache.config.onSettled?.(\n          undefined,\n          error,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          this.state.variables!,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.dispatch({ type: 'error', error: error as TError })\n      }\n    }\n  }\n\n  private dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'loading':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: !canFetch(this.options.networkMode),\n            status: 'loading',\n            variables: action.variables,\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n  }\n}\n", "import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Mutation } from './mutation'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\nimport type { MutationObserver } from './mutationObserver'\nimport type { MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Action, MutationState } from './mutation'\nimport type { MutationFilters } from './utils'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: unknown | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\ntype MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  config: MutationCacheConfig\n\n  private mutations: Mutation<any, any, any, any>[]\n  private mutationId: number\n  private resuming: Promise<unknown> | undefined\n\n  constructor(config?: MutationCacheConfig) {\n    super()\n    this.config = config || {}\n    this.mutations = []\n    this.mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      logger: client.getLogger(),\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n      defaultOptions: options.mutationKey\n        ? client.getMutationDefaults(options.mutationKey)\n        : undefined,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.mutations.push(mutation)\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    this.mutations = this.mutations.filter((x) => x !== mutation)\n    this.notify({ type: 'removed', mutation })\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.mutations.forEach((mutation) => {\n        this.remove(mutation)\n      })\n    })\n  }\n\n  getAll(): Mutation[] {\n    return this.mutations\n  }\n\n  find<TData = unknown, TError = unknown, TVariables = any, TContext = unknown>(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true\n    }\n\n    return this.mutations.find((mutation) => matchMutation(filters, mutation))\n  }\n\n  findAll(filters: MutationFilters): Mutation[] {\n    return this.mutations.filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    this.resuming = (this.resuming ?? Promise.resolve())\n      .then(() => {\n        const pausedMutations = this.mutations.filter((x) => x.state.isPaused)\n        return notifyManager.batch(() =>\n          pausedMutations.reduce(\n            (promise, mutation) =>\n              promise.then(() => mutation.continue().catch(noop)),\n            Promise.resolve() as Promise<unknown>,\n          ),\n        )\n      })\n      .then(() => {\n        this.resuming = undefined\n      })\n\n    return this.resuming\n  }\n}\n", "import type { QueryBehavior } from './query'\n\nimport type {\n  InfiniteData,\n  QueryFunctionContext,\n  QueryOptions,\n  RefetchQueryFilters,\n} from './types'\n\nexport function infiniteQueryBehavior<\n  TQueryFnData,\n  TError,\n  TData,\n>(): QueryBehavior<TQueryFnData, TError, InfiniteData<TData>> {\n  return {\n    onFetch: (context) => {\n      context.fetchFn = () => {\n        const refetchPage: RefetchQueryFilters['refetchPage'] | undefined =\n          context.fetchOptions?.meta?.refetchPage\n        const fetchMore = context.fetchOptions?.meta?.fetchMore\n        const pageParam = fetchMore?.pageParam\n        const isFetchingNextPage = fetchMore?.direction === 'forward'\n        const isFetchingPreviousPage = fetchMore?.direction === 'backward'\n        const oldPages = context.state.data?.pages || []\n        const oldPageParams = context.state.data?.pageParams || []\n        let newPageParams = oldPageParams\n        let cancelled = false\n\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal?.aborted) {\n                cancelled = true\n              } else {\n                context.signal?.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        // Get query function\n        const queryFn =\n          context.options.queryFn ||\n          (() =>\n            Promise.reject(\n              `Missing queryFn for queryKey '${context.options.queryHash}'`,\n            ))\n\n        const buildNewPages = (\n          pages: unknown[],\n          param: unknown,\n          page: unknown,\n          previous?: boolean,\n        ) => {\n          newPageParams = previous\n            ? [param, ...newPageParams]\n            : [...newPageParams, param]\n          return previous ? [page, ...pages] : [...pages, page]\n        }\n\n        // Create function to fetch a page\n        const fetchPage = (\n          pages: unknown[],\n          manual?: boolean,\n          param?: unknown,\n          previous?: boolean,\n        ): Promise<unknown[]> => {\n          if (cancelled) {\n            return Promise.reject('Cancelled')\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages)\n          }\n\n          const queryFnContext: QueryFunctionContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta,\n          }\n\n          addSignalProperty(queryFnContext)\n\n          const queryFnResult = queryFn(queryFnContext)\n\n          const promise = Promise.resolve(queryFnResult).then((page) =>\n            buildNewPages(pages, param, page, previous),\n          )\n\n          return promise\n        }\n\n        let promise: Promise<unknown[]>\n\n        // Fetch first page?\n        if (!oldPages.length) {\n          promise = fetchPage([])\n        }\n\n        // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getNextPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param)\n        }\n\n        // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getPreviousPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param, true)\n        }\n\n        // Refetch pages\n        else {\n          newPageParams = []\n\n          const manual = typeof context.options.getNextPageParam === 'undefined'\n\n          const shouldFetchFirstPage =\n            refetchPage && oldPages[0]\n              ? refetchPage(oldPages[0], 0, oldPages)\n              : true\n\n          // Fetch first page\n          promise = shouldFetchFirstPage\n            ? fetchPage([], manual, oldPageParams[0])\n            : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0]))\n\n          // Fetch remaining pages\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then((pages) => {\n              const shouldFetchNextPage =\n                refetchPage && oldPages[i]\n                  ? refetchPage(oldPages[i], i, oldPages)\n                  : true\n\n              if (shouldFetchNextPage) {\n                const param = manual\n                  ? oldPageParams[i]\n                  : getNextPageParam(context.options, pages)\n                return fetchPage(pages, manual, param)\n              }\n              return Promise.resolve(\n                buildNewPages(pages, oldPageParams[i], oldPages[i]),\n              )\n            })\n          }\n        }\n\n        const finalPromise = promise.then((pages) => ({\n          pages,\n          pageParams: newPageParams,\n        }))\n\n        return finalPromise\n      }\n    },\n  }\n}\n\nexport function getNextPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getNextPageParam?.(pages[pages.length - 1], pages)\n}\n\nexport function getPreviousPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getPreviousPageParam?.(pages[0], pages)\n}\n\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasNextPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages)\n    return (\n      typeof nextPageParam !== 'undefined' &&\n      nextPageParam !== null &&\n      nextPageParam !== false\n    )\n  }\n  return\n}\n\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasPreviousPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages)\n    return (\n      typeof previousPageParam !== 'undefined' &&\n      previousPageParam !== null &&\n      previousPageParam !== false\n    )\n  }\n  return\n}\n", "import {\n  functionalUpdate,\n  hashQueryKey,\n  hashQueryKeyByOptions,\n  noop,\n  parseFilterArgs,\n  parseQueryArgs,\n  partialMatchKey,\n} from './utils'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport { defaultLogger } from './logger'\nimport type { OmitKeyof } from '@tanstack/query-core'\nimport type { CancelOptions, DefaultedQueryObserverOptions } from './types'\nimport type { Logger } from './logger'\nimport type { QueryState } from './query'\nimport type {\n  DefaultOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  QueryClientConfig,\n  QueryFunction,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  ResetQueryFilters,\n  SetDataOptions,\n  WithRequired,\n} from './types'\nimport type { MutationFilters, QueryFilters, Updater } from './utils'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: QueryOptions<any, any, any>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  private queryCache: QueryCache\n  private mutationCache: MutationCache\n  private logger: Logger\n  private defaultOptions: DefaultOptions\n  private queryDefaults: QueryDefaults[]\n  private mutationDefaults: MutationDefaults[]\n  private mountCount: number\n  private unsubscribeFocus?: () => void\n  private unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.queryCache = config.queryCache || new QueryCache()\n    this.mutationCache = config.mutationCache || new MutationCache()\n    this.logger = config.logger || defaultLogger\n    this.defaultOptions = config.defaultOptions || {}\n    this.queryDefaults = []\n    this.mutationDefaults = []\n    this.mountCount = 0\n\n    if (process.env.NODE_ENV !== 'production' && config.logger) {\n      this.logger.error(\n        `Passing a custom logger has been deprecated and will be removed in the next major version.`,\n      )\n    }\n  }\n\n  mount(): void {\n    this.mountCount++\n    if (this.mountCount !== 1) return\n\n    this.unsubscribeFocus = focusManager.subscribe(() => {\n      if (focusManager.isFocused()) {\n        this.resumePausedMutations()\n        this.queryCache.onFocus()\n      }\n    })\n    this.unsubscribeOnline = onlineManager.subscribe(() => {\n      if (onlineManager.isOnline()) {\n        this.resumePausedMutations()\n        this.queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.mountCount--\n    if (this.mountCount !== 0) return\n\n    this.unsubscribeFocus?.()\n    this.unsubscribeFocus = undefined\n\n    this.unsubscribeOnline?.()\n    this.unsubscribeOnline = undefined\n  }\n\n  isFetching(filters?: QueryFilters): number\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): number\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  isFetching(arg1?: QueryKey | QueryFilters, arg2?: QueryFilters): number {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    filters.fetchStatus = 'fetching'\n    return this.queryCache.findAll(filters).length\n  }\n\n  isMutating(filters?: MutationFilters): number {\n    return this.mutationCache.findAll({ ...filters, fetching: true }).length\n  }\n\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): TQueryFnData | undefined\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): TQueryFnData | undefined\n  /**\n   * @deprecated This method will accept only queryKey in the next major version.\n   */\n  getQueryData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): TQueryFnData | undefined {\n    return this.queryCache.find<TQueryFnData>(queryKey, filters)?.state.data\n  }\n\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: WithRequired<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  ensureQueryData<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | WithRequired<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg3?: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const cachedData = this.getQueryData<TData>(parsedOptions.queryKey!)\n\n    return cachedData\n      ? Promise.resolve(cachedData)\n      : this.fetchQuery(parsedOptions)\n  }\n\n  getQueriesData<TQueryFnData = unknown>(\n    filters: QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData<TQueryFnData = unknown>(\n    queryKey: QueryKey,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  getQueriesData<TQueryFnData = unknown>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return this.getQueryCache()\n      .findAll(queryKeyOrFilters)\n      .map(({ queryKey, state }) => {\n        const data = state.data as TQueryFnData | undefined\n        return [queryKey, data]\n      })\n  }\n\n  setQueryData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): TQueryFnData | undefined {\n    const query = this.queryCache.find<TQueryFnData>(queryKey)\n    const prevData = query?.state.data\n    const data = functionalUpdate(updater, prevData)\n\n    if (typeof data === 'undefined') {\n      return undefined\n    }\n\n    const parsedOptions = parseQueryArgs(queryKey)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n    return this.queryCache\n      .build(this, defaultedOptions)\n      .setData(data, { ...options, manual: true })\n  }\n\n  setQueriesData<TQueryFnData>(\n    filters: QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData<TQueryFnData>(\n    queryKey: QueryKey,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][]\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  setQueriesData<TQueryFnData>(\n    queryKeyOrFilters: QueryKey | QueryFilters,\n    updater: Updater<TQueryFnData | undefined, TQueryFnData | undefined>,\n    options?: SetDataOptions,\n  ): [QueryKey, TQueryFnData | undefined][] {\n    return notifyManager.batch(() =>\n      this.getQueryCache()\n        .findAll(queryKeyOrFilters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TQueryFnData>(queryKey, updater, options),\n        ]),\n    )\n  }\n\n  getQueryState<TQueryFnData = unknown, TError = undefined>(\n    queryKey: QueryKey,\n    /**\n     * @deprecated This filters will be removed in the next major version.\n     */\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): QueryState<TQueryFnData, TError> | undefined {\n    return this.queryCache.find<TQueryFnData, TError>(queryKey, filters)?.state\n  }\n\n  removeQueries(filters?: QueryFilters): void\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): void\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  removeQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'>,\n  ): void {\n    const [filters] = parseFilterArgs(arg1, arg2)\n    const queryCache = this.queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TPageData = unknown>(\n    filters?: ResetQueryFilters<TPageData>,\n    options?: ResetOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<ResetQueryFilters<TPageData>, 'queryKey'>,\n    options?: ResetOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  resetQueries(\n    arg1?: QueryKey | ResetQueryFilters,\n    arg2?: OmitKeyof<ResetQueryFilters, 'queryKey'> | ResetOptions,\n    arg3?: ResetOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n    const queryCache = this.queryCache\n\n    const refetchFilters: RefetchQueryFilters = {\n      type: 'active',\n      ...filters,\n    }\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset()\n      })\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  cancelQueries(filters?: QueryFilters, options?: CancelOptions): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<QueryFilters, 'queryKey'>,\n    options?: CancelOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  cancelQueries(\n    arg1?: QueryKey | QueryFilters,\n    arg2?: OmitKeyof<QueryFilters, 'queryKey'> | CancelOptions,\n    arg3?: CancelOptions,\n  ): Promise<void> {\n    const [filters, cancelOptions = {}] = parseFilterArgs(arg1, arg2, arg3)\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true\n    }\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .map((query) => query.cancel(cancelOptions)),\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TPageData = unknown>(\n    filters?: InvalidateQueryFilters<TPageData>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<InvalidateQueryFilters<TPageData>, 'queryKey'>,\n    options?: InvalidateOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  invalidateQueries(\n    arg1?: QueryKey | InvalidateQueryFilters,\n    arg2?: OmitKeyof<InvalidateQueryFilters, 'queryKey'> | InvalidateOptions,\n    arg3?: InvalidateOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    return notifyManager.batch(() => {\n      this.queryCache.findAll(filters).forEach((query) => {\n        query.invalidate()\n      })\n\n      if (filters.refetchType === 'none') {\n        return Promise.resolve()\n      }\n      const refetchFilters: RefetchQueryFilters = {\n        ...filters,\n        type: filters.refetchType ?? filters.type ?? 'active',\n      }\n      return this.refetchQueries(refetchFilters, options)\n    })\n  }\n\n  refetchQueries<TPageData = unknown>(\n    filters?: RefetchQueryFilters<TPageData>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries<TPageData = unknown>(\n    queryKey?: QueryKey,\n    filters?: OmitKeyof<RefetchQueryFilters<TPageData>, 'queryKey'>,\n    options?: RefetchOptions,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  refetchQueries(\n    arg1?: QueryKey | RefetchQueryFilters,\n    arg2?: OmitKeyof<RefetchQueryFilters, 'queryKey'> | RefetchOptions,\n    arg3?: RefetchOptions,\n  ): Promise<void> {\n    const [filters, options] = parseFilterArgs(arg1, arg2, arg3)\n\n    const promises = notifyManager.batch(() =>\n      this.queryCache\n        .findAll(filters)\n        .filter((query) => !query.isDisabled())\n        .map((query) =>\n          query.fetch(undefined, {\n            ...options,\n            cancelRefetch: options?.cancelRefetch ?? true,\n            meta: { refetchPage: filters.refetchPage },\n          }),\n        ),\n    )\n\n    let promise = Promise.all(promises).then(noop)\n\n    if (!options?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<TData> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    const defaultedOptions = this.defaultQueryOptions(parsedOptions)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(defaultedOptions.staleTime)\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1: TQueryKey | FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void> {\n    return this.fetchQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<InfiniteData<TData>>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<InfiniteData<TData>> {\n    const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n    parsedOptions.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData\n    >()\n    return this.fetchQuery(parsedOptions)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData = unknown,\n    TError = unknown,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryKey: TQueryKey,\n    queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n    options?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void>\n  /**\n   * @deprecated This method should be used with only one object argument.\n   */\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    arg1:\n      | TQueryKey\n      | FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    arg2?:\n      | QueryFunction<TQueryFnData, TQueryKey>\n      | OmitKeyof<\n          FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n          'queryKey'\n        >,\n    arg3?: OmitKeyof<\n      FetchInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey' | 'queryFn'\n    >,\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(arg1 as any, arg2 as any, arg3)\n      .then(noop)\n      .catch(noop)\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    return this.mutationCache.resumePausedMutations()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.mutationCache\n  }\n\n  getLogger(): Logger {\n    return this.logger\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.defaultOptions = options\n  }\n\n  setQueryDefaults(\n    queryKey: QueryKey,\n    options: QueryObserverOptions<unknown, any, any, any>,\n  ): void {\n    const result = this.queryDefaults.find(\n      (x) => hashQueryKey(queryKey) === hashQueryKey(x.queryKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.queryDefaults.push({ queryKey, defaultOptions: options })\n    }\n  }\n\n  getQueryDefaults(\n    queryKey?: QueryKey,\n  ): QueryObserverOptions<any, any, any, any, any> | undefined {\n    if (!queryKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.queryDefaults.find((x) =>\n      partialMatchKey(queryKey, x.queryKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.queryDefaults.filter((x) =>\n        partialMatchKey(queryKey, x.queryKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several query defaults match with key '${JSON.stringify(\n            queryKey,\n          )}'. The first matching query defaults are used. Please check how query defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetquerydefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  setMutationDefaults(\n    mutationKey: MutationKey,\n    options: MutationObserverOptions<any, any, any, any>,\n  ): void {\n    const result = this.mutationDefaults.find(\n      (x) => hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey),\n    )\n    if (result) {\n      result.defaultOptions = options\n    } else {\n      this.mutationDefaults.push({ mutationKey, defaultOptions: options })\n    }\n  }\n\n  getMutationDefaults(\n    mutationKey?: MutationKey,\n  ): MutationObserverOptions<any, any, any, any> | undefined {\n    if (!mutationKey) {\n      return undefined\n    }\n\n    // Get the first matching defaults\n    const firstMatchingDefaults = this.mutationDefaults.find((x) =>\n      partialMatchKey(mutationKey, x.mutationKey),\n    )\n\n    // Additional checks and error in dev mode\n    if (process.env.NODE_ENV !== 'production') {\n      // Retrieve all matching defaults for the given key\n      const matchingDefaults = this.mutationDefaults.filter((x) =>\n        partialMatchKey(mutationKey, x.mutationKey),\n      )\n      // It is ok not having defaults, but it is error prone to have more than 1 default for a given key\n      if (matchingDefaults.length > 1) {\n        this.logger.error(\n          `[QueryClient] Several mutation defaults match with key '${JSON.stringify(\n            mutationKey,\n          )}'. The first matching mutation defaults are used. Please check how mutation defaults are registered. Order does matter here. cf. https://react-query.tanstack.com/reference/QueryClient#queryclientsetmutationdefaults.`,\n        )\n      }\n    }\n\n    return firstMatchingDefaults?.defaultOptions\n  }\n\n  defaultQueryOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey extends QueryKey,\n  >(\n    options?:\n      | QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey>\n      | DefaultedQueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey\n        >,\n  ): DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  > {\n    if (options?._defaulted) {\n      return options as DefaultedQueryObserverOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryData,\n        TQueryKey\n      >\n    }\n\n    const defaultedOptions = {\n      ...this.defaultOptions.queries,\n      ...this.getQueryDefaults(options?.queryKey),\n      ...options,\n      _defaulted: true,\n    }\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions,\n      )\n    }\n\n    // dependent default values\n    if (typeof defaultedOptions.refetchOnReconnect === 'undefined') {\n      defaultedOptions.refetchOnReconnect =\n        defaultedOptions.networkMode !== 'always'\n    }\n    if (typeof defaultedOptions.useErrorBoundary === 'undefined') {\n      defaultedOptions.useErrorBoundary = !!defaultedOptions.suspense\n    }\n\n    return defaultedOptions as DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T,\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.defaultOptions.mutations,\n      ...this.getMutationDefaults(options?.mutationKey),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.queryCache.clear()\n    this.mutationCache.clear()\n  }\n}\n", "import {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport { focusManager } from './focusManager'\nimport { Subscribable } from './subscribable'\nimport { canFetch, isCancelledError } from './retryer'\nimport type {\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\nimport type { Action, FetchOptions, Query, QueryState } from './query'\nimport type { QueryClient } from './queryClient'\nimport type { DefaultedQueryObserverOptions, RefetchPageFilters } from './types'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\nexport interface NotifyOptions {\n  cache?: boolean\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\nexport interface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  options: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n\n  private client: QueryClient\n  private currentQuery!: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  private currentQueryInitialState!: QueryState<TQueryData, TError>\n  private currentResult!: QueryObserverResult<TData, TError>\n  private currentResultState?: QueryState<TQueryData, TError>\n  private currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  private previousQueryResult?: QueryObserverResult<TData, TError>\n  private selectError: TError | null\n  private selectFn?: (data: TQueryData) => TData\n  private selectResult?: TData\n  private staleTimeoutId?: ReturnType<typeof setTimeout>\n  private refetchIntervalId?: ReturnType<typeof setInterval>\n  private currentRefetchInterval?: number | false\n  private trackedProps!: Set<keyof QueryObserverResult>\n\n  constructor(\n    client: QueryClient,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.client = client\n    this.options = options\n    this.trackedProps = new Set()\n    this.selectError = null\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.remove = this.remove.bind(this)\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch()\n      }\n\n      this.updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.clearStaleTimeout()\n    this.clearRefetchInterval()\n    this.currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.currentQuery\n\n    this.options = this.client.defaultQueryOptions(options)\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      typeof options?.isDataEqual !== 'undefined'\n    ) {\n      this.client\n        .getLogger()\n        .error(\n          `The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option`,\n        )\n    }\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this,\n      })\n    }\n\n    if (\n      typeof this.options.enabled !== 'undefined' &&\n      typeof this.options.enabled !== 'boolean'\n    ) {\n      throw new Error('Expected enabled to be a boolean')\n    }\n\n    // Keep previous query key if the user does not supply one\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey\n    }\n\n    this.updateQuery()\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.executeFetch()\n    }\n\n    // Update result\n    this.updateResult(notifyOptions)\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        this.options.staleTime !== prevOptions.staleTime)\n    ) {\n      this.updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        nextRefetchInterval !== this.currentRefetchInterval)\n    ) {\n      this.updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.client.getQueryCache().build(this.client, options)\n\n    const result = this.createResult(query, options)\n\n    if (shouldAssignObserverCurrentProperties(this, result, options)) {\n      // this assigns the optimistic result to the current Observer\n      // because if the query function changes, useQuery will be performing\n      // an effect where it would fetch again.\n      // When the fetch finishes, we perform a deep data cloning in order\n      // to reuse objects references. This deep data clone is performed against\n      // the `observer.currentResult.data` property\n      // When QueryKey changes, we refresh the query and get new `optimistic`\n      // result, while we leave the `observer.currentResult`, so when new data\n      // arrives, it finds the old `observer.currentResult` which is related\n      // to the old QueryKey. Which means that currentResult and selectData are\n      // out of sync already.\n      // To solve this, we move the cursor of the currentResult everytime\n      // an observer reads an optimistic value.\n\n      // When keeping the previous data, the result doesn't change until new\n      // data arrives.\n      this.currentResult = result\n      this.currentResultOptions = this.options\n      this.currentResultState = this.currentQuery.state\n    }\n    return result\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n  ): QueryObserverResult<TData, TError> {\n    const trackedResult = {} as QueryObserverResult<TData, TError>\n\n    Object.keys(result).forEach((key) => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key as keyof QueryObserverResult)\n          return result[key as keyof QueryObserverResult]\n        },\n      })\n    })\n\n    return trackedResult\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.currentQuery\n  }\n\n  remove(): void {\n    this.client.getQueryCache().remove(this.currentQuery)\n  }\n\n  refetch<TPageData>({\n    refetchPage,\n    ...options\n  }: RefetchOptions & RefetchPageFilters<TPageData> = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: { refetchPage },\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.client.defaultQueryOptions(options)\n\n    const query = this.client\n      .getQueryCache()\n      .build(this.client, defaultedOptions)\n    query.isFetchingOptimistic = true\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.currentResult\n    })\n  }\n\n  private executeFetch(\n    fetchOptions?: ObserverFetchOptions,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  private updateStaleTimeout(): void {\n    this.clearStaleTimeout()\n\n    if (\n      isServer ||\n      this.currentResult.isStale ||\n      !isValidTimeout(this.options.staleTime)\n    ) {\n      return\n    }\n\n    const time = timeUntilStale(\n      this.currentResult.dataUpdatedAt,\n      this.options.staleTime,\n    )\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  private computeRefetchInterval() {\n    return typeof this.options.refetchInterval === 'function'\n      ? this.options.refetchInterval(this.currentResult.data, this.currentQuery)\n      : this.options.refetchInterval ?? false\n  }\n\n  private updateRefetchInterval(nextInterval: number | false): void {\n    this.clearRefetchInterval()\n\n    this.currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      this.options.enabled === false ||\n      !isValidTimeout(this.currentRefetchInterval) ||\n      this.currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.executeFetch()\n      }\n    }, this.currentRefetchInterval)\n  }\n\n  private updateTimers(): void {\n    this.updateStaleTimeout()\n    this.updateRefetchInterval(this.computeRefetchInterval())\n  }\n\n  private clearStaleTimeout(): void {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId)\n      this.staleTimeoutId = undefined\n    }\n  }\n\n  private clearRefetchInterval(): void {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId)\n      this.refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.currentQuery\n    const prevOptions = this.options\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.currentResultState\n    const prevResultOptions = this.currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.currentQueryInitialState\n    const prevQueryResult = queryChange\n      ? this.currentResult\n      : this.previousQueryResult\n\n    const { state } = query\n    let { dataUpdatedAt, error, errorUpdatedAt, fetchStatus, status } = state\n    let isPreviousData = false\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = canFetch(query.options.networkMode)\n          ? 'fetching'\n          : 'paused'\n        if (!dataUpdatedAt) {\n          status = 'loading'\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle'\n      }\n    }\n\n    // Keep previous data if needed\n    if (\n      options.keepPreviousData &&\n      !state.dataUpdatedAt &&\n      prevQueryResult?.isSuccess &&\n      status !== 'error'\n    ) {\n      data = prevQueryResult.data\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt\n      status = prevQueryResult.status\n      isPreviousData = true\n    }\n    // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (\n        prevResult &&\n        state.data === prevResultState?.data &&\n        options.select === this.selectFn\n      ) {\n        data = this.selectResult\n      } else {\n        try {\n          this.selectFn = options.select\n          data = options.select(state.data)\n          data = replaceData(prevResult?.data, data, options)\n          this.selectResult = data\n          this.selectError = null\n        } catch (selectError) {\n          if (process.env.NODE_ENV !== 'production') {\n            this.client.getLogger().error(selectError)\n          }\n          this.selectError = selectError as TError\n        }\n      }\n    }\n    // Use query data\n    else {\n      data = state.data as unknown as TData\n    }\n\n    // Show placeholder data if needed\n    if (\n      typeof options.placeholderData !== 'undefined' &&\n      typeof data === 'undefined' &&\n      status === 'loading'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n      } else {\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (options.placeholderData as PlaceholderDataFunction<TQueryData>)()\n            : options.placeholderData\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData)\n            this.selectError = null\n          } catch (selectError) {\n            if (process.env.NODE_ENV !== 'production') {\n              this.client.getLogger().error(selectError)\n            }\n            this.selectError = selectError as TError\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success'\n        data = replaceData(prevResult?.data, placeholderData, options) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError as any\n      data = this.selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = fetchStatus === 'fetching'\n    const isLoading = status === 'loading'\n    const isError = status === 'error'\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        state.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove,\n    }\n\n    return result as QueryObserverResult<TData, TError>\n  }\n\n  updateResult(notifyOptions?: NotifyOptions): void {\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.currentQuery, this.options)\n    this.currentResultState = this.currentQuery.state\n    this.currentResultOptions = this.options\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.currentResult = nextResult\n\n    // Determine which callbacks to trigger\n    const defaultNotifyOptions: NotifyOptions = { cache: true }\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n      const notifyOnChangePropsValue =\n        typeof notifyOnChangeProps === 'function'\n          ? notifyOnChangeProps()\n          : notifyOnChangeProps\n\n      if (\n        notifyOnChangePropsValue === 'all' ||\n        (!notifyOnChangePropsValue && !this.trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.trackedProps,\n      )\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey]\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    if (notifyOptions?.listeners !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true\n    }\n\n    this.notify({ ...defaultNotifyOptions, ...notifyOptions })\n  }\n\n  private updateQuery(): void {\n    const query = this.client.getQueryCache().build(this.client, this.options)\n\n    if (query === this.currentQuery) {\n      return\n    }\n\n    const prevQuery = this.currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.currentQuery = query\n    this.currentQueryInitialState = query.state\n    this.previousQueryResult = this.currentResult\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(action: Action<TData, TError>): void {\n    const notifyOptions: NotifyOptions = {}\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true\n    }\n\n    this.updateResult(notifyOptions)\n\n    if (this.hasListeners()) {\n      this.updateTimers()\n    }\n  }\n\n  private notify(notifyOptions: NotifyOptions): void {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        this.options.onSuccess?.(this.currentResult.data!)\n        this.options.onSettled?.(this.currentResult.data!, null)\n      } else if (notifyOptions.onError) {\n        this.options.onError?.(this.currentResult.error!)\n        this.options.onSettled?.(undefined, this.currentResult.error!)\n      }\n\n      // Then trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated',\n        })\n      }\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    !query.state.dataUpdatedAt &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.dataUpdatedAt > 0 &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: typeof options['refetchOnMount'] &\n    typeof options['refetchOnWindowFocus'] &\n    typeof options['refetchOnReconnect'],\n) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    (query !== prevQuery || prevOptions.enabled === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return query.isStaleByTime(options.staleTime)\n}\n\n// this function would decide if we will update the observer's 'current'\n// properties after an optimistic reading via getOptimisticResult\nfunction shouldAssignObserverCurrentProperties<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  optimisticResult: QueryObserverResult<TData, TError>,\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n) {\n  // it is important to keep this condition like this for three reasons:\n  // 1. It will get removed in the v5\n  // 2. it reads: don't update the properties if we want to keep the previous\n  // data.\n  // 3. The opposite condition (!options.keepPreviousData) would fallthrough\n  // and will result in a bad decision\n  if (options.keepPreviousData) {\n    return false\n  }\n\n  // this means we want to put some placeholder data when pending and queryKey\n  // changed.\n  if (options.placeholderData !== undefined) {\n    // re-assign properties only if current data is placeholder data\n    // which means that data did not arrive yet, so, if there is some cached data\n    // we need to \"prepare\" to receive it\n    return optimisticResult.isPlaceholderData\n  }\n\n  // if the newly created result isn't what the observer is holding as current,\n  // then we'll need to update the properties as well\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true\n  }\n\n  // basically, just keep previous properties if nothing changed\n  return false\n}\n", "import { difference, replaceAt } from './utils'\nimport { notifyManager } from './notifyManager'\nimport { QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\nimport type {\n  DefaultedQueryObserverOptions,\n  QueryObserverOptions,\n  QueryObserverResult,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions } from './queryObserver'\n\ntype QueriesObserverListener = (result: QueryObserverResult[]) => void\n\nexport class QueriesObserver extends Subscribable<QueriesObserverListener> {\n  private client: QueryClient\n  private result: QueryObserverResult[]\n  private queries: QueryObserverOptions[]\n  private observers: QueryObserver[]\n  private observersMap: Record<string, QueryObserver>\n\n  constructor(client: QueryClient, queries?: QueryObserverOptions[]) {\n    super()\n\n    this.client = client\n    this.queries = []\n    this.result = []\n    this.observers = []\n    this.observersMap = {}\n\n    if (queries) {\n      this.setQueries(queries)\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.observers.forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.size) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.observers.forEach((observer) => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: QueryObserverOptions[],\n    notifyOptions?: NotifyOptions,\n  ): void {\n    this.queries = queries\n\n    notifyManager.batch(() => {\n      const prevObservers = this.observers\n\n      const newObserverMatches = this.findMatchingObservers(this.queries)\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach((match) =>\n        match.observer.setOptions(match.defaultedQueryOptions, notifyOptions),\n      )\n\n      const newObservers = newObserverMatches.map((match) => match.observer)\n      const newObserversMap = Object.fromEntries(\n        newObservers.map((observer) => [observer.options.queryHash, observer]),\n      )\n      const newResult = newObservers.map((observer) =>\n        observer.getCurrentResult(),\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index],\n      )\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.observers = newObservers\n      this.observersMap = newObserversMap\n      this.result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach((observer) => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n\n      this.notify()\n    })\n  }\n\n  getCurrentResult(): QueryObserverResult[] {\n    return this.result\n  }\n\n  getQueries() {\n    return this.observers.map((observer) => observer.getCurrentQuery())\n  }\n\n  getObservers() {\n    return this.observers\n  }\n\n  getOptimisticResult(queries: QueryObserverOptions[]): QueryObserverResult[] {\n    return this.findMatchingObservers(queries).map((match) =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions),\n    )\n  }\n\n  private findMatchingObservers(\n    queries: QueryObserverOptions[],\n  ): QueryObserverMatch[] {\n    const prevObservers = this.observers\n    const prevObserversMap = new Map(\n      prevObservers.map((observer) => [observer.options.queryHash, observer]),\n    )\n\n    const defaultedQueryOptions = queries.map((options) =>\n      this.client.defaultQueryOptions(options),\n    )\n\n    const matchingObservers: QueryObserverMatch[] =\n      defaultedQueryOptions.flatMap((defaultedOptions) => {\n        const match = prevObserversMap.get(defaultedOptions.queryHash)\n        if (match != null) {\n          return [{ defaultedQueryOptions: defaultedOptions, observer: match }]\n        }\n        return []\n      })\n\n    const matchedQueryHashes = new Set(\n      matchingObservers.map((match) => match.defaultedQueryOptions.queryHash),\n    )\n    const unmatchedQueries = defaultedQueryOptions.filter(\n      (defaultedOptions) => !matchedQueryHashes.has(defaultedOptions.queryHash),\n    )\n\n    const matchingObserversSet = new Set(\n      matchingObservers.map((match) => match.observer),\n    )\n    const unmatchedObservers = prevObservers.filter(\n      (prevObserver) => !matchingObserversSet.has(prevObserver),\n    )\n\n    const getObserver = (options: QueryObserverOptions): QueryObserver => {\n      const defaultedOptions = this.client.defaultQueryOptions(options)\n      const currentObserver = this.observersMap[defaultedOptions.queryHash!]\n      return currentObserver ?? new QueryObserver(this.client, defaultedOptions)\n    }\n\n    const newOrReusedObservers: QueryObserverMatch[] = unmatchedQueries.map(\n      (options, index) => {\n        if (options.keepPreviousData) {\n          // return previous data from one of the observers that no longer match\n          const previouslyUsedObserver = unmatchedObservers[index]\n          if (previouslyUsedObserver !== undefined) {\n            return {\n              defaultedQueryOptions: options,\n              observer: previouslyUsedObserver,\n            }\n          }\n        }\n        return {\n          defaultedQueryOptions: options,\n          observer: getObserver(options),\n        }\n      },\n    )\n\n    const sortMatchesByOrderOfQueries = (\n      a: QueryObserverMatch,\n      b: QueryObserverMatch,\n    ): number =>\n      defaultedQueryOptions.indexOf(a.defaultedQueryOptions) -\n      defaultedQueryOptions.indexOf(b.defaultedQueryOptions)\n\n    return matchingObservers\n      .concat(newOrReusedObservers)\n      .sort(sortMatchesByOrderOfQueries)\n  }\n\n  private onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.observers.indexOf(observer)\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result)\n      this.notify()\n    }\n  }\n\n  private notify(): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(this.result)\n      })\n    })\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: DefaultedQueryObserverOptions\n  observer: QueryObserver\n}\n", "import { QueryObserver } from './queryObserver'\nimport {\n  hasNextPage,\n  hasPreviousPage,\n  infiniteQueryBehavior,\n} from './infiniteQueryBehavior'\nimport type {\n  DefaultedInfiniteQueryObserverOptions,\n  FetchNextPageOptions,\n  FetchPreviousPageOptions,\n  InfiniteData,\n  InfiniteQueryObserverOptions,\n  InfiniteQueryObserverResult,\n  QueryKey,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions, ObserverFetchOptions } from './queryObserver'\nimport type { Query } from './query'\n\ntype InfiniteQueryObserverListener<TData, TError> = (\n  result: InfiniteQueryObserverResult<TData, TError>,\n) => void\n\nexport class InfiniteQueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends QueryKey = QueryK<PERSON>,\n> extends QueryObserver<\n  TQueryFnData,\n  TError,\n  InfiniteData<TData>,\n  InfiniteData<TQueryData>,\n  TQueryK<PERSON>\n> {\n  // Type override\n  subscribe!: (\n    listener?: InfiniteQueryObserverListener<TData, TError>,\n  ) => () => void\n\n  // Type override\n  getCurrentResult!: () => InfiniteQueryObserverResult<TData, TError>\n\n  // Type override\n  protected fetch!: (\n    fetchOptions: ObserverFetchOptions,\n  ) => Promise<InfiniteQueryObserverResult<TData, TError>>\n\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  constructor(\n    client: QueryClient,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super(client, options)\n  }\n\n  protected bindMethods(): void {\n    super.bindMethods()\n    this.fetchNextPage = this.fetchNextPage.bind(this)\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this)\n  }\n\n  setOptions(\n    options?: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    super.setOptions(\n      {\n        ...options,\n        behavior: infiniteQueryBehavior(),\n      },\n      notifyOptions,\n    )\n  }\n\n  getOptimisticResult(\n    options: DefaultedInfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    options.behavior = infiniteQueryBehavior()\n    return super.getOptimisticResult(options) as InfiniteQueryObserverResult<\n      TData,\n      TError\n    >\n  }\n\n  fetchNextPage({ pageParam, ...options }: FetchNextPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'forward', pageParam },\n      },\n    })\n  }\n\n  fetchPreviousPage({\n    pageParam,\n    ...options\n  }: FetchPreviousPageOptions = {}): Promise<\n    InfiniteQueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: { direction: 'backward', pageParam },\n      },\n    })\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, InfiniteData<TQueryData>, TQueryKey>,\n    options: InfiniteQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): InfiniteQueryObserverResult<TData, TError> {\n    const { state } = query\n    const result = super.createResult(query, options)\n\n    const { isFetching, isRefetching } = result\n\n    const isFetchingNextPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'forward'\n\n    const isFetchingPreviousPage =\n      isFetching && state.fetchMeta?.fetchMore?.direction === 'backward'\n\n    return {\n      ...result,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data?.pages),\n      hasPreviousPage: hasPreviousPage(options, state.data?.pages),\n      isFetchingNextPage,\n      isFetchingPreviousPage,\n      isRefetching:\n        isRefetching && !isFetchingNextPage && !isFetchingPreviousPage,\n    }\n  }\n}\n", "import { getDefaultState } from './mutation'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  MutateOptions,\n  MutationObserverBaseResult,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\ninterface NotifyOptions {\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  private client: QueryClient\n  private currentResult!: MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  >\n  private currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  private mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options?: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options\n    this.options = this.client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.currentMutation,\n        observer: this,\n      })\n    }\n    this.currentMutation?.setOptions(this.options)\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.updateResult()\n\n    // Determine which callbacks to trigger\n    const notifyOptions: NotifyOptions = {\n      listeners: true,\n    }\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true\n    }\n\n    this.notify(notifyOptions)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.currentResult\n  }\n\n  reset(): void {\n    this.currentMutation = undefined\n    this.updateResult()\n    this.notify({ listeners: true })\n  }\n\n  mutate(\n    variables?: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.mutateOptions = options\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this)\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, {\n      ...this.options,\n      variables:\n        typeof variables !== 'undefined' ? variables : this.options.variables,\n    })\n\n    this.currentMutation.addObserver(this)\n\n    return this.currentMutation.execute()\n  }\n\n  private updateResult(): void {\n    const state = this.currentMutation\n      ? this.currentMutation.state\n      : getDefaultState<TData, TError, TVariables, TContext>()\n\n    const isLoading = state.status === 'loading'\n    const result: MutationObserverBaseResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    > = {\n      ...state,\n      isLoading,\n      isPending: isLoading,\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    }\n\n    this.currentResult = result as MutationObserverResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    >\n  }\n\n  private notify(options: NotifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions && this.hasListeners()) {\n        if (options.onSuccess) {\n          this.mutateOptions.onSuccess?.(\n            this.currentResult.data!,\n            this.currentResult.variables!,\n            this.currentResult.context!,\n          )\n          this.mutateOptions.onSettled?.(\n            this.currentResult.data!,\n            null,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        } else if (options.onError) {\n          this.mutateOptions.onError?.(\n            this.currentResult.error!,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n          this.mutateOptions.onSettled?.(\n            undefined,\n            this.currentResult.error,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      if (options.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n    })\n  }\n}\n", "import type { QueryClient } from './queryClient'\nimport type { Query, QueryState } from './query'\nimport type {\n  MutationKey,\n  MutationOptions,\n  QueryKey,\n  QueryOptions,\n} from './types'\nimport type { Mutation, MutationState } from './mutation'\n\n// TYPES\n\nexport interface DehydrateOptions {\n  dehydrateMutations?: boolean\n  dehydrateQueries?: boolean\n  shouldDehydrateMutation?: ShouldDehydrateMutationFunction\n  shouldDehydrateQuery?: ShouldDehydrateQueryFunction\n}\n\nexport interface HydrateOptions {\n  defaultOptions?: {\n    queries?: QueryOptions\n    mutations?: MutationOptions\n  }\n}\n\ninterface DehydratedMutation {\n  mutationKey?: MutationKey\n  state: MutationState\n}\n\ninterface DehydratedQuery {\n  queryHash: string\n  queryKey: QueryKey\n  state: QueryState\n}\n\nexport interface DehydratedState {\n  mutations: DehydratedMutation[]\n  queries: DehydratedQuery[]\n}\n\nexport type ShouldDehydrateQueryFunction = (query: Query) => boolean\n\nexport type ShouldDehydrateMutationFunction = (mutation: Mutation) => boolean\n\n// FUNCTIONS\n\nfunction dehydrateMutation(mutation: Mutation): DehydratedMutation {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state,\n  }\n}\n\n// Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\nfunction dehydrateQuery(query: Query): DehydratedQuery {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash,\n  }\n}\n\nexport function defaultShouldDehydrateMutation(mutation: Mutation) {\n  return mutation.state.isPaused\n}\n\nexport function defaultShouldDehydrateQuery(query: Query) {\n  return query.state.status === 'success'\n}\n\nexport function dehydrate(\n  client: QueryClient,\n  options: DehydrateOptions = {},\n): DehydratedState {\n  const mutations: DehydratedMutation[] = []\n  const queries: DehydratedQuery[] = []\n\n  if (options.dehydrateMutations !== false) {\n    const shouldDehydrateMutation =\n      options.shouldDehydrateMutation || defaultShouldDehydrateMutation\n\n    client\n      .getMutationCache()\n      .getAll()\n      .forEach((mutation) => {\n        if (shouldDehydrateMutation(mutation)) {\n          mutations.push(dehydrateMutation(mutation))\n        }\n      })\n  }\n\n  if (options.dehydrateQueries !== false) {\n    const shouldDehydrateQuery =\n      options.shouldDehydrateQuery || defaultShouldDehydrateQuery\n\n    client\n      .getQueryCache()\n      .getAll()\n      .forEach((query) => {\n        if (shouldDehydrateQuery(query)) {\n          queries.push(dehydrateQuery(query))\n        }\n      })\n  }\n\n  return { mutations, queries }\n}\n\nexport function hydrate(\n  client: QueryClient,\n  dehydratedState: unknown,\n  options?: HydrateOptions,\n): void {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return\n  }\n\n  const mutationCache = client.getMutationCache()\n  const queryCache = client.getQueryCache()\n\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const mutations = (dehydratedState as DehydratedState).mutations || []\n  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n  const queries = (dehydratedState as DehydratedState).queries || []\n\n  mutations.forEach((dehydratedMutation) => {\n    mutationCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.mutations,\n        mutationKey: dehydratedMutation.mutationKey,\n      },\n      dehydratedMutation.state,\n    )\n  })\n\n  queries.forEach(({ queryKey, state, queryHash }) => {\n    const query = queryCache.get(queryHash)\n\n    // Do not hydrate if an existing query exists with newer data\n    if (query) {\n      if (query.state.dataUpdatedAt < state.dataUpdatedAt) {\n        // omit fetchStatus from dehydrated state\n        // so that query stays in its current fetchStatus\n        const { fetchStatus: _ignored, ...dehydratedQueryState } = state\n        query.setState(dehydratedQueryState)\n      }\n      return\n    }\n\n    // Restore query\n    queryCache.build(\n      client,\n      {\n        ...options?.defaultOptions?.queries,\n        queryKey,\n        queryHash,\n      },\n      // Reset fetch status to idle to avoid\n      // query being stuck in fetching state upon hydration\n      {\n        ...state,\n        fetchStatus: 'idle',\n      },\n    )\n  })\n}\n"], "names": ["Subscribable", "constructor", "listeners", "Set", "subscribe", "bind", "listener", "identity", "add", "onSubscribe", "delete", "onUnsubscribe", "hasListeners", "size", "isServer", "window", "noop", "undefined", "functionalUpdate", "updater", "input", "isValidTimeout", "value", "Infinity", "difference", "array1", "array2", "filter", "x", "includes", "replaceAt", "array", "index", "copy", "slice", "timeUntilStale", "updatedAt", "staleTime", "Math", "max", "Date", "now", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg1", "arg2", "arg3", "is<PERSON>uery<PERSON>ey", "query<PERSON><PERSON>", "queryFn", "parseMutationArgs", "<PERSON><PERSON><PERSON>", "mutationFn", "parseFilter<PERSON><PERSON>s", "parseMutationFilterArgs", "matchQuery", "filters", "query", "type", "exact", "fetchStatus", "predicate", "stale", "queryHash", "hashQueryKeyByOptions", "options", "partialMatchKey", "isActive", "isStale", "state", "matchMutation", "mutation", "fetching", "hashQuery<PERSON>ey", "status", "hashFn", "queryKeyHashFn", "JSON", "stringify", "_", "val", "isPlainObject", "Object", "keys", "sort", "reduce", "result", "key", "a", "b", "partialDeepEqual", "some", "replaceEqualDeep", "is<PERSON><PERSON>A<PERSON>y", "aSize", "length", "bItems", "bSize", "equalItems", "i", "shallowEqualObjects", "Array", "isArray", "o", "hasObjectPrototype", "ctor", "prot", "prototype", "hasOwnProperty", "toString", "call", "isError", "Error", "sleep", "timeout", "Promise", "resolve", "setTimeout", "scheduleMicrotask", "callback", "then", "getAbortController", "AbortController", "replaceData", "prevData", "data", "isDataEqual", "structuralSharing", "FocusManager", "setup", "onFocus", "addEventListener", "removeEventListener", "cleanup", "setEventListener", "focused", "setFocused", "changed", "for<PERSON>ach", "isFocused", "document", "visibilityState", "focusManager", "onlineEvents", "OnlineManager", "onOnline", "event", "online", "setOnline", "isOnline", "navigator", "onLine", "onlineManager", "defaultRetryDelay", "failureCount", "min", "canFetch", "networkMode", "CancelledError", "revert", "silent", "isCancelledError", "createRetryer", "config", "isRetryCancelled", "isResolved", "continueFn", "promiseResolve", "promiseReject", "promise", "outerResolve", "outerReject", "cancel", "cancelOptions", "reject", "abort", "cancelRetry", "continueRetry", "shouldP<PERSON>e", "onSuccess", "onError", "pause", "continueResolve", "canContinue", "onPause", "onContinue", "run", "promiseOrValue", "fn", "error", "catch", "retry", "retry<PERSON><PERSON><PERSON>", "delay", "shouldRetry", "onFail", "continue", "didContinue", "defaultLogger", "console", "createNotifyManager", "queue", "transactions", "notifyFn", "batchNotifyFn", "batch", "flush", "schedule", "push", "batchCalls", "args", "originalQueue", "setNotifyFunction", "setBatchNotifyFunction", "notify<PERSON><PERSON>ger", "Removable", "destroy", "clearGcTimeout", "scheduleGc", "cacheTime", "gcTimeout", "optionalRemove", "updateCacheTime", "newCacheTime", "clearTimeout", "Query", "abortSignalConsumed", "defaultOptions", "setOptions", "observers", "cache", "logger", "initialState", "getDefaultState", "meta", "remove", "setData", "newData", "dispatch", "dataUpdatedAt", "manual", "setState", "setStateOptions", "retryer", "reset", "observer", "enabled", "isDisabled", "getObserversCount", "isInvalidated", "getCurrentResult", "isStaleByTime", "find", "shouldFetchOnWindowFocus", "refetch", "cancelRefetch", "shouldFetchOnReconnect", "addObserver", "notify", "removeObserver", "invalidate", "fetch", "fetchOptions", "abortController", "queryFnContext", "pageParam", "addSignalProperty", "object", "defineProperty", "enumerable", "get", "signal", "fetchFn", "context", "behavior", "onFetch", "revertState", "fetchMeta", "onSettled", "isFetchingOptimistic", "action", "reducer", "fetchFailureCount", "fetchFailureReason", "dataUpdateCount", "errorUpdateCount", "errorUpdatedAt", "onQueryUpdate", "initialData", "hasData", "initialDataUpdatedAt", "Query<PERSON>ache", "queries", "queriesMap", "build", "client", "<PERSON><PERSON><PERSON><PERSON>", "defaultQueryOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "queryInMap", "clear", "getAll", "findAll", "Mutation", "mutationId", "mutationCache", "execute", "executeMutation", "variables", "restored", "onMutate", "process", "failureReason", "isPaused", "onMutationUpdate", "MutationCache", "mutations", "defaultMutationOptions", "getMutationDefaults", "resumePausedMutations", "resuming", "pausedMutations", "infiniteQueryBehavior", "refetchPage", "fetchMore", "isFetchingNextPage", "direction", "isFetchingPreviousPage", "oldPages", "pages", "oldPageParams", "pageParams", "newPageParams", "cancelled", "aborted", "buildNewPages", "param", "page", "previous", "fetchPage", "queryFnResult", "getNextPageParam", "getPreviousPageParam", "shouldFetchFirstPage", "shouldFetchNextPage", "finalPromise", "hasNextPage", "nextPageParam", "hasPreviousPage", "previousPageParam", "QueryClient", "queryCache", "queryDefaults", "mutationDefaults", "mountCount", "mount", "unsubscribeFocus", "unsubscribeOnline", "unmount", "isFetching", "isMutating", "getQueryData", "ensureQueryData", "parsedOptions", "cachedData", "<PERSON><PERSON><PERSON><PERSON>", "getQueriesData", "query<PERSON>eyOrFilters", "get<PERSON><PERSON><PERSON><PERSON>ache", "map", "setQueryData", "defaultedOptions", "setQueriesData", "getQueryState", "removeQueries", "resetQueries", "refetchFilters", "refetchQueries", "cancelQueries", "promises", "all", "invalidateQueries", "refetchType", "throwOnError", "prefetch<PERSON><PERSON>y", "fetchInfiniteQuery", "prefetchInfiniteQuery", "getMutationCache", "getDefaultOptions", "setDefaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "firstMatchingDefaults", "matchingDefaults", "setMutationDefaults", "_defaulted", "refetchOnReconnect", "useErrorBoundary", "suspense", "QueryObserver", "trackedProps", "selectError", "bindMethods", "<PERSON><PERSON><PERSON><PERSON>", "shouldFetchOnMount", "executeFetch", "updateTimers", "shouldFetchOn", "refetchOnWindowFocus", "clearStaleTimeout", "clearRefetchInterval", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "updateQuery", "mounted", "shouldFetchOptionally", "updateResult", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "currentRefetchInterval", "updateRefetchInterval", "getOptimisticResult", "createResult", "shouldAssignObserverCurrentProperties", "currentResult", "currentResultOptions", "currentResultState", "trackResult", "trackedResult", "configurable", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchOptimistic", "time", "staleTimeoutId", "refetchInterval", "nextInterval", "refetchIntervalId", "setInterval", "refetchIntervalInBackground", "clearInterval", "prevResult", "prevResultState", "prevResultOptions", "query<PERSON>hange", "queryInitialState", "currentQueryInitialState", "prevQueryResult", "previousQueryResult", "isPreviousData", "isPlaceholderData", "_optimisticResults", "fetchOnMount", "fetchOptionally", "keepPreviousData", "isSuccess", "select", "selectFn", "selectResult", "placeholderData", "isLoading", "isInitialLoading", "isFetched", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isRefetchError", "nextResult", "defaultNotifyOptions", "shouldNotifyListeners", "notifyOnChangeProps", "notifyOnChangePropsValue", "includedProps", "<PERSON><PERSON><PERSON>", "has", "shouldLoadOnMount", "retryOnMount", "refetchOnMount", "field", "optimisticResult", "QueriesObserver", "observersMap", "setQueries", "onUpdate", "prevObservers", "newObserverMatches", "findMatchingObservers", "match", "defaultedQueryOptions", "newObservers", "newObserversMap", "fromEntries", "newResult", "hasIndexChange", "getQueries", "getObservers", "prevObserversMap", "Map", "matchingObservers", "flatMap", "matchedQueryHashes", "unmatchedQueries", "matchingObserversSet", "unmatchedObservers", "prevObserver", "getObserver", "currentObserver", "newOrReusedObservers", "previouslyUsedObserver", "sortMatchesByOrderOfQueries", "indexOf", "concat", "InfiniteQueryObserver", "fetchNextPage", "fetchPreviousPage", "MutationObserver", "mutate", "currentMutation", "mutateOptions", "isPending", "isIdle", "dehydrateMutation", "dehydrate<PERSON><PERSON>y", "defaultShouldDehydrateMutation", "defaultShouldDehydrateQuery", "dehydrate", "dehydrateMutations", "shouldDehydrateMutation", "dehydrateQueries", "shouldDehydrateQuery", "hydrate", "dehydratedState", "dehydratedMutation", "_ignored", "dehydratedQueryState"], "mappings": ";;;;;;EAEO,MAAMA,YAAN,CAA0D;EAG/DC,EAAAA,WAAW,GAAG;EACZ,IAAA,IAAA,CAAKC,SAAL,GAAiB,IAAIC,GAAJ,EAAjB,CAAA;MACA,IAAKC,CAAAA,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAeC,IAAf,CAAoB,IAApB,CAAjB,CAAA;EACD,GAAA;;IAEDD,SAAS,CAACE,QAAD,EAAkC;EACzC,IAAA,MAAMC,QAAQ,GAAG;EAAED,MAAAA,QAAAA;OAAnB,CAAA;EACA,IAAA,IAAA,CAAKJ,SAAL,CAAeM,GAAf,CAAmBD,QAAnB,CAAA,CAAA;EAEA,IAAA,IAAA,CAAKE,WAAL,EAAA,CAAA;EAEA,IAAA,OAAO,MAAM;EACX,MAAA,IAAA,CAAKP,SAAL,CAAeQ,MAAf,CAAsBH,QAAtB,CAAA,CAAA;EACA,MAAA,IAAA,CAAKI,aAAL,EAAA,CAAA;OAFF,CAAA;EAID,GAAA;;EAEDC,EAAAA,YAAY,GAAY;EACtB,IAAA,OAAO,IAAKV,CAAAA,SAAL,CAAeW,IAAf,GAAsB,CAA7B,CAAA;EACD,GAAA;;EAESJ,EAAAA,WAAW,GAAS;EAE7B,GAAA;;EAESE,EAAAA,aAAa,GAAS;EAE/B,GAAA;;EA9B8D;;ECUjE;EAwDA;AAEO,QAAMG,QAAQ,GAAG,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,MAAA,IAAUA,OAA5D;EAEA,SAASC,IAAT,GAA2B;EAChC,EAAA,OAAOC,SAAP,CAAA;EACD,CAAA;EAEM,SAASC,gBAAT,CACLC,OADK,EAELC,KAFK,EAGI;IACT,OAAO,OAAOD,OAAP,KAAmB,UAAnB,GACFA,OAAD,CAAiDC,KAAjD,CADG,GAEHD,OAFJ,CAAA;EAGD,CAAA;EAEM,SAASE,cAAT,CAAwBC,KAAxB,EAAyD;IAC9D,OAAO,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,IAAI,CAAtC,IAA2CA,KAAK,KAAKC,QAA5D,CAAA;EACD,CAAA;EAEM,SAASC,UAAT,CAAuBC,MAAvB,EAAoCC,MAApC,EAAsD;EAC3D,EAAA,OAAOD,MAAM,CAACE,MAAP,CAAeC,CAAD,IAAO,CAACF,MAAM,CAACG,QAAP,CAAgBD,CAAhB,CAAtB,CAAP,CAAA;EACD,CAAA;EAEM,SAASE,SAAT,CAAsBC,KAAtB,EAAkCC,KAAlC,EAAiDV,KAAjD,EAAgE;EACrE,EAAA,MAAMW,IAAI,GAAGF,KAAK,CAACG,KAAN,CAAY,CAAZ,CAAb,CAAA;EACAD,EAAAA,IAAI,CAACD,KAAD,CAAJ,GAAcV,KAAd,CAAA;EACA,EAAA,OAAOW,IAAP,CAAA;EACD,CAAA;EAEM,SAASE,cAAT,CAAwBC,SAAxB,EAA2CC,SAA3C,EAAuE;EAC5E,EAAA,OAAOC,IAAI,CAACC,GAAL,CAASH,SAAS,IAAIC,SAAS,IAAI,CAAjB,CAAT,GAA+BG,IAAI,CAACC,GAAL,EAAxC,EAAoD,CAApD,CAAP,CAAA;EACD,CAAA;EAEM,SAASC,cAAT,CAILC,IAJK,EAKLC,IALK,EAMLC,IANK,EAOK;EACV,EAAA,IAAI,CAACC,UAAU,CAACH,IAAD,CAAf,EAAuB;EACrB,IAAA,OAAOA,IAAP,CAAA;EACD,GAAA;;EAED,EAAA,IAAI,OAAOC,IAAP,KAAgB,UAApB,EAAgC;MAC9B,OAAO,EAAE,GAAGC,IAAL;EAAWE,MAAAA,QAAQ,EAAEJ,IAArB;EAA2BK,MAAAA,OAAO,EAAEJ,IAAAA;OAA3C,CAAA;EACD,GAAA;;IAED,OAAO,EAAE,GAAGA,IAAL;EAAWG,IAAAA,QAAQ,EAAEJ,IAAAA;KAA5B,CAAA;EACD,CAAA;EAEM,SAASM,iBAAT,CAGLN,IAHK,EAILC,IAJK,EAKLC,IALK,EAMK;EACV,EAAA,IAAIC,UAAU,CAACH,IAAD,CAAd,EAAsB;EACpB,IAAA,IAAI,OAAOC,IAAP,KAAgB,UAApB,EAAgC;QAC9B,OAAO,EAAE,GAAGC,IAAL;EAAWK,QAAAA,WAAW,EAAEP,IAAxB;EAA8BQ,QAAAA,UAAU,EAAEP,IAAAA;SAAjD,CAAA;EACD,KAAA;;MACD,OAAO,EAAE,GAAGA,IAAL;EAAWM,MAAAA,WAAW,EAAEP,IAAAA;OAA/B,CAAA;EACD,GAAA;;EAED,EAAA,IAAI,OAAOA,IAAP,KAAgB,UAApB,EAAgC;MAC9B,OAAO,EAAE,GAAGC,IAAL;EAAWO,MAAAA,UAAU,EAAER,IAAAA;OAA9B,CAAA;EACD,GAAA;;EAED,EAAA,OAAO,EAAE,GAAGA,IAAAA;KAAZ,CAAA;EACD,CAAA;EAEM,SAASS,eAAT,CAILT,IAJK,EAKLC,IALK,EAMLC,IANK,EAO6B;IAClC,OACEC,UAAU,CAACH,IAAD,CAAV,GAAmB,CAAC,EAAE,GAAGC,IAAL;EAAWG,IAAAA,QAAQ,EAAEJ,IAAAA;KAAtB,EAA8BE,IAA9B,CAAnB,GAAyD,CAACF,IAAI,IAAI,EAAT,EAAaC,IAAb,CAD3D,CAAA;EAGD,CAAA;EAEM,SAASS,uBAAT,CAILV,IAJK,EAKLC,IALK,EAMLC,IANK,EAO6B;IAClC,OACEC,UAAU,CAACH,IAAD,CAAV,GACI,CAAC,EAAE,GAAGC,IAAL;EAAWM,IAAAA,WAAW,EAAEP,IAAAA;KAAzB,EAAiCE,IAAjC,CADJ,GAEI,CAACF,IAAI,IAAI,EAAT,EAAaC,IAAb,CAHN,CAAA;EAKD,CAAA;EAEM,SAASU,UAAT,CACLC,OADK,EAELC,KAFK,EAGI;IACT,MAAM;EACJC,IAAAA,IAAI,GAAG,KADH;MAEJC,KAFI;MAGJC,WAHI;MAIJC,SAJI;MAKJb,QALI;EAMJc,IAAAA,KAAAA;EANI,GAAA,GAOFN,OAPJ,CAAA;;EASA,EAAA,IAAIT,UAAU,CAACC,QAAD,CAAd,EAA0B;EACxB,IAAA,IAAIW,KAAJ,EAAW;EACT,MAAA,IAAIF,KAAK,CAACM,SAAN,KAAoBC,qBAAqB,CAAChB,QAAD,EAAWS,KAAK,CAACQ,OAAjB,CAA7C,EAAwE;EACtE,QAAA,OAAO,KAAP,CAAA;EACD,OAAA;OAHH,MAIO,IAAI,CAACC,eAAe,CAACT,KAAK,CAACT,QAAP,EAAiBA,QAAjB,CAApB,EAAgD;EACrD,MAAA,OAAO,KAAP,CAAA;EACD,KAAA;EACF,GAAA;;IAED,IAAIU,IAAI,KAAK,KAAb,EAAoB;EAClB,IAAA,MAAMS,QAAQ,GAAGV,KAAK,CAACU,QAAN,EAAjB,CAAA;;EACA,IAAA,IAAIT,IAAI,KAAK,QAAT,IAAqB,CAACS,QAA1B,EAAoC;EAClC,MAAA,OAAO,KAAP,CAAA;EACD,KAAA;;EACD,IAAA,IAAIT,IAAI,KAAK,UAAT,IAAuBS,QAA3B,EAAqC;EACnC,MAAA,OAAO,KAAP,CAAA;EACD,KAAA;EACF,GAAA;;IAED,IAAI,OAAOL,KAAP,KAAiB,SAAjB,IAA8BL,KAAK,CAACW,OAAN,EAAoBN,KAAAA,KAAtD,EAA6D;EAC3D,IAAA,OAAO,KAAP,CAAA;EACD,GAAA;;EAED,EAAA,IACE,OAAOF,WAAP,KAAuB,WAAvB,IACAA,WAAW,KAAKH,KAAK,CAACY,KAAN,CAAYT,WAF9B,EAGE;EACA,IAAA,OAAO,KAAP,CAAA;EACD,GAAA;;EAED,EAAA,IAAIC,SAAS,IAAI,CAACA,SAAS,CAACJ,KAAD,CAA3B,EAAoC;EAClC,IAAA,OAAO,KAAP,CAAA;EACD,GAAA;;EAED,EAAA,OAAO,IAAP,CAAA;EACD,CAAA;EAEM,SAASa,aAAT,CACLd,OADK,EAELe,QAFK,EAGI;IACT,MAAM;MAAEZ,KAAF;MAASa,QAAT;MAAmBX,SAAnB;EAA8BV,IAAAA,WAAAA;EAA9B,GAAA,GAA8CK,OAApD,CAAA;;EACA,EAAA,IAAIT,UAAU,CAACI,WAAD,CAAd,EAA6B;EAC3B,IAAA,IAAI,CAACoB,QAAQ,CAACN,OAAT,CAAiBd,WAAtB,EAAmC;EACjC,MAAA,OAAO,KAAP,CAAA;EACD,KAAA;;EACD,IAAA,IAAIQ,KAAJ,EAAW;EACT,MAAA,IACEc,YAAY,CAACF,QAAQ,CAACN,OAAT,CAAiBd,WAAlB,CAAZ,KAA+CsB,YAAY,CAACtB,WAAD,CAD7D,EAEE;EACA,QAAA,OAAO,KAAP,CAAA;EACD,OAAA;EACF,KAND,MAMO,IAAI,CAACe,eAAe,CAACK,QAAQ,CAACN,OAAT,CAAiBd,WAAlB,EAA+BA,WAA/B,CAApB,EAAiE;EACtE,MAAA,OAAO,KAAP,CAAA;EACD,KAAA;EACF,GAAA;;EAED,EAAA,IACE,OAAOqB,QAAP,KAAoB,SAApB,IACCD,QAAQ,CAACF,KAAT,CAAeK,MAAf,KAA0B,SAA3B,KAA0CF,QAF5C,EAGE;EACA,IAAA,OAAO,KAAP,CAAA;EACD,GAAA;;EAED,EAAA,IAAIX,SAAS,IAAI,CAACA,SAAS,CAACU,QAAD,CAA3B,EAAuC;EACrC,IAAA,OAAO,KAAP,CAAA;EACD,GAAA;;EAED,EAAA,OAAO,IAAP,CAAA;EACD,CAAA;EAEM,SAASP,qBAAT,CACLhB,QADK,EAELiB,OAFK,EAGG;IACR,MAAMU,MAAM,GAAG,CAAAV,OAAO,IAAA,IAAP,YAAAA,OAAO,CAAEW,cAAT,KAA2BH,YAA1C,CAAA;IACA,OAAOE,MAAM,CAAC3B,QAAD,CAAb,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;;EACO,SAASyB,YAAT,CAAsBzB,QAAtB,EAAkD;EACvD,EAAA,OAAO6B,IAAI,CAACC,SAAL,CAAe9B,QAAf,EAAyB,CAAC+B,CAAD,EAAIC,GAAJ,KAC9BC,aAAa,CAACD,GAAD,CAAb,GACIE,MAAM,CAACC,IAAP,CAAYH,GAAZ,CACGI,CAAAA,IADH,EAEGC,CAAAA,MAFH,CAEU,CAACC,MAAD,EAASC,GAAT,KAAiB;EACvBD,IAAAA,MAAM,CAACC,GAAD,CAAN,GAAcP,GAAG,CAACO,GAAD,CAAjB,CAAA;EACA,IAAA,OAAOD,MAAP,CAAA;EACD,GALH,EAKK,EALL,CADJ,GAOIN,GARC,CAAP,CAAA;EAUD,CAAA;EAED;EACA;EACA;;EACO,SAASd,eAAT,CAAyBsB,CAAzB,EAAsCC,CAAtC,EAA4D;EACjE,EAAA,OAAOC,gBAAgB,CAACF,CAAD,EAAIC,CAAJ,CAAvB,CAAA;EACD,CAAA;EAED;EACA;EACA;;EACO,SAASC,gBAAT,CAA0BF,CAA1B,EAAkCC,CAAlC,EAAmD;IACxD,IAAID,CAAC,KAAKC,CAAV,EAAa;EACX,IAAA,OAAO,IAAP,CAAA;EACD,GAAA;;EAED,EAAA,IAAI,OAAOD,CAAP,KAAa,OAAOC,CAAxB,EAA2B;EACzB,IAAA,OAAO,KAAP,CAAA;EACD,GAAA;;EAED,EAAA,IAAID,CAAC,IAAIC,CAAL,IAAU,OAAOD,CAAP,KAAa,QAAvB,IAAmC,OAAOC,CAAP,KAAa,QAApD,EAA8D;MAC5D,OAAO,CAACP,MAAM,CAACC,IAAP,CAAYM,CAAZ,CAAeE,CAAAA,IAAf,CAAqBJ,GAAD,IAAS,CAACG,gBAAgB,CAACF,CAAC,CAACD,GAAD,CAAF,EAASE,CAAC,CAACF,GAAD,CAAV,CAA9C,CAAR,CAAA;EACD,GAAA;;EAED,EAAA,OAAO,KAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;EACA;;EAEO,SAASK,gBAAT,CAA0BJ,CAA1B,EAAkCC,CAAlC,EAA+C;IACpD,IAAID,CAAC,KAAKC,CAAV,EAAa;EACX,IAAA,OAAOD,CAAP,CAAA;EACD,GAAA;;IAED,MAAMxD,KAAK,GAAG6D,YAAY,CAACL,CAAD,CAAZ,IAAmBK,YAAY,CAACJ,CAAD,CAA7C,CAAA;;IAEA,IAAIzD,KAAK,IAAKiD,aAAa,CAACO,CAAD,CAAb,IAAoBP,aAAa,CAACQ,CAAD,CAA/C,EAAqD;EACnD,IAAA,MAAMK,KAAK,GAAG9D,KAAK,GAAGwD,CAAC,CAACO,MAAL,GAAcb,MAAM,CAACC,IAAP,CAAYK,CAAZ,EAAeO,MAAhD,CAAA;MACA,MAAMC,MAAM,GAAGhE,KAAK,GAAGyD,CAAH,GAAOP,MAAM,CAACC,IAAP,CAAYM,CAAZ,CAA3B,CAAA;EACA,IAAA,MAAMQ,KAAK,GAAGD,MAAM,CAACD,MAArB,CAAA;EACA,IAAA,MAAM7D,IAAS,GAAGF,KAAK,GAAG,EAAH,GAAQ,EAA/B,CAAA;MAEA,IAAIkE,UAAU,GAAG,CAAjB,CAAA;;MAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,KAApB,EAA2BE,CAAC,EAA5B,EAAgC;QAC9B,MAAMZ,GAAG,GAAGvD,KAAK,GAAGmE,CAAH,GAAOH,MAAM,CAACG,CAAD,CAA9B,CAAA;EACAjE,MAAAA,IAAI,CAACqD,GAAD,CAAJ,GAAYK,gBAAgB,CAACJ,CAAC,CAACD,GAAD,CAAF,EAASE,CAAC,CAACF,GAAD,CAAV,CAA5B,CAAA;;QACA,IAAIrD,IAAI,CAACqD,GAAD,CAAJ,KAAcC,CAAC,CAACD,GAAD,CAAnB,EAA0B;UACxBW,UAAU,EAAA,CAAA;EACX,OAAA;EACF,KAAA;;MAED,OAAOJ,KAAK,KAAKG,KAAV,IAAmBC,UAAU,KAAKJ,KAAlC,GAA0CN,CAA1C,GAA8CtD,IAArD,CAAA;EACD,GAAA;;EAED,EAAA,OAAOuD,CAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;;EACO,SAASW,mBAAT,CAAgCZ,CAAhC,EAAsCC,CAAtC,EAAqD;IAC1D,IAAKD,CAAC,IAAI,CAACC,CAAP,IAAcA,CAAC,IAAI,CAACD,CAAxB,EAA4B;EAC1B,IAAA,OAAO,KAAP,CAAA;EACD,GAAA;;EAED,EAAA,KAAK,MAAMD,GAAX,IAAkBC,CAAlB,EAAqB;MACnB,IAAIA,CAAC,CAACD,GAAD,CAAD,KAAWE,CAAC,CAACF,GAAD,CAAhB,EAAuB;EACrB,MAAA,OAAO,KAAP,CAAA;EACD,KAAA;EACF,GAAA;;EAED,EAAA,OAAO,IAAP,CAAA;EACD,CAAA;EAEM,SAASM,YAAT,CAAsBtE,KAAtB,EAAsC;EAC3C,EAAA,OAAO8E,KAAK,CAACC,OAAN,CAAc/E,KAAd,KAAwBA,KAAK,CAACwE,MAAN,KAAiBb,MAAM,CAACC,IAAP,CAAY5D,KAAZ,EAAmBwE,MAAnE,CAAA;EACD;;EAGM,SAASd,aAAT,CAAuBsB,CAAvB,EAA4C;EACjD,EAAA,IAAI,CAACC,kBAAkB,CAACD,CAAD,CAAvB,EAA4B;EAC1B,IAAA,OAAO,KAAP,CAAA;EACD,GAHgD;;;EAMjD,EAAA,MAAME,IAAI,GAAGF,CAAC,CAACrG,WAAf,CAAA;;EACA,EAAA,IAAI,OAAOuG,IAAP,KAAgB,WAApB,EAAiC;EAC/B,IAAA,OAAO,IAAP,CAAA;EACD,GATgD;;;EAYjD,EAAA,MAAMC,IAAI,GAAGD,IAAI,CAACE,SAAlB,CAAA;;EACA,EAAA,IAAI,CAACH,kBAAkB,CAACE,IAAD,CAAvB,EAA+B;EAC7B,IAAA,OAAO,KAAP,CAAA;EACD,GAfgD;;;EAkBjD,EAAA,IAAI,CAACA,IAAI,CAACE,cAAL,CAAoB,eAApB,CAAL,EAA2C;EACzC,IAAA,OAAO,KAAP,CAAA;EACD,GApBgD;;;EAuBjD,EAAA,OAAO,IAAP,CAAA;EACD,CAAA;;EAED,SAASJ,kBAAT,CAA4BD,CAA5B,EAA6C;IAC3C,OAAOrB,MAAM,CAACyB,SAAP,CAAiBE,QAAjB,CAA0BC,IAA1B,CAA+BP,CAA/B,CAAA,KAAsC,iBAA7C,CAAA;EACD,CAAA;;EAEM,SAASxD,UAAT,CAAoBxB,KAApB,EAAuD;EAC5D,EAAA,OAAO8E,KAAK,CAACC,OAAN,CAAc/E,KAAd,CAAP,CAAA;EACD,CAAA;EAEM,SAASwF,OAAT,CAAiBxF,KAAjB,EAA6C;IAClD,OAAOA,KAAK,YAAYyF,KAAxB,CAAA;EACD,CAAA;EAEM,SAASC,KAAT,CAAeC,OAAf,EAA+C;EACpD,EAAA,OAAO,IAAIC,OAAJ,CAAaC,OAAD,IAAa;EAC9BC,IAAAA,UAAU,CAACD,OAAD,EAAUF,OAAV,CAAV,CAAA;EACD,GAFM,CAAP,CAAA;EAGD,CAAA;EAED;EACA;EACA;EACA;;EACO,SAASI,iBAAT,CAA2BC,QAA3B,EAAiD;EACtDN,EAAAA,KAAK,CAAC,CAAD,CAAL,CAASO,IAAT,CAAcD,QAAd,CAAA,CAAA;EACD,CAAA;EAEM,SAASE,kBAAT,GAA2D;EAChE,EAAA,IAAI,OAAOC,eAAP,KAA2B,UAA/B,EAA2C;MACzC,OAAO,IAAIA,eAAJ,EAAP,CAAA;EACD,GAAA;;EACD,EAAA,OAAA;EACD,CAAA;EAEM,SAASC,WAAT,CAGLC,QAHK,EAGwBC,IAHxB,EAGqC5D,OAHrC,EAG+D;EACpE;EACA,EAAA,IAAIA,OAAO,CAAC6D,WAAZ,IAAA,IAAA,IAAI7D,OAAO,CAAC6D,WAAR,CAAsBF,QAAtB,EAAgCC,IAAhC,CAAJ,EAA2C;EACzC,IAAA,OAAOD,QAAP,CAAA;KADF,MAEO,IAAI,OAAO3D,OAAO,CAAC8D,iBAAf,KAAqC,UAAzC,EAAqD;EAC1D,IAAA,OAAO9D,OAAO,CAAC8D,iBAAR,CAA0BH,QAA1B,EAAoCC,IAApC,CAAP,CAAA;EACD,GAFM,MAEA,IAAI5D,OAAO,CAAC8D,iBAAR,KAA8B,KAAlC,EAAyC;EAC9C;EACA,IAAA,OAAOnC,gBAAgB,CAACgC,QAAD,EAAWC,IAAX,CAAvB,CAAA;EACD,GAAA;;EACD,EAAA,OAAOA,IAAP,CAAA;EACD;;EC9aM,MAAMG,YAAN,SAA2B/H,YAA3B,CAAwC;EAM7CC,EAAAA,WAAW,GAAG;EACZ,IAAA,KAAA,EAAA,CAAA;;MACA,IAAK+H,CAAAA,KAAL,GAAcC,OAAD,IAAa;EACxB;EACA;EACA,MAAA,IAAI,CAACnH,QAAD,IAAaC,MAAM,CAACmH,gBAAxB,EAA0C;EACxC,QAAA,MAAM5H,QAAQ,GAAG,MAAM2H,OAAO,EAA9B,CADwC;;;EAGxClH,QAAAA,MAAM,CAACmH,gBAAP,CAAwB,kBAAxB,EAA4C5H,QAA5C,EAAsD,KAAtD,CAAA,CAAA;EACAS,QAAAA,MAAM,CAACmH,gBAAP,CAAwB,OAAxB,EAAiC5H,QAAjC,EAA2C,KAA3C,CAAA,CAAA;EAEA,QAAA,OAAO,MAAM;EACX;EACAS,UAAAA,MAAM,CAACoH,mBAAP,CAA2B,kBAA3B,EAA+C7H,QAA/C,CAAA,CAAA;EACAS,UAAAA,MAAM,CAACoH,mBAAP,CAA2B,OAA3B,EAAoC7H,QAApC,CAAA,CAAA;WAHF,CAAA;EAKD,OAAA;;EACD,MAAA,OAAA;OAfF,CAAA;EAiBD,GAAA;;EAESG,EAAAA,WAAW,GAAS;MAC5B,IAAI,CAAC,IAAK2H,CAAAA,OAAV,EAAmB;QACjB,IAAKC,CAAAA,gBAAL,CAAsB,IAAA,CAAKL,KAA3B,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAESrH,EAAAA,aAAa,GAAG;EACxB,IAAA,IAAI,CAAC,IAAA,CAAKC,YAAL,EAAL,EAA0B;EAAA,MAAA,IAAA,aAAA,CAAA;;EACxB,MAAA,CAAA,aAAA,GAAA,IAAA,CAAKwH,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAKA,CAAAA,OAAL,GAAenH,SAAf,CAAA;EACD,KAAA;EACF,GAAA;;IAEDoH,gBAAgB,CAACL,KAAD,EAAuB;EAAA,IAAA,IAAA,cAAA,CAAA;;MACrC,IAAKA,CAAAA,KAAL,GAAaA,KAAb,CAAA;EACA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAKI,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;EACA,IAAA,IAAA,CAAKA,OAAL,GAAeJ,KAAK,CAAEM,OAAD,IAAa;EAChC,MAAA,IAAI,OAAOA,OAAP,KAAmB,SAAvB,EAAkC;UAChC,IAAKC,CAAAA,UAAL,CAAgBD,OAAhB,CAAA,CAAA;EACD,OAFD,MAEO;EACL,QAAA,IAAA,CAAKL,OAAL,EAAA,CAAA;EACD,OAAA;EACF,KANmB,CAApB,CAAA;EAOD,GAAA;;IAEDM,UAAU,CAACD,OAAD,EAA0B;EAClC,IAAA,MAAME,OAAO,GAAG,IAAKF,CAAAA,OAAL,KAAiBA,OAAjC,CAAA;;EACA,IAAA,IAAIE,OAAJ,EAAa;QACX,IAAKF,CAAAA,OAAL,GAAeA,OAAf,CAAA;EACA,MAAA,IAAA,CAAKL,OAAL,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDA,EAAAA,OAAO,GAAS;EACd,IAAA,IAAA,CAAK/H,SAAL,CAAeuI,OAAf,CAAuB,CAAC;EAAEnI,MAAAA,QAAAA;EAAF,KAAD,KAAkB;QACvCA,QAAQ,EAAA,CAAA;OADV,CAAA,CAAA;EAGD,GAAA;;EAEDoI,EAAAA,SAAS,GAAY;EACnB,IAAA,IAAI,OAAO,IAAA,CAAKJ,OAAZ,KAAwB,SAA5B,EAAuC;EACrC,MAAA,OAAO,KAAKA,OAAZ,CAAA;EACD,KAHkB;;;EAMnB,IAAA,IAAI,OAAOK,QAAP,KAAoB,WAAxB,EAAqC;EACnC,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;;EAED,IAAA,OAAO,CAAC1H,SAAD,EAAY,SAAZ,EAAuB,WAAvB,CAAoCY,CAAAA,QAApC,CACL8G,QAAQ,CAACC,eADJ,CAAP,CAAA;EAGD,GAAA;;EA/E4C,CAAA;AAkFlCC,QAAAA,YAAY,GAAG,IAAId,YAAJ;;EClF5B,MAAMe,YAAY,GAAG,CAAC,QAAD,EAAW,SAAX,CAArB,CAAA;EAEO,MAAMC,aAAN,SAA4B/I,YAA5B,CAAyC;EAM9CC,EAAAA,WAAW,GAAG;EACZ,IAAA,KAAA,EAAA,CAAA;;MACA,IAAK+H,CAAAA,KAAL,GAAcgB,QAAD,IAAc;EACzB;EACA;EACA,MAAA,IAAI,CAAClI,QAAD,IAAaC,MAAM,CAACmH,gBAAxB,EAA0C;EACxC,QAAA,MAAM5H,QAAQ,GAAG,MAAM0I,QAAQ,EAA/B,CADwC;;;EAGxCF,QAAAA,YAAY,CAACL,OAAb,CAAsBQ,KAAD,IAAW;EAC9BlI,UAAAA,MAAM,CAACmH,gBAAP,CAAwBe,KAAxB,EAA+B3I,QAA/B,EAAyC,KAAzC,CAAA,CAAA;WADF,CAAA,CAAA;EAIA,QAAA,OAAO,MAAM;EACX;EACAwI,UAAAA,YAAY,CAACL,OAAb,CAAsBQ,KAAD,IAAW;EAC9BlI,YAAAA,MAAM,CAACoH,mBAAP,CAA2Bc,KAA3B,EAAkC3I,QAAlC,CAAA,CAAA;aADF,CAAA,CAAA;WAFF,CAAA;EAMD,OAAA;;EAED,MAAA,OAAA;OAlBF,CAAA;EAoBD,GAAA;;EAESG,EAAAA,WAAW,GAAS;MAC5B,IAAI,CAAC,IAAK2H,CAAAA,OAAV,EAAmB;QACjB,IAAKC,CAAAA,gBAAL,CAAsB,IAAA,CAAKL,KAA3B,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAESrH,EAAAA,aAAa,GAAG;EACxB,IAAA,IAAI,CAAC,IAAA,CAAKC,YAAL,EAAL,EAA0B;EAAA,MAAA,IAAA,aAAA,CAAA;;EACxB,MAAA,CAAA,aAAA,GAAA,IAAA,CAAKwH,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;QACA,IAAKA,CAAAA,OAAL,GAAenH,SAAf,CAAA;EACD,KAAA;EACF,GAAA;;IAEDoH,gBAAgB,CAACL,KAAD,EAAuB;EAAA,IAAA,IAAA,cAAA,CAAA;;MACrC,IAAKA,CAAAA,KAAL,GAAaA,KAAb,CAAA;EACA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAKI,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;EACA,IAAA,IAAA,CAAKA,OAAL,GAAeJ,KAAK,CAAEkB,MAAD,IAAsB;EACzC,MAAA,IAAI,OAAOA,MAAP,KAAkB,SAAtB,EAAiC;UAC/B,IAAKC,CAAAA,SAAL,CAAeD,MAAf,CAAA,CAAA;EACD,OAFD,MAEO;EACL,QAAA,IAAA,CAAKF,QAAL,EAAA,CAAA;EACD,OAAA;EACF,KANmB,CAApB,CAAA;EAOD,GAAA;;IAEDG,SAAS,CAACD,MAAD,EAAyB;EAChC,IAAA,MAAMV,OAAO,GAAG,IAAKU,CAAAA,MAAL,KAAgBA,MAAhC,CAAA;;EAEA,IAAA,IAAIV,OAAJ,EAAa;QACX,IAAKU,CAAAA,MAAL,GAAcA,MAAd,CAAA;EACA,MAAA,IAAA,CAAKF,QAAL,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDA,EAAAA,QAAQ,GAAS;EACf,IAAA,IAAA,CAAK9I,SAAL,CAAeuI,OAAf,CAAuB,CAAC;EAAEnI,MAAAA,QAAAA;EAAF,KAAD,KAAkB;QACvCA,QAAQ,EAAA,CAAA;OADV,CAAA,CAAA;EAGD,GAAA;;EAED8I,EAAAA,QAAQ,GAAY;EAClB,IAAA,IAAI,OAAO,IAAA,CAAKF,MAAZ,KAAuB,SAA3B,EAAsC;EACpC,MAAA,OAAO,KAAKA,MAAZ,CAAA;EACD,KAAA;;MAED,IACE,OAAOG,SAAP,KAAqB,WAArB,IACA,OAAOA,SAAS,CAACC,MAAjB,KAA4B,WAF9B,EAGE;EACA,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;;MAED,OAAOD,SAAS,CAACC,MAAjB,CAAA;EACD,GAAA;;EAnF6C,CAAA;AAsFnCC,QAAAA,aAAa,GAAG,IAAIR,aAAJ;;ECrD7B,SAASS,iBAAT,CAA2BC,YAA3B,EAAiD;IAC/C,OAAOnH,IAAI,CAACoH,GAAL,CAAS,OAAO,CAAKD,IAAAA,YAArB,EAAmC,KAAnC,CAAP,CAAA;EACD,CAAA;;EAEM,SAASE,QAAT,CAAkBC,WAAlB,EAAiE;EACtE,EAAA,OAAO,CAACA,WAAD,IAACA,IAAAA,GAAAA,WAAD,GAAgB,QAAhB,MAA8B,QAA9B,GACHL,aAAa,CAACH,QAAd,EADG,GAEH,IAFJ,CAAA;EAGD,CAAA;EAEM,MAAMS,cAAN,CAAqB;IAG1B5J,WAAW,CAAC+D,OAAD,EAA0B;EACnC,IAAA,IAAA,CAAK8F,MAAL,GAAc9F,OAAd,IAAcA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAE8F,MAAvB,CAAA;EACA,IAAA,IAAA,CAAKC,MAAL,GAAc/F,OAAd,IAAcA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAE+F,MAAvB,CAAA;EACD,GAAA;;EANyB,CAAA;EASrB,SAASC,gBAAT,CAA0B1I,KAA1B,EAA+D;IACpE,OAAOA,KAAK,YAAYuI,cAAxB,CAAA;EACD,CAAA;EAEM,SAASI,aAAT,CACLC,MADK,EAEW;IAChB,IAAIC,gBAAgB,GAAG,KAAvB,CAAA;IACA,IAAIV,YAAY,GAAG,CAAnB,CAAA;IACA,IAAIW,UAAU,GAAG,KAAjB,CAAA;EACA,EAAA,IAAIC,UAAJ,CAAA;EACA,EAAA,IAAIC,cAAJ,CAAA;EACA,EAAA,IAAIC,aAAJ,CAAA;IAEA,MAAMC,OAAO,GAAG,IAAItD,OAAJ,CAAmB,CAACuD,YAAD,EAAeC,WAAf,KAA+B;EAChEJ,IAAAA,cAAc,GAAGG,YAAjB,CAAA;EACAF,IAAAA,aAAa,GAAGG,WAAhB,CAAA;EACD,GAHe,CAAhB,CAAA;;IAKA,MAAMC,MAAM,GAAIC,aAAD,IAAyC;MACtD,IAAI,CAACR,UAAL,EAAiB;EACfS,MAAAA,MAAM,CAAC,IAAIhB,cAAJ,CAAmBe,aAAnB,CAAD,CAAN,CAAA;EAEAV,MAAAA,MAAM,CAACY,KAAP,IAAAZ,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAM,CAACY,KAAP,EAAA,CAAA;EACD,KAAA;KALH,CAAA;;IAOA,MAAMC,WAAW,GAAG,MAAM;EACxBZ,IAAAA,gBAAgB,GAAG,IAAnB,CAAA;KADF,CAAA;;IAIA,MAAMa,aAAa,GAAG,MAAM;EAC1Bb,IAAAA,gBAAgB,GAAG,KAAnB,CAAA;KADF,CAAA;;EAIA,EAAA,MAAMc,WAAW,GAAG,MAClB,CAACpC,YAAY,CAACH,SAAb,EAAD,IACCwB,MAAM,CAACN,WAAP,KAAuB,QAAvB,IAAmC,CAACL,aAAa,CAACH,QAAd,EAFvC,CAAA;;IAIA,MAAMjC,OAAO,GAAI7F,KAAD,IAAgB;MAC9B,IAAI,CAAC8I,UAAL,EAAiB;EACfA,MAAAA,UAAU,GAAG,IAAb,CAAA;EACAF,MAAAA,MAAM,CAACgB,SAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAAhB,MAAM,CAACgB,SAAP,CAAmB5J,KAAnB,CAAA,CAAA;QACA+I,UAAU,IAAA,IAAV,YAAAA,UAAU,EAAA,CAAA;QACVC,cAAc,CAAChJ,KAAD,CAAd,CAAA;EACD,KAAA;KANH,CAAA;;IASA,MAAMuJ,MAAM,GAAIvJ,KAAD,IAAgB;MAC7B,IAAI,CAAC8I,UAAL,EAAiB;EACfA,MAAAA,UAAU,GAAG,IAAb,CAAA;EACAF,MAAAA,MAAM,CAACiB,OAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAAjB,MAAM,CAACiB,OAAP,CAAiB7J,KAAjB,CAAA,CAAA;QACA+I,UAAU,IAAA,IAAV,YAAAA,UAAU,EAAA,CAAA;QACVE,aAAa,CAACjJ,KAAD,CAAb,CAAA;EACD,KAAA;KANH,CAAA;;IASA,MAAM8J,KAAK,GAAG,MAAM;EAClB,IAAA,OAAO,IAAIlE,OAAJ,CAAamE,eAAD,IAAqB;QACtChB,UAAU,GAAI/I,KAAD,IAAW;EACtB,QAAA,MAAMgK,WAAW,GAAGlB,UAAU,IAAI,CAACa,WAAW,EAA9C,CAAA;;EACA,QAAA,IAAIK,WAAJ,EAAiB;YACfD,eAAe,CAAC/J,KAAD,CAAf,CAAA;EACD,SAAA;;EACD,QAAA,OAAOgK,WAAP,CAAA;SALF,CAAA;;EAOApB,MAAAA,MAAM,CAACqB,OAAP,IAAArB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAM,CAACqB,OAAP,EAAA,CAAA;OARK,CAAA,CASJhE,IATI,CASC,MAAM;EACZ8C,MAAAA,UAAU,GAAGpJ,SAAb,CAAA;;QACA,IAAI,CAACmJ,UAAL,EAAiB;EACfF,QAAAA,MAAM,CAACsB,UAAP,IAAAtB,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAM,CAACsB,UAAP,EAAA,CAAA;EACD,OAAA;EACF,KAdM,CAAP,CAAA;EAeD,GAhBD,CAlDgB;;;IAqEhB,MAAMC,GAAG,GAAG,MAAM;EAChB;EACA,IAAA,IAAIrB,UAAJ,EAAgB;EACd,MAAA,OAAA;EACD,KAAA;;MAED,IAAIsB,cAAJ,CANgB;;MAShB,IAAI;EACFA,MAAAA,cAAc,GAAGxB,MAAM,CAACyB,EAAP,EAAjB,CAAA;OADF,CAEE,OAAOC,KAAP,EAAc;EACdF,MAAAA,cAAc,GAAGxE,OAAO,CAAC2D,MAAR,CAAee,KAAf,CAAjB,CAAA;EACD,KAAA;;EAED1E,IAAAA,OAAO,CAACC,OAAR,CAAgBuE,cAAhB,CACGnE,CAAAA,IADH,CACQJ,OADR,CAEG0E,CAAAA,KAFH,CAEUD,KAAD,IAAW;EAAA,MAAA,IAAA,aAAA,EAAA,kBAAA,CAAA;;EAChB;EACA,MAAA,IAAIxB,UAAJ,EAAgB;EACd,QAAA,OAAA;EACD,OAJe;;;EAOhB,MAAA,MAAM0B,KAAK,GAAG5B,CAAAA,aAAAA,GAAAA,MAAM,CAAC4B,KAAV,4BAAmB,CAA9B,CAAA;EACA,MAAA,MAAMC,UAAU,GAAG7B,CAAAA,kBAAAA,GAAAA,MAAM,CAAC6B,UAAV,iCAAwBvC,iBAAxC,CAAA;EACA,MAAA,MAAMwC,KAAK,GACT,OAAOD,UAAP,KAAsB,UAAtB,GACIA,UAAU,CAACtC,YAAD,EAAemC,KAAf,CADd,GAEIG,UAHN,CAAA;QAIA,MAAME,WAAW,GACfH,KAAK,KAAK,IAAV,IACC,OAAOA,KAAP,KAAiB,QAAjB,IAA6BrC,YAAY,GAAGqC,KAD7C,IAEC,OAAOA,KAAP,KAAiB,UAAjB,IAA+BA,KAAK,CAACrC,YAAD,EAAemC,KAAf,CAHvC,CAAA;;EAKA,MAAA,IAAIzB,gBAAgB,IAAI,CAAC8B,WAAzB,EAAsC;EACpC;UACApB,MAAM,CAACe,KAAD,CAAN,CAAA;EACA,QAAA,OAAA;EACD,OAAA;;EAEDnC,MAAAA,YAAY,GAxBI;;QA2BhBS,MAAM,CAACgC,MAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAAhC,MAAM,CAACgC,MAAP,CAAgBzC,YAAhB,EAA8BmC,KAA9B,CAAA,CA3BgB;;QA8BhB5E,KAAK,CAACgF,KAAD,CAAL;SAEGzE,IAFH,CAEQ,MAAM;UACV,IAAI0D,WAAW,EAAf,EAAmB;EACjB,UAAA,OAAOG,KAAK,EAAZ,CAAA;EACD,SAAA;;EACD,QAAA,OAAA;SANJ,CAAA,CAQG7D,IARH,CAQQ,MAAM;EACV,QAAA,IAAI4C,gBAAJ,EAAsB;YACpBU,MAAM,CAACe,KAAD,CAAN,CAAA;EACD,SAFD,MAEO;YACLH,GAAG,EAAA,CAAA;EACJ,SAAA;SAbL,CAAA,CAAA;OAhCJ,CAAA,CAAA;EAgDD,GA/DD,CArEgB;;;EAuIhB,EAAA,IAAI9B,QAAQ,CAACO,MAAM,CAACN,WAAR,CAAZ,EAAkC;MAChC6B,GAAG,EAAA,CAAA;EACJ,GAFD,MAEO;MACLL,KAAK,EAAA,CAAG7D,IAAR,CAAakE,GAAb,CAAA,CAAA;EACD,GAAA;;IAED,OAAO;MACLjB,OADK;MAELG,MAFK;EAGLwB,IAAAA,QAAQ,EAAE,MAAM;EACd,MAAA,MAAMC,WAAW,GAAG/B,UAAH,IAAA,IAAA,GAAA,KAAA,CAAA,GAAGA,UAAU,EAA9B,CAAA;EACA,MAAA,OAAO+B,WAAW,GAAG5B,OAAH,GAAatD,OAAO,CAACC,OAAR,EAA/B,CAAA;OALG;MAOL4D,WAPK;EAQLC,IAAAA,aAAAA;KARF,CAAA;EAUD;;EClNM,MAAMqB,aAAqB,GAAGC,OAA9B;;ECIA,SAASC,mBAAT,GAA+B;IACpC,IAAIC,KAAuB,GAAG,EAA9B,CAAA;IACA,IAAIC,YAAY,GAAG,CAAnB,CAAA;;IACA,IAAIC,QAAwB,GAAIpF,QAAD,IAAc;MAC3CA,QAAQ,EAAA,CAAA;KADV,CAAA;;IAGA,IAAIqF,aAAkC,GAAIrF,QAAD,IAA0B;MACjEA,QAAQ,EAAA,CAAA;KADV,CAAA;;IAIA,MAAMsF,KAAK,GAAOtF,QAAJ,IAA6B;EACzC,IAAA,IAAIjC,MAAJ,CAAA;MACAoH,YAAY,EAAA,CAAA;;MACZ,IAAI;QACFpH,MAAM,GAAGiC,QAAQ,EAAjB,CAAA;EACD,KAFD,SAEU;QACRmF,YAAY,EAAA,CAAA;;QACZ,IAAI,CAACA,YAAL,EAAmB;UACjBI,KAAK,EAAA,CAAA;EACN,OAAA;EACF,KAAA;;EACD,IAAA,OAAOxH,MAAP,CAAA;KAXF,CAAA;;IAcA,MAAMyH,QAAQ,GAAIxF,QAAD,IAAoC;EACnD,IAAA,IAAImF,YAAJ,EAAkB;QAChBD,KAAK,CAACO,IAAN,CAAWzF,QAAX,CAAA,CAAA;EACD,KAFD,MAEO;EACLD,MAAAA,iBAAiB,CAAC,MAAM;UACtBqF,QAAQ,CAACpF,QAAD,CAAR,CAAA;EACD,OAFgB,CAAjB,CAAA;EAGD,KAAA;KAPH,CAAA;EAUA;EACF;EACA;;;IACE,MAAM0F,UAAU,GACd1F,QADiB,IAES;MAC1B,OAAO,CAAC,GAAG2F,IAAJ,KAAa;EAClBH,MAAAA,QAAQ,CAAC,MAAM;UACbxF,QAAQ,CAAC,GAAG2F,IAAJ,CAAR,CAAA;EACD,OAFO,CAAR,CAAA;OADF,CAAA;KAHF,CAAA;;IAUA,MAAMJ,KAAK,GAAG,MAAY;MACxB,MAAMK,aAAa,GAAGV,KAAtB,CAAA;EACAA,IAAAA,KAAK,GAAG,EAAR,CAAA;;MACA,IAAIU,aAAa,CAACpH,MAAlB,EAA0B;EACxBuB,MAAAA,iBAAiB,CAAC,MAAM;EACtBsF,QAAAA,aAAa,CAAC,MAAM;EAClBO,UAAAA,aAAa,CAACzE,OAAd,CAAuBnB,QAAD,IAAc;cAClCoF,QAAQ,CAACpF,QAAD,CAAR,CAAA;aADF,CAAA,CAAA;EAGD,SAJY,CAAb,CAAA;EAKD,OANgB,CAAjB,CAAA;EAOD,KAAA;KAXH,CAAA;EAcA;EACF;EACA;EACA;;;IACE,MAAM6F,iBAAiB,GAAIxB,EAAD,IAAwB;EAChDe,IAAAA,QAAQ,GAAGf,EAAX,CAAA;KADF,CAAA;EAIA;EACF;EACA;EACA;;;IACE,MAAMyB,sBAAsB,GAAIzB,EAAD,IAA6B;EAC1DgB,IAAAA,aAAa,GAAGhB,EAAhB,CAAA;KADF,CAAA;;IAIA,OAAO;MACLiB,KADK;MAELI,UAFK;MAGLF,QAHK;MAILK,iBAJK;EAKLC,IAAAA,sBAAAA;KALF,CAAA;EAOD;;AAGYC,QAAAA,aAAa,GAAGd,mBAAmB;;ECjGzC,MAAee,SAAf,CAAyB;EAI9BC,EAAAA,OAAO,GAAS;EACd,IAAA,IAAA,CAAKC,cAAL,EAAA,CAAA;EACD,GAAA;;EAESC,EAAAA,UAAU,GAAS;EAC3B,IAAA,IAAA,CAAKD,cAAL,EAAA,CAAA;;EAEA,IAAA,IAAInM,cAAc,CAAC,IAAKqM,CAAAA,SAAN,CAAlB,EAAoC;EAClC,MAAA,IAAA,CAAKC,SAAL,GAAiBvG,UAAU,CAAC,MAAM;EAChC,QAAA,IAAA,CAAKwG,cAAL,EAAA,CAAA;SADyB,EAExB,IAAKF,CAAAA,SAFmB,CAA3B,CAAA;EAGD,KAAA;EACF,GAAA;;IAESG,eAAe,CAACC,YAAD,EAAyC;EAChE;MACA,IAAKJ,CAAAA,SAAL,GAAiBpL,IAAI,CAACC,GAAL,CACf,IAAA,CAAKmL,SAAL,IAAkB,CADH,EAEfI,YAFe,IAEfA,IAAAA,GAAAA,YAFe,GAEEhN,QAAQ,GAAGS,QAAH,GAAc,CAAI,GAAA,EAAJ,GAAS,IAFjC,CAAjB,CAAA;EAID,GAAA;;EAESiM,EAAAA,cAAc,GAAG;MACzB,IAAI,IAAA,CAAKG,SAAT,EAAoB;QAClBI,YAAY,CAAC,IAAKJ,CAAAA,SAAN,CAAZ,CAAA;QACA,IAAKA,CAAAA,SAAL,GAAiB1M,SAAjB,CAAA;EACD,KAAA;EACF,GAAA;;EA/B6B;;ECwIhC;EAEO,MAAM+M,KAAN,SAKGV,SALH,CAKa;IAiBlBrN,WAAW,CAACiK,MAAD,EAA8D;EACvE,IAAA,KAAA,EAAA,CAAA;MAEA,IAAK+D,CAAAA,mBAAL,GAA2B,KAA3B,CAAA;EACA,IAAA,IAAA,CAAKC,cAAL,GAAsBhE,MAAM,CAACgE,cAA7B,CAAA;EACA,IAAA,IAAA,CAAKC,UAAL,CAAgBjE,MAAM,CAAClG,OAAvB,CAAA,CAAA;MACA,IAAKoK,CAAAA,SAAL,GAAiB,EAAjB,CAAA;EACA,IAAA,IAAA,CAAKC,KAAL,GAAanE,MAAM,CAACmE,KAApB,CAAA;EACA,IAAA,IAAA,CAAKC,MAAL,GAAcpE,MAAM,CAACoE,MAAP,IAAiBjC,aAA/B,CAAA;EACA,IAAA,IAAA,CAAKtJ,QAAL,GAAgBmH,MAAM,CAACnH,QAAvB,CAAA;EACA,IAAA,IAAA,CAAKe,SAAL,GAAiBoG,MAAM,CAACpG,SAAxB,CAAA;MACA,IAAKyK,CAAAA,YAAL,GAAoBrE,MAAM,CAAC9F,KAAP,IAAgBoK,iBAAe,CAAC,IAAKxK,CAAAA,OAAN,CAAnD,CAAA;MACA,IAAKI,CAAAA,KAAL,GAAa,IAAA,CAAKmK,YAAlB,CAAA;EACA,IAAA,IAAA,CAAKd,UAAL,EAAA,CAAA;EACD,GAAA;;EAEO,EAAA,IAAJgB,IAAI,GAA0B;MAChC,OAAO,IAAA,CAAKzK,OAAL,CAAayK,IAApB,CAAA;EACD,GAAA;;IAEON,UAAU,CAChBnK,OADgB,EAEV;EACN,IAAA,IAAA,CAAKA,OAAL,GAAe,EAAE,GAAG,KAAKkK,cAAV;QAA0B,GAAGlK,OAAAA;OAA5C,CAAA;EAEA,IAAA,IAAA,CAAK6J,eAAL,CAAqB,IAAK7J,CAAAA,OAAL,CAAa0J,SAAlC,CAAA,CAAA;EACD,GAAA;;EAESE,EAAAA,cAAc,GAAG;EACzB,IAAA,IAAI,CAAC,IAAA,CAAKQ,SAAL,CAAetI,MAAhB,IAA0B,IAAK1B,CAAAA,KAAL,CAAWT,WAAX,KAA2B,MAAzD,EAAiE;EAC/D,MAAA,IAAA,CAAK0K,KAAL,CAAWK,MAAX,CAAkB,IAAlB,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDC,EAAAA,OAAO,CACLC,OADK,EAEL5K,OAFK,EAGE;EACP,IAAA,MAAM4D,IAAI,GAAGF,WAAW,CAAC,KAAKtD,KAAL,CAAWwD,IAAZ,EAAkBgH,OAAlB,EAA2B,IAAA,CAAK5K,OAAhC,CAAxB,CADO;;EAIP,IAAA,IAAA,CAAK6K,QAAL,CAAc;QACZjH,IADY;EAEZnE,MAAAA,IAAI,EAAE,SAFM;EAGZqL,MAAAA,aAAa,EAAE9K,OAAF,IAAEA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAE5B,SAHZ;EAIZ2M,MAAAA,MAAM,EAAE/K,OAAF,IAAEA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAE+K,MAAAA;OAJnB,CAAA,CAAA;EAOA,IAAA,OAAOnH,IAAP,CAAA;EACD,GAAA;;EAEDoH,EAAAA,QAAQ,CACN5K,KADM,EAEN6K,eAFM,EAGA;EACN,IAAA,IAAA,CAAKJ,QAAL,CAAc;EAAEpL,MAAAA,IAAI,EAAE,UAAR;QAAoBW,KAApB;EAA2B6K,MAAAA,eAAAA;OAAzC,CAAA,CAAA;EACD,GAAA;;IAEDtE,MAAM,CAAC3G,OAAD,EAAyC;EAAA,IAAA,IAAA,aAAA,CAAA;;MAC7C,MAAMwG,OAAO,GAAG,IAAA,CAAKA,OAArB,CAAA;EACA,IAAA,CAAA,aAAA,GAAA,IAAA,CAAK0E,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAcvE,MAAd,CAAqB3G,OAArB,CAAA,CAAA;EACA,IAAA,OAAOwG,OAAO,GAAGA,OAAO,CAACjD,IAAR,CAAavG,IAAb,CAAA,CAAmB6K,KAAnB,CAAyB7K,IAAzB,CAAH,GAAoCkG,OAAO,CAACC,OAAR,EAAlD,CAAA;EACD,GAAA;;EAEDoG,EAAAA,OAAO,GAAS;EACd,IAAA,KAAA,CAAMA,OAAN,EAAA,CAAA;EAEA,IAAA,IAAA,CAAK5C,MAAL,CAAY;EAAEZ,MAAAA,MAAM,EAAE,IAAA;OAAtB,CAAA,CAAA;EACD,GAAA;;EAEDoF,EAAAA,KAAK,GAAS;EACZ,IAAA,IAAA,CAAK5B,OAAL,EAAA,CAAA;MACA,IAAKyB,CAAAA,QAAL,CAAc,IAAA,CAAKT,YAAnB,CAAA,CAAA;EACD,GAAA;;EAEDrK,EAAAA,QAAQ,GAAY;EAClB,IAAA,OAAO,IAAKkK,CAAAA,SAAL,CAAe1I,IAAf,CAAqB0J,QAAD,IAAcA,QAAQ,CAACpL,OAAT,CAAiBqL,OAAjB,KAA6B,KAA/D,CAAP,CAAA;EACD,GAAA;;EAEDC,EAAAA,UAAU,GAAY;MACpB,OAAO,IAAA,CAAKC,iBAAL,EAA2B,GAAA,CAA3B,IAAgC,CAAC,IAAA,CAAKrL,QAAL,EAAxC,CAAA;EACD,GAAA;;EAEDC,EAAAA,OAAO,GAAY;MACjB,OACE,IAAA,CAAKC,KAAL,CAAWoL,aAAX,IACA,CAAC,IAAA,CAAKpL,KAAL,CAAW0K,aADZ,IAEA,KAAKV,SAAL,CAAe1I,IAAf,CAAqB0J,QAAD,IAAcA,QAAQ,CAACK,gBAAT,EAA4BtL,CAAAA,OAA9D,CAHF,CAAA;EAKD,GAAA;;EAEDuL,EAAAA,aAAa,CAACrN,SAAS,GAAG,CAAb,EAAyB;MACpC,OACE,IAAA,CAAK+B,KAAL,CAAWoL,aAAX,IACA,CAAC,IAAA,CAAKpL,KAAL,CAAW0K,aADZ,IAEA,CAAC3M,cAAc,CAAC,IAAKiC,CAAAA,KAAL,CAAW0K,aAAZ,EAA2BzM,SAA3B,CAHjB,CAAA;EAKD,GAAA;;EAED4F,EAAAA,OAAO,GAAS;EAAA,IAAA,IAAA,cAAA,CAAA;;EACd,IAAA,MAAMmH,QAAQ,GAAG,IAAKhB,CAAAA,SAAL,CAAeuB,IAAf,CAAqB/N,CAAD,IAAOA,CAAC,CAACgO,wBAAF,EAA3B,CAAjB,CAAA;;EAEA,IAAA,IAAIR,QAAJ,EAAc;QACZA,QAAQ,CAACS,OAAT,CAAiB;EAAEC,QAAAA,aAAa,EAAE,KAAA;SAAlC,CAAA,CAAA;EACD,KALa;;;MAQd,CAAKZ,cAAAA,GAAAA,IAAAA,CAAAA,OAAL,oCAAc/C,QAAd,EAAA,CAAA;EACD,GAAA;;EAEDnD,EAAAA,QAAQ,GAAS;EAAA,IAAA,IAAA,cAAA,CAAA;;EACf,IAAA,MAAMoG,QAAQ,GAAG,IAAKhB,CAAAA,SAAL,CAAeuB,IAAf,CAAqB/N,CAAD,IAAOA,CAAC,CAACmO,sBAAF,EAA3B,CAAjB,CAAA;;EAEA,IAAA,IAAIX,QAAJ,EAAc;QACZA,QAAQ,CAACS,OAAT,CAAiB;EAAEC,QAAAA,aAAa,EAAE,KAAA;SAAlC,CAAA,CAAA;EACD,KALc;;;MAQf,CAAKZ,cAAAA,GAAAA,IAAAA,CAAAA,OAAL,oCAAc/C,QAAd,EAAA,CAAA;EACD,GAAA;;IAED6D,WAAW,CAACZ,QAAD,EAAyD;MAClE,IAAI,CAAC,KAAKhB,SAAL,CAAevM,QAAf,CAAwBuN,QAAxB,CAAL,EAAwC;EACtC,MAAA,IAAA,CAAKhB,SAAL,CAAerB,IAAf,CAAoBqC,QAApB,EADsC;;EAItC,MAAA,IAAA,CAAK5B,cAAL,EAAA,CAAA;QAEA,IAAKa,CAAAA,KAAL,CAAW4B,MAAX,CAAkB;EAAExM,QAAAA,IAAI,EAAE,eAAR;EAAyBD,QAAAA,KAAK,EAAE,IAAhC;EAAsC4L,QAAAA,QAAAA;SAAxD,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAEDc,cAAc,CAACd,QAAD,EAAyD;EACrE,IAAA,IAAI,KAAKhB,SAAL,CAAevM,QAAf,CAAwBuN,QAAxB,CAAJ,EAAuC;EACrC,MAAA,IAAA,CAAKhB,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAezM,MAAf,CAAuBC,CAAD,IAAOA,CAAC,KAAKwN,QAAnC,CAAjB,CAAA;;EAEA,MAAA,IAAI,CAAC,IAAA,CAAKhB,SAAL,CAAetI,MAApB,EAA4B;EAC1B;EACA;UACA,IAAI,IAAA,CAAKoJ,OAAT,EAAkB;YAChB,IAAI,IAAA,CAAKjB,mBAAT,EAA8B;cAC5B,IAAKiB,CAAAA,OAAL,CAAavE,MAAb,CAAoB;EAAEb,cAAAA,MAAM,EAAE,IAAA;eAA9B,CAAA,CAAA;EACD,WAFD,MAEO;cACL,IAAKoF,CAAAA,OAAL,CAAanE,WAAb,EAAA,CAAA;EACD,WAAA;EACF,SAAA;;EAED,QAAA,IAAA,CAAK0C,UAAL,EAAA,CAAA;EACD,OAAA;;QAED,IAAKY,CAAAA,KAAL,CAAW4B,MAAX,CAAkB;EAAExM,QAAAA,IAAI,EAAE,iBAAR;EAA2BD,QAAAA,KAAK,EAAE,IAAlC;EAAwC4L,QAAAA,QAAAA;SAA1D,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDG,EAAAA,iBAAiB,GAAW;MAC1B,OAAO,IAAA,CAAKnB,SAAL,CAAetI,MAAtB,CAAA;EACD,GAAA;;EAEDqK,EAAAA,UAAU,GAAS;EACjB,IAAA,IAAI,CAAC,IAAA,CAAK/L,KAAL,CAAWoL,aAAhB,EAA+B;EAC7B,MAAA,IAAA,CAAKX,QAAL,CAAc;EAAEpL,QAAAA,IAAI,EAAE,YAAA;SAAtB,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAED2M,EAAAA,KAAK,CACHpM,OADG,EAEHqM,YAFG,EAGa;EAAA,IAAA,IAAA,qBAAA,EAAA,qBAAA,CAAA;;EAChB,IAAA,IAAI,KAAKjM,KAAL,CAAWT,WAAX,KAA2B,MAA/B,EAAuC;QACrC,IAAI,IAAA,CAAKS,KAAL,CAAW0K,aAAX,IAA4BuB,YAA5B,IAA4BA,IAAAA,IAAAA,YAAY,CAAEP,aAA9C,EAA6D;EAC3D;EACA,QAAA,IAAA,CAAKnF,MAAL,CAAY;EAAEZ,UAAAA,MAAM,EAAE,IAAA;WAAtB,CAAA,CAAA;EACD,OAHD,MAGO,IAAI,IAAKS,CAAAA,OAAT,EAAkB;EAAA,QAAA,IAAA,cAAA,CAAA;;EACvB;EACA,QAAA,CAAA,cAAA,GAAA,IAAA,CAAK0E,OAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAclE,aAAd,EAAA,CAFuB;;EAIvB,QAAA,OAAO,KAAKR,OAAZ,CAAA;EACD,OAAA;EACF,KAXe;;;EAchB,IAAA,IAAIxG,OAAJ,EAAa;QACX,IAAKmK,CAAAA,UAAL,CAAgBnK,OAAhB,CAAA,CAAA;EACD,KAhBe;EAmBhB;;;EACA,IAAA,IAAI,CAAC,IAAA,CAAKA,OAAL,CAAahB,OAAlB,EAA2B;EACzB,MAAA,MAAMoM,QAAQ,GAAG,IAAKhB,CAAAA,SAAL,CAAeuB,IAAf,CAAqB/N,CAAD,IAAOA,CAAC,CAACoC,OAAF,CAAUhB,OAArC,CAAjB,CAAA;;EACA,MAAA,IAAIoM,QAAJ,EAAc;EACZ,QAAA,IAAA,CAAKjB,UAAL,CAAgBiB,QAAQ,CAACpL,OAAzB,CAAA,CAAA;EACD,OAAA;EACF,KAAA;;EAED,IAA2C;QACzC,IAAI,CAACoC,KAAK,CAACC,OAAN,CAAc,KAAKrC,OAAL,CAAajB,QAA3B,CAAL,EAA2C;UACzC,IAAKuL,CAAAA,MAAL,CAAY1C,KAAZ,CAAA,qIAAA,CAAA,CAAA;EAGD,OAAA;EACF,KAAA;;EAED,IAAA,MAAM0E,eAAe,GAAG9I,kBAAkB,EAA1C,CAnCgB;;EAsChB,IAAA,MAAM+I,cAA+C,GAAG;QACtDxN,QAAQ,EAAE,KAAKA,QADuC;EAEtDyN,MAAAA,SAAS,EAAEvP,SAF2C;EAGtDwN,MAAAA,IAAI,EAAE,IAAKA,CAAAA,IAAAA;EAH2C,KAAxD,CAtCgB;EA6ChB;EACA;;MACA,MAAMgC,iBAAiB,GAAIC,MAAD,IAAqB;EAC7CzL,MAAAA,MAAM,CAAC0L,cAAP,CAAsBD,MAAtB,EAA8B,QAA9B,EAAwC;EACtCE,QAAAA,UAAU,EAAE,IAD0B;EAEtCC,QAAAA,GAAG,EAAE,MAAM;EACT,UAAA,IAAIP,eAAJ,EAAqB;cACnB,IAAKrC,CAAAA,mBAAL,GAA2B,IAA3B,CAAA;cACA,OAAOqC,eAAe,CAACQ,MAAvB,CAAA;EACD,WAAA;;EACD,UAAA,OAAO7P,SAAP,CAAA;EACD,SAAA;SARH,CAAA,CAAA;OADF,CAAA;;EAaAwP,IAAAA,iBAAiB,CAACF,cAAD,CAAjB,CA5DgB;;MA+DhB,MAAMQ,OAAO,GAAG,MAAM;EACpB,MAAA,IAAI,CAAC,IAAA,CAAK/M,OAAL,CAAahB,OAAlB,EAA2B;UACzB,OAAOkE,OAAO,CAAC2D,MAAR,CAAA,gCAAA,GAC4B,KAAK7G,OAAL,CAAaF,SADzC,GAAP,GAAA,CAAA,CAAA;EAGD,OAAA;;QACD,IAAKmK,CAAAA,mBAAL,GAA2B,KAA3B,CAAA;EACA,MAAA,OAAO,KAAKjK,OAAL,CAAahB,OAAb,CAAqBuN,cAArB,CAAP,CAAA;EACD,KARD,CA/DgB;;;EA0EhB,IAAA,MAAMS,OAA6D,GAAG;QACpEX,YADoE;QAEpErM,OAAO,EAAE,KAAKA,OAFsD;QAGpEjB,QAAQ,EAAE,KAAKA,QAHqD;QAIpEqB,KAAK,EAAE,KAAKA,KAJwD;EAKpE2M,MAAAA,OAAAA;OALF,CAAA;MAQAN,iBAAiB,CAACO,OAAD,CAAjB,CAAA;MAEA,CAAKhN,qBAAAA,GAAAA,IAAAA,CAAAA,OAAL,CAAaiN,QAAb,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAuBC,OAAvB,CAA+BF,OAA/B,EApFgB;;EAuFhB,IAAA,IAAA,CAAKG,WAAL,GAAmB,IAAK/M,CAAAA,KAAxB,CAvFgB;;EA0FhB,IAAA,IACE,KAAKA,KAAL,CAAWT,WAAX,KAA2B,MAA3B,IACA,IAAKS,CAAAA,KAAL,CAAWgN,SAAX,MAAA,CAAA,qBAAA,GAAyBJ,OAAO,CAACX,YAAjC,qBAAyB,qBAAsB5B,CAAAA,IAA/C,CAFF,EAGE;EAAA,MAAA,IAAA,sBAAA,CAAA;;EACA,MAAA,IAAA,CAAKI,QAAL,CAAc;EAAEpL,QAAAA,IAAI,EAAE,OAAR;EAAiBgL,QAAAA,IAAI,EAAEuC,CAAAA,sBAAAA,GAAAA,OAAO,CAACX,YAAV,qBAAE,sBAAsB5B,CAAAA,IAAAA;SAA3D,CAAA,CAAA;EACD,KAAA;;MAED,MAAMtD,OAAO,GAAIS,KAAD,IAA0C;EACxD;QACA,IAAI,EAAE5B,gBAAgB,CAAC4B,KAAD,CAAhB,IAA2BA,KAAK,CAAC7B,MAAnC,CAAJ,EAAgD;EAC9C,QAAA,IAAA,CAAK8E,QAAL,CAAc;EACZpL,UAAAA,IAAI,EAAE,OADM;EAEZmI,UAAAA,KAAK,EAAEA,KAAAA;WAFT,CAAA,CAAA;EAID,OAAA;;EAED,MAAA,IAAI,CAAC5B,gBAAgB,CAAC4B,KAAD,CAArB,EAA8B;EAAA,QAAA,IAAA,qBAAA,EAAA,kBAAA,EAAA,sBAAA,EAAA,mBAAA,CAAA;;EAC5B;UACA,CAAKyC,qBAAAA,GAAAA,CAAAA,kBAAAA,GAAAA,IAAAA,CAAAA,KAAL,CAAWnE,MAAX,EAAkBiB,OAAlB,KAA4BS,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAAA,CAAAA,IAAAA,CAAAA,kBAAAA,EAAAA,KAA5B,EAAmC,IAAnC,CAAA,CAAA;EACA,QAAA,CAAA,sBAAA,GAAA,CAAA,mBAAA,GAAA,IAAA,CAAKyC,KAAL,CAAWnE,MAAX,EAAkBmH,SAAlB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,mBAAA,EACE,IAAKjN,CAAAA,KAAL,CAAWwD,IADb,EAEEgE,KAFF,EAGE,IAHF,CAAA,CAAA;;EAMA,QAA2C;EACzC,UAAA,IAAA,CAAK0C,MAAL,CAAY1C,KAAZ,CAAkBA,KAAlB,CAAA,CAAA;EACD,SAAA;EACF,OAAA;;QAED,IAAI,CAAC,IAAK0F,CAAAA,oBAAV,EAAgC;EAC9B;EACA,QAAA,IAAA,CAAK7D,UAAL,EAAA,CAAA;EACD,OAAA;;QACD,IAAK6D,CAAAA,oBAAL,GAA4B,KAA5B,CAAA;EACD,KA5BD,CAjGgB;;;MAgIhB,IAAKpC,CAAAA,OAAL,GAAejF,aAAa,CAAC;QAC3B0B,EAAE,EAAEqF,OAAO,CAACD,OADe;QAE3BjG,KAAK,EAAEwF,eAAF,IAAA,IAAA,GAAA,KAAA,CAAA,GAAEA,eAAe,CAAExF,KAAjB,CAAuBzK,IAAvB,CAA4BiQ,eAA5B,CAFoB;QAG3BpF,SAAS,EAAGtD,IAAD,IAAU;EAAA,QAAA,IAAA,sBAAA,EAAA,mBAAA,EAAA,sBAAA,EAAA,mBAAA,CAAA;;EACnB,QAAA,IAAI,OAAOA,IAAP,KAAgB,WAApB,EAAiC;EAC/B,UAA2C;EACzC,YAAA,IAAA,CAAK0G,MAAL,CAAY1C,KAAZ,CAAA,wIAAA,GAC2I,KAAK9H,SADhJ,CAAA,CAAA;EAGD,WAAA;;EACDqH,UAAAA,OAAO,CAAC,IAAIpE,KAAJ,CAAa,IAAKjD,CAAAA,SAAlB,wBAAD,CAAP,CAAA;EACA,UAAA,OAAA;EACD,SAAA;;EAED,QAAA,IAAA,CAAK6K,OAAL,CAAa/G,IAAb,CAAA,CAXmB;;UAcnB,CAAKyG,sBAAAA,GAAAA,CAAAA,mBAAAA,GAAAA,IAAAA,CAAAA,KAAL,CAAWnE,MAAX,EAAkBgB,SAAlB,KAA8BtD,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAAA,CAAAA,IAAAA,CAAAA,mBAAAA,EAAAA,IAA9B,EAAoC,IAApC,CAAA,CAAA;EACA,QAAA,CAAA,sBAAA,GAAA,CAAA,mBAAA,GAAA,IAAA,CAAKyG,KAAL,CAAWnE,MAAX,EAAkBmH,SAAlB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,mBAAA,EACEzJ,IADF,EAEE,IAAKxD,CAAAA,KAAL,CAAWwH,KAFb,EAGE,IAHF,CAAA,CAAA;;UAMA,IAAI,CAAC,IAAK0F,CAAAA,oBAAV,EAAgC;EAC9B;EACA,UAAA,IAAA,CAAK7D,UAAL,EAAA,CAAA;EACD,SAAA;;UACD,IAAK6D,CAAAA,oBAAL,GAA4B,KAA5B,CAAA;SA5ByB;QA8B3BnG,OA9B2B;EA+B3Be,MAAAA,MAAM,EAAE,CAACzC,YAAD,EAAemC,KAAf,KAAyB;EAC/B,QAAA,IAAA,CAAKiD,QAAL,CAAc;EAAEpL,UAAAA,IAAI,EAAE,QAAR;YAAkBgG,YAAlB;EAAgCmC,UAAAA,KAAAA;WAA9C,CAAA,CAAA;SAhCyB;EAkC3BL,MAAAA,OAAO,EAAE,MAAM;EACb,QAAA,IAAA,CAAKsD,QAAL,CAAc;EAAEpL,UAAAA,IAAI,EAAE,OAAA;WAAtB,CAAA,CAAA;SAnCyB;EAqC3B+H,MAAAA,UAAU,EAAE,MAAM;EAChB,QAAA,IAAA,CAAKqD,QAAL,CAAc;EAAEpL,UAAAA,IAAI,EAAE,UAAA;WAAtB,CAAA,CAAA;SAtCyB;EAwC3BqI,MAAAA,KAAK,EAAEkF,OAAO,CAAChN,OAAR,CAAgB8H,KAxCI;EAyC3BC,MAAAA,UAAU,EAAEiF,OAAO,CAAChN,OAAR,CAAgB+H,UAzCD;EA0C3BnC,MAAAA,WAAW,EAAEoH,OAAO,CAAChN,OAAR,CAAgB4F,WAAAA;EA1CF,KAAD,CAA5B,CAAA;EA6CA,IAAA,IAAA,CAAKY,OAAL,GAAe,IAAK0E,CAAAA,OAAL,CAAa1E,OAA5B,CAAA;EAEA,IAAA,OAAO,KAAKA,OAAZ,CAAA;EACD,GAAA;;IAEOqE,QAAQ,CAAC0C,MAAD,EAAsC;MACpD,MAAMC,OAAO,GACXpN,KADc,IAEgB;EAAA,MAAA,IAAA,YAAA,EAAA,qBAAA,CAAA;;QAC9B,QAAQmN,MAAM,CAAC9N,IAAf;EACE,QAAA,KAAK,QAAL;YACE,OAAO,EACL,GAAGW,KADE;cAELqN,iBAAiB,EAAEF,MAAM,CAAC9H,YAFrB;cAGLiI,kBAAkB,EAAEH,MAAM,CAAC3F,KAAAA;aAH7B,CAAA;;EAKF,QAAA,KAAK,OAAL;YACE,OAAO,EACL,GAAGxH,KADE;EAELT,YAAAA,WAAW,EAAE,QAAA;aAFf,CAAA;;EAIF,QAAA,KAAK,UAAL;YACE,OAAO,EACL,GAAGS,KADE;EAELT,YAAAA,WAAW,EAAE,UAAA;aAFf,CAAA;;EAIF,QAAA,KAAK,OAAL;YACE,OAAO,EACL,GAAGS,KADE;EAELqN,YAAAA,iBAAiB,EAAE,CAFd;EAGLC,YAAAA,kBAAkB,EAAE,IAHf;EAILN,YAAAA,SAAS,EAAEG,CAAAA,YAAAA,GAAAA,MAAM,CAAC9C,IAAT,2BAAiB,IAJrB;cAKL9K,WAAW,EAAEgG,QAAQ,CAAC,IAAK3F,CAAAA,OAAL,CAAa4F,WAAd,CAAR,GACT,UADS,GAET,QAPC;EAQL,YAAA,IAAI,CAACxF,KAAK,CAAC0K,aAAP,IAAwB;EAC1BlD,cAAAA,KAAK,EAAE,IADmB;EAE1BnH,cAAAA,MAAM,EAAE,SAAA;eAFV,CAAA;aARF,CAAA;;EAaF,QAAA,KAAK,SAAL;YACE,OAAO,EACL,GAAGL,KADE;cAELwD,IAAI,EAAE2J,MAAM,CAAC3J,IAFR;EAGL+J,YAAAA,eAAe,EAAEvN,KAAK,CAACuN,eAAN,GAAwB,CAHpC;cAIL7C,aAAa,EAAA,CAAA,qBAAA,GAAEyC,MAAM,CAACzC,aAAT,oCAA0BtM,IAAI,CAACC,GAAL,EAJlC;EAKLmJ,YAAAA,KAAK,EAAE,IALF;EAML4D,YAAAA,aAAa,EAAE,KANV;EAOL/K,YAAAA,MAAM,EAAE,SAPH;EAQL,YAAA,IAAI,CAAC8M,MAAM,CAACxC,MAAR,IAAkB;EACpBpL,cAAAA,WAAW,EAAE,MADO;EAEpB8N,cAAAA,iBAAiB,EAAE,CAFC;EAGpBC,cAAAA,kBAAkB,EAAE,IAAA;eAHtB,CAAA;aARF,CAAA;;EAcF,QAAA,KAAK,OAAL;EACE,UAAA,MAAM9F,KAAK,GAAG2F,MAAM,CAAC3F,KAArB,CAAA;;YAEA,IAAI5B,gBAAgB,CAAC4B,KAAD,CAAhB,IAA2BA,KAAK,CAAC9B,MAAjC,IAA2C,IAAKqH,CAAAA,WAApD,EAAiE;cAC/D,OAAO,EAAE,GAAG,IAAA,CAAKA,WAAV;EAAuBxN,cAAAA,WAAW,EAAE,MAAA;eAA3C,CAAA;EACD,WAAA;;YAED,OAAO,EACL,GAAGS,KADE;EAELwH,YAAAA,KAAK,EAAEA,KAFF;EAGLgG,YAAAA,gBAAgB,EAAExN,KAAK,CAACwN,gBAAN,GAAyB,CAHtC;EAILC,YAAAA,cAAc,EAAErP,IAAI,CAACC,GAAL,EAJX;EAKLgP,YAAAA,iBAAiB,EAAErN,KAAK,CAACqN,iBAAN,GAA0B,CALxC;EAMLC,YAAAA,kBAAkB,EAAE9F,KANf;EAOLjI,YAAAA,WAAW,EAAE,MAPR;EAQLc,YAAAA,MAAM,EAAE,OAAA;aARV,CAAA;;EAUF,QAAA,KAAK,YAAL;YACE,OAAO,EACL,GAAGL,KADE;EAELoL,YAAAA,aAAa,EAAE,IAAA;aAFjB,CAAA;;EAIF,QAAA,KAAK,UAAL;YACE,OAAO,EACL,GAAGpL,KADE;EAEL,YAAA,GAAGmN,MAAM,CAACnN,KAAAA;aAFZ,CAAA;EArEJ,OAAA;OAHF,CAAA;;EA+EA,IAAA,IAAA,CAAKA,KAAL,GAAaoN,OAAO,CAAC,IAAA,CAAKpN,KAAN,CAApB,CAAA;MAEAiJ,aAAa,CAACT,KAAd,CAAoB,MAAM;EACxB,MAAA,IAAA,CAAKwB,SAAL,CAAe3F,OAAf,CAAwB2G,QAAD,IAAc;UACnCA,QAAQ,CAAC0C,aAAT,CAAuBP,MAAvB,CAAA,CAAA;SADF,CAAA,CAAA;QAIA,IAAKlD,CAAAA,KAAL,CAAW4B,MAAX,CAAkB;EAAEzM,QAAAA,KAAK,EAAE,IAAT;EAAeC,QAAAA,IAAI,EAAE,SAArB;EAAgC8N,QAAAA,MAAAA;SAAlD,CAAA,CAAA;OALF,CAAA,CAAA;EAOD,GAAA;;EAnciB,CAAA;;EAscpB,SAAS/C,iBAAT,CAMExK,OANF,EAO6B;EAC3B,EAAA,MAAM4D,IAAI,GACR,OAAO5D,OAAO,CAAC+N,WAAf,KAA+B,UAA/B,GACK/N,OAAO,CAAC+N,WAAT,EADJ,GAEI/N,OAAO,CAAC+N,WAHd,CAAA;EAKA,EAAA,MAAMC,OAAO,GAAG,OAAOpK,IAAP,KAAgB,WAAhC,CAAA;IAEA,MAAMqK,oBAAoB,GAAGD,OAAO,GAChC,OAAOhO,OAAO,CAACiO,oBAAf,KAAwC,UAAxC,GACGjO,OAAO,CAACiO,oBAAT,EADF,GAEEjO,OAAO,CAACiO,oBAHsB,GAIhC,CAJJ,CAAA;IAMA,OAAO;MACLrK,IADK;EAEL+J,IAAAA,eAAe,EAAE,CAFZ;EAGL7C,IAAAA,aAAa,EAAEkD,OAAO,GAAGC,oBAAH,IAAGA,IAAAA,GAAAA,oBAAH,GAA2BzP,IAAI,CAACC,GAAL,EAA3B,GAAwC,CAHzD;EAILmJ,IAAAA,KAAK,EAAE,IAJF;EAKLgG,IAAAA,gBAAgB,EAAE,CALb;EAMLC,IAAAA,cAAc,EAAE,CANX;EAOLJ,IAAAA,iBAAiB,EAAE,CAPd;EAQLC,IAAAA,kBAAkB,EAAE,IARf;EASLN,IAAAA,SAAS,EAAE,IATN;EAUL5B,IAAAA,aAAa,EAAE,KAVV;EAWL/K,IAAAA,MAAM,EAAEuN,OAAO,GAAG,SAAH,GAAe,SAXzB;EAYLrO,IAAAA,WAAW,EAAE,MAAA;KAZf,CAAA;EAcD;;EC9iBD;EAEO,MAAMuO,UAAN,SAAyBlS,YAAzB,CAA0D;IAM/DC,WAAW,CAACiK,MAAD,EAA4B;EACrC,IAAA,KAAA,EAAA,CAAA;EACA,IAAA,IAAA,CAAKA,MAAL,GAAcA,MAAM,IAAI,EAAxB,CAAA;MACA,IAAKiI,CAAAA,OAAL,GAAe,EAAf,CAAA;MACA,IAAKC,CAAAA,UAAL,GAAkB,EAAlB,CAAA;EACD,GAAA;;EAEDC,EAAAA,KAAK,CACHC,MADG,EAEHtO,OAFG,EAGHI,KAHG,EAI4C;EAAA,IAAA,IAAA,kBAAA,CAAA;;EAC/C,IAAA,MAAMrB,QAAQ,GAAGiB,OAAO,CAACjB,QAAzB,CAAA;MACA,MAAMe,SAAS,GACbE,CAAAA,kBAAAA,GAAAA,OAAO,CAACF,SADK,KACQC,IAAAA,GAAAA,kBAAAA,GAAAA,qBAAqB,CAAChB,QAAD,EAAWiB,OAAX,CAD5C,CAAA;EAEA,IAAA,IAAIR,KAAK,GAAG,IAAA,CAAKqN,GAAL,CAAiD/M,SAAjD,CAAZ,CAAA;;MAEA,IAAI,CAACN,KAAL,EAAY;QACVA,KAAK,GAAG,IAAIwK,KAAJ,CAAU;EAChBK,QAAAA,KAAK,EAAE,IADS;EAEhBC,QAAAA,MAAM,EAAEgE,MAAM,CAACC,SAAP,EAFQ;UAGhBxP,QAHgB;UAIhBe,SAJgB;EAKhBE,QAAAA,OAAO,EAAEsO,MAAM,CAACE,mBAAP,CAA2BxO,OAA3B,CALO;UAMhBI,KANgB;EAOhB8J,QAAAA,cAAc,EAAEoE,MAAM,CAACG,gBAAP,CAAwB1P,QAAxB,CAAA;EAPA,OAAV,CAAR,CAAA;QASA,IAAKvC,CAAAA,GAAL,CAASgD,KAAT,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,OAAOA,KAAP,CAAA;EACD,GAAA;;IAEDhD,GAAG,CAACgD,KAAD,EAAyC;MAC1C,IAAI,CAAC,KAAK4O,UAAL,CAAgB5O,KAAK,CAACM,SAAtB,CAAL,EAAuC;EACrC,MAAA,IAAA,CAAKsO,UAAL,CAAgB5O,KAAK,CAACM,SAAtB,IAAmCN,KAAnC,CAAA;EACA,MAAA,IAAA,CAAK2O,OAAL,CAAapF,IAAb,CAAkBvJ,KAAlB,CAAA,CAAA;EACA,MAAA,IAAA,CAAKyM,MAAL,CAAY;EACVxM,QAAAA,IAAI,EAAE,OADI;EAEVD,QAAAA,KAAAA;SAFF,CAAA,CAAA;EAID,KAAA;EACF,GAAA;;IAEDkL,MAAM,CAAClL,KAAD,EAAyC;MAC7C,MAAMkP,UAAU,GAAG,IAAKN,CAAAA,UAAL,CAAgB5O,KAAK,CAACM,SAAtB,CAAnB,CAAA;;EAEA,IAAA,IAAI4O,UAAJ,EAAgB;EACdlP,MAAAA,KAAK,CAAC+J,OAAN,EAAA,CAAA;EAEA,MAAA,IAAA,CAAK4E,OAAL,GAAe,IAAKA,CAAAA,OAAL,CAAaxQ,MAAb,CAAqBC,CAAD,IAAOA,CAAC,KAAK4B,KAAjC,CAAf,CAAA;;QAEA,IAAIkP,UAAU,KAAKlP,KAAnB,EAA0B;EACxB,QAAA,OAAO,KAAK4O,UAAL,CAAgB5O,KAAK,CAACM,SAAtB,CAAP,CAAA;EACD,OAAA;;EAED,MAAA,IAAA,CAAKmM,MAAL,CAAY;EAAExM,QAAAA,IAAI,EAAE,SAAR;EAAmBD,QAAAA,KAAAA;SAA/B,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDmP,EAAAA,KAAK,GAAS;MACZtF,aAAa,CAACT,KAAd,CAAoB,MAAM;EACxB,MAAA,IAAA,CAAKuF,OAAL,CAAa1J,OAAb,CAAsBjF,KAAD,IAAW;UAC9B,IAAKkL,CAAAA,MAAL,CAAYlL,KAAZ,CAAA,CAAA;SADF,CAAA,CAAA;OADF,CAAA,CAAA;EAKD,GAAA;;IAEDqN,GAAG,CAMD/M,SANC,EAO0D;EAC3D,IAAA,OAAO,IAAKsO,CAAAA,UAAL,CAAgBtO,SAAhB,CAAP,CAAA;EACD,GAAA;;EAED8O,EAAAA,MAAM,GAAY;EAChB,IAAA,OAAO,KAAKT,OAAZ,CAAA;EACD,GAAA;;EAYD;EACF;EACA;EACExC,EAAAA,IAAI,CACFhN,IADE,EAEFC,IAFE,EAG8C;MAChD,MAAM,CAACW,OAAD,CAAYH,GAAAA,eAAe,CAACT,IAAD,EAAOC,IAAP,CAAjC,CAAA;;EAEA,IAAA,IAAI,OAAOW,OAAO,CAACG,KAAf,KAAyB,WAA7B,EAA0C;QACxCH,OAAO,CAACG,KAAR,GAAgB,IAAhB,CAAA;EACD,KAAA;;EAED,IAAA,OAAO,IAAKyO,CAAAA,OAAL,CAAaxC,IAAb,CAAmBnM,KAAD,IAAWF,UAAU,CAACC,OAAD,EAAUC,KAAV,CAAvC,CAAP,CAAA;EACD,GAAA;;EAiBD;EACF;EACA;EACEqP,EAAAA,OAAO,CACLlQ,IADK,EAELC,IAFK,EAGI;MACT,MAAM,CAACW,OAAD,CAAYH,GAAAA,eAAe,CAACT,IAAD,EAAOC,IAAP,CAAjC,CAAA;MACA,OAAOqC,MAAM,CAACC,IAAP,CAAY3B,OAAZ,CAAqBuC,CAAAA,MAArB,GAA8B,CAA9B,GACH,IAAA,CAAKqM,OAAL,CAAaxQ,MAAb,CAAqB6B,KAAD,IAAWF,UAAU,CAACC,OAAD,EAAUC,KAAV,CAAzC,CADG,GAEH,IAAA,CAAK2O,OAFT,CAAA;EAGD,GAAA;;IAEDlC,MAAM,CAAChH,KAAD,EAA+B;MACnCoE,aAAa,CAACT,KAAd,CAAoB,MAAM;EACxB,MAAA,IAAA,CAAK1M,SAAL,CAAeuI,OAAf,CAAuB,CAAC;EAAEnI,QAAAA,QAAAA;EAAF,OAAD,KAAkB;UACvCA,QAAQ,CAAC2I,KAAD,CAAR,CAAA;SADF,CAAA,CAAA;OADF,CAAA,CAAA;EAKD,GAAA;;EAEDhB,EAAAA,OAAO,GAAS;MACdoF,aAAa,CAACT,KAAd,CAAoB,MAAM;EACxB,MAAA,IAAA,CAAKuF,OAAL,CAAa1J,OAAb,CAAsBjF,KAAD,IAAW;EAC9BA,QAAAA,KAAK,CAACyE,OAAN,EAAA,CAAA;SADF,CAAA,CAAA;OADF,CAAA,CAAA;EAKD,GAAA;;EAEDe,EAAAA,QAAQ,GAAS;MACfqE,aAAa,CAACT,KAAd,CAAoB,MAAM;EACxB,MAAA,IAAA,CAAKuF,OAAL,CAAa1J,OAAb,CAAsBjF,KAAD,IAAW;EAC9BA,QAAAA,KAAK,CAACwF,QAAN,EAAA,CAAA;SADF,CAAA,CAAA;OADF,CAAA,CAAA;EAKD,GAAA;;EArK8D;;ECIjE;EAEO,MAAM8J,QAAN,SAKGxF,SALH,CAKa;IAWlBrN,WAAW,CAACiK,MAAD,EAA8D;EACvE,IAAA,KAAA,EAAA,CAAA;EAEA,IAAA,IAAA,CAAKgE,cAAL,GAAsBhE,MAAM,CAACgE,cAA7B,CAAA;EACA,IAAA,IAAA,CAAK6E,UAAL,GAAkB7I,MAAM,CAAC6I,UAAzB,CAAA;EACA,IAAA,IAAA,CAAKC,aAAL,GAAqB9I,MAAM,CAAC8I,aAA5B,CAAA;EACA,IAAA,IAAA,CAAK1E,MAAL,GAAcpE,MAAM,CAACoE,MAAP,IAAiBjC,aAA/B,CAAA;MACA,IAAK+B,CAAAA,SAAL,GAAiB,EAAjB,CAAA;EACA,IAAA,IAAA,CAAKhK,KAAL,GAAa8F,MAAM,CAAC9F,KAAP,IAAgBoK,eAAe,EAA5C,CAAA;EAEA,IAAA,IAAA,CAAKL,UAAL,CAAgBjE,MAAM,CAAClG,OAAvB,CAAA,CAAA;EACA,IAAA,IAAA,CAAKyJ,UAAL,EAAA,CAAA;EACD,GAAA;;IAEDU,UAAU,CACRnK,OADQ,EAEF;EACN,IAAA,IAAA,CAAKA,OAAL,GAAe,EAAE,GAAG,KAAKkK,cAAV;QAA0B,GAAGlK,OAAAA;OAA5C,CAAA;EAEA,IAAA,IAAA,CAAK6J,eAAL,CAAqB,IAAK7J,CAAAA,OAAL,CAAa0J,SAAlC,CAAA,CAAA;EACD,GAAA;;EAEO,EAAA,IAAJe,IAAI,GAA6B;MACnC,OAAO,IAAA,CAAKzK,OAAL,CAAayK,IAApB,CAAA;EACD,GAAA;;IAEDO,QAAQ,CAAC5K,KAAD,EAAkE;EACxE,IAAA,IAAA,CAAKyK,QAAL,CAAc;EAAEpL,MAAAA,IAAI,EAAE,UAAR;EAAoBW,MAAAA,KAAAA;OAAlC,CAAA,CAAA;EACD,GAAA;;IAED4L,WAAW,CAACZ,QAAD,EAAuD;MAChE,IAAI,CAAC,KAAKhB,SAAL,CAAevM,QAAf,CAAwBuN,QAAxB,CAAL,EAAwC;EACtC,MAAA,IAAA,CAAKhB,SAAL,CAAerB,IAAf,CAAoBqC,QAApB,EADsC;;EAItC,MAAA,IAAA,CAAK5B,cAAL,EAAA,CAAA;QAEA,IAAKwF,CAAAA,aAAL,CAAmB/C,MAAnB,CAA0B;EACxBxM,QAAAA,IAAI,EAAE,eADkB;EAExBa,QAAAA,QAAQ,EAAE,IAFc;EAGxB8K,QAAAA,QAAAA;SAHF,CAAA,CAAA;EAKD,KAAA;EACF,GAAA;;IAEDc,cAAc,CAACd,QAAD,EAAuD;EACnE,IAAA,IAAA,CAAKhB,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAezM,MAAf,CAAuBC,CAAD,IAAOA,CAAC,KAAKwN,QAAnC,CAAjB,CAAA;EAEA,IAAA,IAAA,CAAK3B,UAAL,EAAA,CAAA;MAEA,IAAKuF,CAAAA,aAAL,CAAmB/C,MAAnB,CAA0B;EACxBxM,MAAAA,IAAI,EAAE,iBADkB;EAExBa,MAAAA,QAAQ,EAAE,IAFc;EAGxB8K,MAAAA,QAAAA;OAHF,CAAA,CAAA;EAKD,GAAA;;EAESxB,EAAAA,cAAc,GAAG;EACzB,IAAA,IAAI,CAAC,IAAA,CAAKQ,SAAL,CAAetI,MAApB,EAA4B;EAC1B,MAAA,IAAI,KAAK1B,KAAL,CAAWK,MAAX,KAAsB,SAA1B,EAAqC;EACnC,QAAA,IAAA,CAAKgJ,UAAL,EAAA,CAAA;EACD,OAFD,MAEO;EACL,QAAA,IAAA,CAAKuF,aAAL,CAAmBtE,MAAnB,CAA0B,IAA1B,CAAA,CAAA;EACD,OAAA;EACF,KAAA;EACF,GAAA;;EAEDvC,EAAAA,QAAQ,GAAqB;EAAA,IAAA,IAAA,qBAAA,EAAA,aAAA,CAAA;;MAC3B,OAAO,CAAA,qBAAA,GAAA,CAAA,aAAA,GAAA,IAAA,CAAK+C,OAAZ,KAAO,IAAA,GAAA,KAAA,CAAA,GAAA,aAAA,CAAc/C,QAAd,EAAP,KAAA,IAAA,GAAA,qBAAA,GAAmC,IAAK8G,CAAAA,OAAL,EAAnC,CAAA;EACD,GAAA;;EAEY,EAAA,MAAPA,OAAO,GAAmB;MAC9B,MAAMC,eAAe,GAAG,MAAM;EAAA,MAAA,IAAA,mBAAA,CAAA;;QAC5B,IAAKhE,CAAAA,OAAL,GAAejF,aAAa,CAAC;EAC3B0B,QAAAA,EAAE,EAAE,MAAM;EACR,UAAA,IAAI,CAAC,IAAA,CAAK3H,OAAL,CAAab,UAAlB,EAA8B;EAC5B,YAAA,OAAO+D,OAAO,CAAC2D,MAAR,CAAe,qBAAf,CAAP,CAAA;EACD,WAAA;;YACD,OAAO,IAAA,CAAK7G,OAAL,CAAab,UAAb,CAAwB,IAAKiB,CAAAA,KAAL,CAAW+O,SAAnC,CAAP,CAAA;WALyB;EAO3BjH,QAAAA,MAAM,EAAE,CAACzC,YAAD,EAAemC,KAAf,KAAyB;EAC/B,UAAA,IAAA,CAAKiD,QAAL,CAAc;EAAEpL,YAAAA,IAAI,EAAE,QAAR;cAAkBgG,YAAlB;EAAgCmC,YAAAA,KAAAA;aAA9C,CAAA,CAAA;WARyB;EAU3BL,QAAAA,OAAO,EAAE,MAAM;EACb,UAAA,IAAA,CAAKsD,QAAL,CAAc;EAAEpL,YAAAA,IAAI,EAAE,OAAA;aAAtB,CAAA,CAAA;WAXyB;EAa3B+H,QAAAA,UAAU,EAAE,MAAM;EAChB,UAAA,IAAA,CAAKqD,QAAL,CAAc;EAAEpL,YAAAA,IAAI,EAAE,UAAA;aAAtB,CAAA,CAAA;WAdyB;EAgB3BqI,QAAAA,KAAK,yBAAE,IAAK9H,CAAAA,OAAL,CAAa8H,KAAf,kCAAwB,CAhBF;EAiB3BC,QAAAA,UAAU,EAAE,IAAA,CAAK/H,OAAL,CAAa+H,UAjBE;UAkB3BnC,WAAW,EAAE,IAAK5F,CAAAA,OAAL,CAAa4F,WAAAA;EAlBC,OAAD,CAA5B,CAAA;QAqBA,OAAO,IAAA,CAAKsF,OAAL,CAAa1E,OAApB,CAAA;OAtBF,CAAA;;EAyBA,IAAA,MAAM4I,QAAQ,GAAG,IAAA,CAAKhP,KAAL,CAAWK,MAAX,KAAsB,SAAvC,CAAA;;MACA,IAAI;EAAA,MAAA,IAAA,sBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,cAAA,CAAA;;QACF,IAAI,CAAC2O,QAAL,EAAe;EAAA,QAAA,IAAA,qBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,aAAA,CAAA;;EACb,QAAA,IAAA,CAAKvE,QAAL,CAAc;EAAEpL,UAAAA,IAAI,EAAE,SAAR;YAAmB0P,SAAS,EAAE,IAAKnP,CAAAA,OAAL,CAAamP,SAAAA;EAA3C,SAAd,EADa;;EAGb,QAAA,OAAA,CAAA,qBAAA,GAAM,CAAKH,sBAAAA,GAAAA,IAAAA,CAAAA,aAAL,CAAmB9I,MAAnB,EAA0BmJ,QAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,qBACJ,CAAA,IAAA,CAAA,sBAAA,EAAA,IAAA,CAAKjP,KAAL,CAAW+O,SADP,EAEJ,IAFI,CAAN,CAAA,CAAA;EAIA,QAAA,MAAMnC,OAAO,GAAG,OAAM,CAAA,qBAAA,GAAA,CAAA,aAAA,GAAA,IAAA,CAAKhN,OAAL,EAAaqP,QAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,aAAA,EAAwB,IAAKjP,CAAAA,KAAL,CAAW+O,SAAnC,CAAN,CAAhB,CAAA;;EACA,QAAA,IAAInC,OAAO,KAAK,IAAA,CAAK5M,KAAL,CAAW4M,OAA3B,EAAoC;EAClC,UAAA,IAAA,CAAKnC,QAAL,CAAc;EACZpL,YAAAA,IAAI,EAAE,SADM;cAEZuN,OAFY;cAGZmC,SAAS,EAAE,IAAK/O,CAAAA,KAAL,CAAW+O,SAAAA;aAHxB,CAAA,CAAA;EAKD,SAAA;EACF,OAAA;;EACD,MAAA,MAAMvL,IAAI,GAAG,MAAMsL,eAAe,EAAlC,CAjBE;;QAoBF,OAAM,CAAA,sBAAA,GAAA,CAAA,sBAAA,GAAA,IAAA,CAAKF,aAAL,CAAmB9I,MAAnB,EAA0BgB,SAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,oDACJtD,IADI,EAEJ,KAAKxD,KAAL,CAAW+O,SAFP,EAGJ,IAAA,CAAK/O,KAAL,CAAW4M,OAHP,EAIJ,IAJI,CAAN,CAAA,CAAA;EAOA,MAAA,OAAA,CAAA,qBAAA,GAAM,uBAAKhN,OAAL,EAAakH,SAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,cAAA,EACJtD,IADI,EAEJ,IAAA,CAAKxD,KAAL,CAAW+O,SAFP,EAGJ,IAAK/O,CAAAA,KAAL,CAAW4M,OAHP,CAAN,EA3BE;;QAkCF,OAAM,CAAA,sBAAA,GAAA,CAAA,sBAAA,GAAA,IAAA,CAAKgC,aAAL,CAAmB9I,MAAnB,EAA0BmH,SAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,sBACJzJ,CAAAA,IAAAA,CAAAA,sBAAAA,EAAAA,IADI,EAEJ,IAFI,EAGJ,IAAKxD,CAAAA,KAAL,CAAW+O,SAHP,EAIJ,IAAA,CAAK/O,KAAL,CAAW4M,OAJP,EAKJ,IALI,CAAN,CAAA,CAAA;EAQA,MAAA,OAAA,CAAA,qBAAA,GAAM,uBAAKhN,OAAL,EAAaqN,SAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,cAAA,EACJzJ,IADI,EAEJ,IAFI,EAGJ,IAAKxD,CAAAA,KAAL,CAAW+O,SAHP,EAIJ,KAAK/O,KAAL,CAAW4M,OAJP,CAAN,CAAA,CAAA;EAOA,MAAA,IAAA,CAAKnC,QAAL,CAAc;EAAEpL,QAAAA,IAAI,EAAE,SAAR;EAAmBmE,QAAAA,IAAAA;SAAjC,CAAA,CAAA;EACA,MAAA,OAAOA,IAAP,CAAA;OAlDF,CAmDE,OAAOgE,KAAP,EAAc;QACd,IAAI;EAAA,QAAA,IAAA,sBAAA,EAAA,sBAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,uBAAA,EAAA,sBAAA,EAAA,cAAA,CAAA;;EACF;UACA,OAAM,CAAA,sBAAA,GAAA,CAAA,sBAAA,GAAA,IAAA,CAAKoH,aAAL,CAAmB9I,MAAnB,EAA0BiB,OAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,oDACJS,KADI,EAEJ,KAAKxH,KAAL,CAAW+O,SAFP,EAGJ,IAAA,CAAK/O,KAAL,CAAW4M,OAHP,EAIJ,IAJI,CAAN,CAAA,CAAA;;EAOA,QAAA,IAAIsC,aAAA,KAAyB,YAA7B,EAA2C;EACzC,UAAA,IAAA,CAAKhF,MAAL,CAAY1C,KAAZ,CAAkBA,KAAlB,CAAA,CAAA;EACD,SAAA;;EAED,QAAA,OAAA,CAAA,qBAAA,GAAM,uBAAK5H,OAAL,EAAamH,OAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,cAAA,EACJS,KADI,EAEJ,IAAA,CAAKxH,KAAL,CAAW+O,SAFP,EAGJ,IAAK/O,CAAAA,KAAL,CAAW4M,OAHP,CAAN,EAbE;;UAoBF,OAAM,CAAA,sBAAA,GAAA,CAAA,uBAAA,GAAA,IAAA,CAAKgC,aAAL,CAAmB9I,MAAnB,EAA0BmH,SAAhC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,sBACJpQ,CAAAA,IAAAA,CAAAA,uBAAAA,EAAAA,SADI,EAEJ2K,KAFI,EAGJ,IAAKxH,CAAAA,KAAL,CAAW+O,SAHP,EAIJ,IAAA,CAAK/O,KAAL,CAAW4M,OAJP,EAKJ,IALI,CAAN,CAAA,CAAA;EAQA,QAAA,OAAA,CAAA,sBAAA,GAAM,uBAAKhN,OAAL,EAAaqN,SAAnB,KAAM,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,cAAA,EACJpQ,SADI,EAEJ2K,KAFI,EAGJ,IAAKxH,CAAAA,KAAL,CAAW+O,SAHP,EAIJ,KAAK/O,KAAL,CAAW4M,OAJP,CAAN,CAAA,CAAA;EAMA,QAAA,MAAMpF,KAAN,CAAA;EACD,OAnCD,SAmCU;EACR,QAAA,IAAA,CAAKiD,QAAL,CAAc;EAAEpL,UAAAA,IAAI,EAAE,OAAR;EAAiBmI,UAAAA,KAAK,EAAEA,KAAAA;WAAtC,CAAA,CAAA;EACD,OAAA;EACF,KAAA;EACF,GAAA;;IAEOiD,QAAQ,CAAC0C,MAAD,EAA4D;MAC1E,MAAMC,OAAO,GACXpN,KADc,IAEyC;QACvD,QAAQmN,MAAM,CAAC9N,IAAf;EACE,QAAA,KAAK,QAAL;YACE,OAAO,EACL,GAAGW,KADE;cAELqF,YAAY,EAAE8H,MAAM,CAAC9H,YAFhB;cAGL8J,aAAa,EAAEhC,MAAM,CAAC3F,KAAAA;aAHxB,CAAA;;EAKF,QAAA,KAAK,OAAL;YACE,OAAO,EACL,GAAGxH,KADE;EAELoP,YAAAA,QAAQ,EAAE,IAAA;aAFZ,CAAA;;EAIF,QAAA,KAAK,UAAL;YACE,OAAO,EACL,GAAGpP,KADE;EAELoP,YAAAA,QAAQ,EAAE,KAAA;aAFZ,CAAA;;EAIF,QAAA,KAAK,SAAL;YACE,OAAO,EACL,GAAGpP,KADE;cAEL4M,OAAO,EAAEO,MAAM,CAACP,OAFX;EAGLpJ,YAAAA,IAAI,EAAE3G,SAHD;EAILwI,YAAAA,YAAY,EAAE,CAJT;EAKL8J,YAAAA,aAAa,EAAE,IALV;EAML3H,YAAAA,KAAK,EAAE,IANF;cAOL4H,QAAQ,EAAE,CAAC7J,QAAQ,CAAC,KAAK3F,OAAL,CAAa4F,WAAd,CAPd;EAQLnF,YAAAA,MAAM,EAAE,SARH;cASL0O,SAAS,EAAE5B,MAAM,CAAC4B,SAAAA;aATpB,CAAA;;EAWF,QAAA,KAAK,SAAL;YACE,OAAO,EACL,GAAG/O,KADE;cAELwD,IAAI,EAAE2J,MAAM,CAAC3J,IAFR;EAGL6B,YAAAA,YAAY,EAAE,CAHT;EAIL8J,YAAAA,aAAa,EAAE,IAJV;EAKL3H,YAAAA,KAAK,EAAE,IALF;EAMLnH,YAAAA,MAAM,EAAE,SANH;EAOL+O,YAAAA,QAAQ,EAAE,KAAA;aAPZ,CAAA;;EASF,QAAA,KAAK,OAAL;YACE,OAAO,EACL,GAAGpP,KADE;EAELwD,YAAAA,IAAI,EAAE3G,SAFD;cAGL2K,KAAK,EAAE2F,MAAM,CAAC3F,KAHT;EAILnC,YAAAA,YAAY,EAAErF,KAAK,CAACqF,YAAN,GAAqB,CAJ9B;cAKL8J,aAAa,EAAEhC,MAAM,CAAC3F,KALjB;EAML4H,YAAAA,QAAQ,EAAE,KANL;EAOL/O,YAAAA,MAAM,EAAE,OAAA;aAPV,CAAA;;EASF,QAAA,KAAK,UAAL;YACE,OAAO,EACL,GAAGL,KADE;EAEL,YAAA,GAAGmN,MAAM,CAACnN,KAAAA;aAFZ,CAAA;EAlDJ,OAAA;OAHF,CAAA;;EA2DA,IAAA,IAAA,CAAKA,KAAL,GAAaoN,OAAO,CAAC,IAAA,CAAKpN,KAAN,CAApB,CAAA;MAEAiJ,aAAa,CAACT,KAAd,CAAoB,MAAM;EACxB,MAAA,IAAA,CAAKwB,SAAL,CAAe3F,OAAf,CAAwB2G,QAAD,IAAc;UACnCA,QAAQ,CAACqE,gBAAT,CAA0BlC,MAA1B,CAAA,CAAA;SADF,CAAA,CAAA;QAGA,IAAKyB,CAAAA,aAAL,CAAmB/C,MAAnB,CAA0B;EACxB3L,QAAAA,QAAQ,EAAE,IADc;EAExBb,QAAAA,IAAI,EAAE,SAFkB;EAGxB8N,QAAAA,MAAAA;SAHF,CAAA,CAAA;OAJF,CAAA,CAAA;EAUD,GAAA;;EAlRiB,CAAA;EAqRb,SAAS/C,eAAT,GAKiD;IACtD,OAAO;EACLwC,IAAAA,OAAO,EAAE/P,SADJ;EAEL2G,IAAAA,IAAI,EAAE3G,SAFD;EAGL2K,IAAAA,KAAK,EAAE,IAHF;EAILnC,IAAAA,YAAY,EAAE,CAJT;EAKL8J,IAAAA,aAAa,EAAE,IALV;EAMLC,IAAAA,QAAQ,EAAE,KANL;EAOL/O,IAAAA,MAAM,EAAE,MAPH;EAQL0O,IAAAA,SAAS,EAAElS,SAAAA;KARb,CAAA;EAUD;;EC7SD;EAEO,MAAMyS,aAAN,SAA4B1T,YAA5B,CAAgE;IAOrEC,WAAW,CAACiK,MAAD,EAA+B;EACxC,IAAA,KAAA,EAAA,CAAA;EACA,IAAA,IAAA,CAAKA,MAAL,GAAcA,MAAM,IAAI,EAAxB,CAAA;MACA,IAAKyJ,CAAAA,SAAL,GAAiB,EAAjB,CAAA;MACA,IAAKZ,CAAAA,UAAL,GAAkB,CAAlB,CAAA;EACD,GAAA;;EAEDV,EAAAA,KAAK,CACHC,MADG,EAEHtO,OAFG,EAGHI,KAHG,EAI4C;EAC/C,IAAA,MAAME,QAAQ,GAAG,IAAIwO,QAAJ,CAAa;EAC5BE,MAAAA,aAAa,EAAE,IADa;EAE5B1E,MAAAA,MAAM,EAAEgE,MAAM,CAACC,SAAP,EAFoB;QAG5BQ,UAAU,EAAE,EAAE,IAAA,CAAKA,UAHS;EAI5B/O,MAAAA,OAAO,EAAEsO,MAAM,CAACsB,sBAAP,CAA8B5P,OAA9B,CAJmB;QAK5BI,KAL4B;EAM5B8J,MAAAA,cAAc,EAAElK,OAAO,CAACd,WAAR,GACZoP,MAAM,CAACuB,mBAAP,CAA2B7P,OAAO,CAACd,WAAnC,CADY,GAEZjC,SAAAA;EARwB,KAAb,CAAjB,CAAA;MAWA,IAAKT,CAAAA,GAAL,CAAS8D,QAAT,CAAA,CAAA;EAEA,IAAA,OAAOA,QAAP,CAAA;EACD,GAAA;;IAED9D,GAAG,CAAC8D,QAAD,EAA+C;EAChD,IAAA,IAAA,CAAKqP,SAAL,CAAe5G,IAAf,CAAoBzI,QAApB,CAAA,CAAA;EACA,IAAA,IAAA,CAAK2L,MAAL,CAAY;EAAExM,MAAAA,IAAI,EAAE,OAAR;EAAiBa,MAAAA,QAAAA;OAA7B,CAAA,CAAA;EACD,GAAA;;IAEDoK,MAAM,CAACpK,QAAD,EAA+C;EACnD,IAAA,IAAA,CAAKqP,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAehS,MAAf,CAAuBC,CAAD,IAAOA,CAAC,KAAK0C,QAAnC,CAAjB,CAAA;EACA,IAAA,IAAA,CAAK2L,MAAL,CAAY;EAAExM,MAAAA,IAAI,EAAE,SAAR;EAAmBa,MAAAA,QAAAA;OAA/B,CAAA,CAAA;EACD,GAAA;;EAEDqO,EAAAA,KAAK,GAAS;MACZtF,aAAa,CAACT,KAAd,CAAoB,MAAM;EACxB,MAAA,IAAA,CAAK+G,SAAL,CAAelL,OAAf,CAAwBnE,QAAD,IAAc;UACnC,IAAKoK,CAAAA,MAAL,CAAYpK,QAAZ,CAAA,CAAA;SADF,CAAA,CAAA;OADF,CAAA,CAAA;EAKD,GAAA;;EAEDsO,EAAAA,MAAM,GAAe;EACnB,IAAA,OAAO,KAAKe,SAAZ,CAAA;EACD,GAAA;;IAEDhE,IAAI,CACFpM,OADE,EAEyD;EAC3D,IAAA,IAAI,OAAOA,OAAO,CAACG,KAAf,KAAyB,WAA7B,EAA0C;QACxCH,OAAO,CAACG,KAAR,GAAgB,IAAhB,CAAA;EACD,KAAA;;EAED,IAAA,OAAO,IAAKiQ,CAAAA,SAAL,CAAehE,IAAf,CAAqBrL,QAAD,IAAcD,aAAa,CAACd,OAAD,EAAUe,QAAV,CAA/C,CAAP,CAAA;EACD,GAAA;;IAEDuO,OAAO,CAACtP,OAAD,EAAuC;EAC5C,IAAA,OAAO,IAAKoQ,CAAAA,SAAL,CAAehS,MAAf,CAAuB2C,QAAD,IAAcD,aAAa,CAACd,OAAD,EAAUe,QAAV,CAAjD,CAAP,CAAA;EACD,GAAA;;IAED2L,MAAM,CAAChH,KAAD,EAAkC;MACtCoE,aAAa,CAACT,KAAd,CAAoB,MAAM;EACxB,MAAA,IAAA,CAAK1M,SAAL,CAAeuI,OAAf,CAAuB,CAAC;EAAEnI,QAAAA,QAAAA;EAAF,OAAD,KAAkB;UACvCA,QAAQ,CAAC2I,KAAD,CAAR,CAAA;SADF,CAAA,CAAA;OADF,CAAA,CAAA;EAKD,GAAA;;EAED6K,EAAAA,qBAAqB,GAAqB;EAAA,IAAA,IAAA,cAAA,CAAA;;EACxC,IAAA,IAAA,CAAKC,QAAL,GAAgB,CAAC,CAAA,cAAA,GAAA,IAAA,CAAKA,QAAN,KAAA,IAAA,GAAA,cAAA,GAAkB7M,OAAO,CAACC,OAAR,EAAlB,EACbI,IADa,CACR,MAAM;EACV,MAAA,MAAMyM,eAAe,GAAG,IAAKL,CAAAA,SAAL,CAAehS,MAAf,CAAuBC,CAAD,IAAOA,CAAC,CAACwC,KAAF,CAAQoP,QAArC,CAAxB,CAAA;EACA,MAAA,OAAOnG,aAAa,CAACT,KAAd,CAAoB,MACzBoH,eAAe,CAAC5O,MAAhB,CACE,CAACoF,OAAD,EAAUlG,QAAV,KACEkG,OAAO,CAACjD,IAAR,CAAa,MAAMjD,QAAQ,CAAC6H,QAAT,EAAA,CAAoBN,KAApB,CAA0B7K,IAA1B,CAAnB,CAFJ,EAGEkG,OAAO,CAACC,OAAR,EAHF,CADK,CAAP,CAAA;OAHY,CAAA,CAWbI,IAXa,CAWR,MAAM;QACV,IAAKwM,CAAAA,QAAL,GAAgB9S,SAAhB,CAAA;EACD,KAba,CAAhB,CAAA;EAeA,IAAA,OAAO,KAAK8S,QAAZ,CAAA;EACD,GAAA;;EAhGoE;;EC1EhE,SAASE,qBAAT,GAIuD;IAC5D,OAAO;MACL/C,OAAO,EAAGF,OAAD,IAAa;QACpBA,OAAO,CAACD,OAAR,GAAkB,MAAM;EAAA,QAAA,IAAA,qBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,mBAAA,EAAA,oBAAA,CAAA;;UACtB,MAAMmD,WAA2D,GAC/DlD,CAAAA,qBAAAA,GAAAA,OAAO,CAACX,YADuD,+CAC/D,qBAAsB5B,CAAAA,IADyC,KAC/D,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAA4ByF,WAD9B,CAAA;UAEA,MAAMC,SAAS,GAAGnD,CAAAA,sBAAAA,GAAAA,OAAO,CAACX,YAAX,+CAAG,sBAAsB5B,CAAAA,IAAzB,KAAG,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAA4B0F,SAA9C,CAAA;EACA,QAAA,MAAM3D,SAAS,GAAG2D,SAAH,IAAGA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,SAAS,CAAE3D,SAA7B,CAAA;UACA,MAAM4D,kBAAkB,GAAG,CAAAD,SAAS,IAAA,IAAT,YAAAA,SAAS,CAAEE,SAAX,MAAyB,SAApD,CAAA;UACA,MAAMC,sBAAsB,GAAG,CAAAH,SAAS,IAAA,IAAT,YAAAA,SAAS,CAAEE,SAAX,MAAyB,UAAxD,CAAA;UACA,MAAME,QAAQ,GAAG,CAAA,CAAA,mBAAA,GAAAvD,OAAO,CAAC5M,KAAR,CAAcwD,IAAd,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,mBAAA,CAAoB4M,KAApB,KAA6B,EAA9C,CAAA;UACA,MAAMC,aAAa,GAAG,CAAA,CAAA,oBAAA,GAAAzD,OAAO,CAAC5M,KAAR,CAAcwD,IAAd,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,oBAAA,CAAoB8M,UAApB,KAAkC,EAAxD,CAAA;UACA,IAAIC,aAAa,GAAGF,aAApB,CAAA;UACA,IAAIG,SAAS,GAAG,KAAhB,CAAA;;UAEA,MAAMnE,iBAAiB,GAAIC,MAAD,IAAqB;EAC7CzL,UAAAA,MAAM,CAAC0L,cAAP,CAAsBD,MAAtB,EAA8B,QAA9B,EAAwC;EACtCE,YAAAA,UAAU,EAAE,IAD0B;EAEtCC,YAAAA,GAAG,EAAE,MAAM;EAAA,cAAA,IAAA,eAAA,CAAA;;EACT,cAAA,IAAA,CAAA,eAAA,GAAIG,OAAO,CAACF,MAAZ,KAAI,IAAA,IAAA,eAAA,CAAgB+D,OAApB,EAA6B;EAC3BD,gBAAAA,SAAS,GAAG,IAAZ,CAAA;EACD,eAFD,MAEO;EAAA,gBAAA,IAAA,gBAAA,CAAA;;kBACL,CAAA5D,gBAAAA,GAAAA,OAAO,CAACF,MAAR,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,gBAAA,CAAgB5I,gBAAhB,CAAiC,OAAjC,EAA0C,MAAM;EAC9C0M,kBAAAA,SAAS,GAAG,IAAZ,CAAA;mBADF,CAAA,CAAA;EAGD,eAAA;;gBACD,OAAO5D,OAAO,CAACF,MAAf,CAAA;EACD,aAAA;aAXH,CAAA,CAAA;EAaD,SAdD,CAZsB;;;EA6BtB,QAAA,MAAM9N,OAAO,GACXgO,OAAO,CAAChN,OAAR,CAAgBhB,OAAhB,KACC,MACCkE,OAAO,CAAC2D,MAAR,oCACmCmG,OAAO,CAAChN,OAAR,CAAgBF,SADnD,OAFF,CADF,CAAA;;UAOA,MAAMgR,aAAa,GAAG,CACpBN,KADoB,EAEpBO,KAFoB,EAGpBC,IAHoB,EAIpBC,QAJoB,KAKjB;EACHN,UAAAA,aAAa,GAAGM,QAAQ,GACpB,CAACF,KAAD,EAAQ,GAAGJ,aAAX,CADoB,GAEpB,CAAC,GAAGA,aAAJ,EAAmBI,KAAnB,CAFJ,CAAA;EAGA,UAAA,OAAOE,QAAQ,GAAG,CAACD,IAAD,EAAO,GAAGR,KAAV,CAAH,GAAsB,CAAC,GAAGA,KAAJ,EAAWQ,IAAX,CAArC,CAAA;EACD,SAVD,CApCsB;;;UAiDtB,MAAME,SAAS,GAAG,CAChBV,KADgB,EAEhBzF,MAFgB,EAGhBgG,KAHgB,EAIhBE,QAJgB,KAKO;EACvB,UAAA,IAAIL,SAAJ,EAAe;EACb,YAAA,OAAO1N,OAAO,CAAC2D,MAAR,CAAe,WAAf,CAAP,CAAA;EACD,WAAA;;YAED,IAAI,OAAOkK,KAAP,KAAiB,WAAjB,IAAgC,CAAChG,MAAjC,IAA2CyF,KAAK,CAAC1O,MAArD,EAA6D;EAC3D,YAAA,OAAOoB,OAAO,CAACC,OAAR,CAAgBqN,KAAhB,CAAP,CAAA;EACD,WAAA;;EAED,UAAA,MAAMjE,cAAoC,GAAG;cAC3CxN,QAAQ,EAAEiO,OAAO,CAACjO,QADyB;EAE3CyN,YAAAA,SAAS,EAAEuE,KAFgC;EAG3CtG,YAAAA,IAAI,EAAEuC,OAAO,CAAChN,OAAR,CAAgByK,IAAAA;aAHxB,CAAA;YAMAgC,iBAAiB,CAACF,cAAD,CAAjB,CAAA;EAEA,UAAA,MAAM4E,aAAa,GAAGnS,OAAO,CAACuN,cAAD,CAA7B,CAAA;YAEA,MAAM/F,OAAO,GAAGtD,OAAO,CAACC,OAAR,CAAgBgO,aAAhB,CAA+B5N,CAAAA,IAA/B,CAAqCyN,IAAD,IAClDF,aAAa,CAACN,KAAD,EAAQO,KAAR,EAAeC,IAAf,EAAqBC,QAArB,CADC,CAAhB,CAAA;EAIA,UAAA,OAAOzK,OAAP,CAAA;WA5BF,CAAA;;UA+BA,IAAIA,OAAJ,CAhFsB;;EAmFtB,QAAA,IAAI,CAAC+J,QAAQ,CAACzO,MAAd,EAAsB;EACpB0E,UAAAA,OAAO,GAAG0K,SAAS,CAAC,EAAD,CAAnB,CAAA;EACD,SAFD;eAKK,IAAId,kBAAJ,EAAwB;EAC3B,UAAA,MAAMrF,MAAM,GAAG,OAAOyB,SAAP,KAAqB,WAApC,CAAA;EACA,UAAA,MAAMuE,KAAK,GAAGhG,MAAM,GAChByB,SADgB,GAEhB4E,gBAAgB,CAACpE,OAAO,CAAChN,OAAT,EAAkBuQ,QAAlB,CAFpB,CAAA;YAGA/J,OAAO,GAAG0K,SAAS,CAACX,QAAD,EAAWxF,MAAX,EAAmBgG,KAAnB,CAAnB,CAAA;EACD,SANI;eASA,IAAIT,sBAAJ,EAA4B;EAC/B,UAAA,MAAMvF,MAAM,GAAG,OAAOyB,SAAP,KAAqB,WAApC,CAAA;EACA,UAAA,MAAMuE,KAAK,GAAGhG,MAAM,GAChByB,SADgB,GAEhB6E,oBAAoB,CAACrE,OAAO,CAAChN,OAAT,EAAkBuQ,QAAlB,CAFxB,CAAA;YAGA/J,OAAO,GAAG0K,SAAS,CAACX,QAAD,EAAWxF,MAAX,EAAmBgG,KAAnB,EAA0B,IAA1B,CAAnB,CAAA;EACD,SANI;eASA;EACHJ,UAAAA,aAAa,GAAG,EAAhB,CAAA;YAEA,MAAM5F,MAAM,GAAG,OAAOiC,OAAO,CAAChN,OAAR,CAAgBoR,gBAAvB,KAA4C,WAA3D,CAAA;YAEA,MAAME,oBAAoB,GACxBpB,WAAW,IAAIK,QAAQ,CAAC,CAAD,CAAvB,GACIL,WAAW,CAACK,QAAQ,CAAC,CAAD,CAAT,EAAc,CAAd,EAAiBA,QAAjB,CADf,GAEI,IAHN,CALG;;EAWH/J,UAAAA,OAAO,GAAG8K,oBAAoB,GAC1BJ,SAAS,CAAC,EAAD,EAAKnG,MAAL,EAAa0F,aAAa,CAAC,CAAD,CAA1B,CADiB,GAE1BvN,OAAO,CAACC,OAAR,CAAgB2N,aAAa,CAAC,EAAD,EAAKL,aAAa,CAAC,CAAD,CAAlB,EAAuBF,QAAQ,CAAC,CAAD,CAA/B,CAA7B,CAFJ,CAXG;;EAgBH,UAAA,KAAK,IAAIrO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqO,QAAQ,CAACzO,MAA7B,EAAqCI,CAAC,EAAtC,EAA0C;EACxCsE,YAAAA,OAAO,GAAGA,OAAO,CAACjD,IAAR,CAAciN,KAAD,IAAW;gBAChC,MAAMe,mBAAmB,GACvBrB,WAAW,IAAIK,QAAQ,CAACrO,CAAD,CAAvB,GACIgO,WAAW,CAACK,QAAQ,CAACrO,CAAD,CAAT,EAAcA,CAAd,EAAiBqO,QAAjB,CADf,GAEI,IAHN,CAAA;;EAKA,cAAA,IAAIgB,mBAAJ,EAAyB;EACvB,gBAAA,MAAMR,KAAK,GAAGhG,MAAM,GAChB0F,aAAa,CAACvO,CAAD,CADG,GAEhBkP,gBAAgB,CAACpE,OAAO,CAAChN,OAAT,EAAkBwQ,KAAlB,CAFpB,CAAA;EAGA,gBAAA,OAAOU,SAAS,CAACV,KAAD,EAAQzF,MAAR,EAAgBgG,KAAhB,CAAhB,CAAA;EACD,eAAA;;EACD,cAAA,OAAO7N,OAAO,CAACC,OAAR,CACL2N,aAAa,CAACN,KAAD,EAAQC,aAAa,CAACvO,CAAD,CAArB,EAA0BqO,QAAQ,CAACrO,CAAD,CAAlC,CADR,CAAP,CAAA;EAGD,aAfS,CAAV,CAAA;EAgBD,WAAA;EACF,SAAA;;EAED,QAAA,MAAMsP,YAAY,GAAGhL,OAAO,CAACjD,IAAR,CAAciN,KAAD,KAAY;YAC5CA,KAD4C;EAE5CE,UAAAA,UAAU,EAAEC,aAAAA;EAFgC,SAAZ,CAAb,CAArB,CAAA;EAKA,QAAA,OAAOa,YAAP,CAAA;SAnJF,CAAA;EAqJD,KAAA;KAvJH,CAAA;EAyJD,CAAA;EAEM,SAASJ,gBAAT,CACLpR,OADK,EAELwQ,KAFK,EAGgB;EACrB,EAAA,OAAOxQ,OAAO,CAACoR,gBAAf,oBAAOpR,OAAO,CAACoR,gBAAR,CAA2BZ,KAAK,CAACA,KAAK,CAAC1O,MAAN,GAAe,CAAhB,CAAhC,EAAoD0O,KAApD,CAAP,CAAA;EACD,CAAA;EAEM,SAASa,oBAAT,CACLrR,OADK,EAELwQ,KAFK,EAGgB;EACrB,EAAA,OAAOxQ,OAAO,CAACqR,oBAAf,IAAA,IAAA,GAAA,KAAA,CAAA,GAAOrR,OAAO,CAACqR,oBAAR,CAA+Bb,KAAK,CAAC,CAAD,CAApC,EAAyCA,KAAzC,CAAP,CAAA;EACD,CAAA;EAED;EACA;EACA;EACA;;EACO,SAASiB,WAAT,CACLzR,OADK,EAELwQ,KAFK,EAGgB;IACrB,IAAIxQ,OAAO,CAACoR,gBAAR,IAA4BhP,KAAK,CAACC,OAAN,CAAcmO,KAAd,CAAhC,EAAsD;EACpD,IAAA,MAAMkB,aAAa,GAAGN,gBAAgB,CAACpR,OAAD,EAAUwQ,KAAV,CAAtC,CAAA;MACA,OACE,OAAOkB,aAAP,KAAyB,WAAzB,IACAA,aAAa,KAAK,IADlB,IAEAA,aAAa,KAAK,KAHpB,CAAA;EAKD,GAAA;;EACD,EAAA,OAAA;EACD,CAAA;EAED;EACA;EACA;EACA;;EACO,SAASC,eAAT,CACL3R,OADK,EAELwQ,KAFK,EAGgB;IACrB,IAAIxQ,OAAO,CAACqR,oBAAR,IAAgCjP,KAAK,CAACC,OAAN,CAAcmO,KAAd,CAApC,EAA0D;EACxD,IAAA,MAAMoB,iBAAiB,GAAGP,oBAAoB,CAACrR,OAAD,EAAUwQ,KAAV,CAA9C,CAAA;MACA,OACE,OAAOoB,iBAAP,KAA6B,WAA7B,IACAA,iBAAiB,KAAK,IADtB,IAEAA,iBAAiB,KAAK,KAHxB,CAAA;EAKD,GAAA;;EACD,EAAA,OAAA;EACD;;ECnKD;EAEO,MAAMC,WAAN,CAAkB;EAWvB5V,EAAAA,WAAW,CAACiK,MAAyB,GAAG,EAA7B,EAAiC;MAC1C,IAAK4L,CAAAA,UAAL,GAAkB5L,MAAM,CAAC4L,UAAP,IAAqB,IAAI5D,UAAJ,EAAvC,CAAA;MACA,IAAKc,CAAAA,aAAL,GAAqB9I,MAAM,CAAC8I,aAAP,IAAwB,IAAIU,aAAJ,EAA7C,CAAA;EACA,IAAA,IAAA,CAAKpF,MAAL,GAAcpE,MAAM,CAACoE,MAAP,IAAiBjC,aAA/B,CAAA;EACA,IAAA,IAAA,CAAK6B,cAAL,GAAsBhE,MAAM,CAACgE,cAAP,IAAyB,EAA/C,CAAA;MACA,IAAK6H,CAAAA,aAAL,GAAqB,EAArB,CAAA;MACA,IAAKC,CAAAA,gBAAL,GAAwB,EAAxB,CAAA;MACA,IAAKC,CAAAA,UAAL,GAAkB,CAAlB,CAAA;;MAEA,IAA6C/L,MAAM,CAACoE,MAApD,EAA4D;QAC1D,IAAKA,CAAAA,MAAL,CAAY1C,KAAZ,CAAA,4FAAA,CAAA,CAAA;EAGD,KAAA;EACF,GAAA;;EAEDsK,EAAAA,KAAK,GAAS;EACZ,IAAA,IAAA,CAAKD,UAAL,EAAA,CAAA;EACA,IAAA,IAAI,IAAKA,CAAAA,UAAL,KAAoB,CAAxB,EAA2B,OAAA;EAE3B,IAAA,IAAA,CAAKE,gBAAL,GAAwBtN,YAAY,CAACzI,SAAb,CAAuB,MAAM;EACnD,MAAA,IAAIyI,YAAY,CAACH,SAAb,EAAJ,EAA8B;EAC5B,QAAA,IAAA,CAAKoL,qBAAL,EAAA,CAAA;UACA,IAAKgC,CAAAA,UAAL,CAAgB7N,OAAhB,EAAA,CAAA;EACD,OAAA;EACF,KALuB,CAAxB,CAAA;EAMA,IAAA,IAAA,CAAKmO,iBAAL,GAAyB7M,aAAa,CAACnJ,SAAd,CAAwB,MAAM;EACrD,MAAA,IAAImJ,aAAa,CAACH,QAAd,EAAJ,EAA8B;EAC5B,QAAA,IAAA,CAAK0K,qBAAL,EAAA,CAAA;UACA,IAAKgC,CAAAA,UAAL,CAAgB9M,QAAhB,EAAA,CAAA;EACD,OAAA;EACF,KALwB,CAAzB,CAAA;EAMD,GAAA;;EAEDqN,EAAAA,OAAO,GAAS;EAAA,IAAA,IAAA,qBAAA,EAAA,qBAAA,CAAA;;EACd,IAAA,IAAA,CAAKJ,UAAL,EAAA,CAAA;EACA,IAAA,IAAI,IAAKA,CAAAA,UAAL,KAAoB,CAAxB,EAA2B,OAAA;EAE3B,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAKE,gBAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;MACA,IAAKA,CAAAA,gBAAL,GAAwBlV,SAAxB,CAAA;EAEA,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAKmV,iBAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;MACA,IAAKA,CAAAA,iBAAL,GAAyBnV,SAAzB,CAAA;EACD,GAAA;;EAUD;EACF;EACA;EACEqV,EAAAA,UAAU,CAAC3T,IAAD,EAAiCC,IAAjC,EAA8D;MACtE,MAAM,CAACW,OAAD,CAAYH,GAAAA,eAAe,CAACT,IAAD,EAAOC,IAAP,CAAjC,CAAA;MACAW,OAAO,CAACI,WAAR,GAAsB,UAAtB,CAAA;EACA,IAAA,OAAO,KAAKmS,UAAL,CAAgBjD,OAAhB,CAAwBtP,OAAxB,EAAiCuC,MAAxC,CAAA;EACD,GAAA;;IAEDyQ,UAAU,CAAChT,OAAD,EAAoC;EAC5C,IAAA,OAAO,KAAKyP,aAAL,CAAmBH,OAAnB,CAA2B,EAAE,GAAGtP,OAAL;EAAcgB,MAAAA,QAAQ,EAAE,IAAA;EAAxB,KAA3B,EAA2DuB,MAAlE,CAAA;EACD,GAAA;;EAYD;EACF;EACA;EACE0Q,EAAAA,YAAY,CACVzT,QADU,EAEVQ,OAFU,EAGgB;EAAA,IAAA,IAAA,qBAAA,CAAA;;EAC1B,IAAA,OAAA,CAAA,qBAAA,GAAO,IAAKuS,CAAAA,UAAL,CAAgBnG,IAAhB,CAAmC5M,QAAnC,EAA6CQ,OAA7C,CAAP,KAAA,IAAA,GAAA,KAAA,CAAA,GAAO,qBAAuDa,CAAAA,KAAvD,CAA6DwD,IAApE,CAAA;EACD,GAAA;;EA4CD;EACF;EACA;EACE6O,EAAAA,eAAe,CAMb9T,IANa,EAYbC,IAZa,EAebC,IAfa,EAgBG;MAChB,MAAM6T,aAAa,GAAGhU,cAAc,CAACC,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAApC,CAAA;MACA,MAAM8T,UAAU,GAAG,IAAKH,CAAAA,YAAL,CAAyBE,aAAa,CAAC3T,QAAvC,CAAnB,CAAA;EAEA,IAAA,OAAO4T,UAAU,GACbzP,OAAO,CAACC,OAAR,CAAgBwP,UAAhB,CADa,GAEb,IAAA,CAAKC,UAAL,CAAgBF,aAAhB,CAFJ,CAAA;EAGD,GAAA;;EAWD;EACF;EACA;IACEG,cAAc,CACZC,iBADY,EAE4B;MACxC,OAAO,IAAA,CAAKC,aAAL,EACJlE,CAAAA,OADI,CACIiE,iBADJ,CAAA,CAEJE,GAFI,CAEA,CAAC;QAAEjU,QAAF;EAAYqB,MAAAA,KAAAA;EAAZ,KAAD,KAAyB;EAC5B,MAAA,MAAMwD,IAAI,GAAGxD,KAAK,CAACwD,IAAnB,CAAA;EACA,MAAA,OAAO,CAAC7E,QAAD,EAAW6E,IAAX,CAAP,CAAA;EACD,KALI,CAAP,CAAA;EAMD,GAAA;;EAEDqP,EAAAA,YAAY,CACVlU,QADU,EAEV5B,OAFU,EAGV6C,OAHU,EAIgB;MAC1B,MAAMR,KAAK,GAAG,IAAKsS,CAAAA,UAAL,CAAgBnG,IAAhB,CAAmC5M,QAAnC,CAAd,CAAA;MACA,MAAM4E,QAAQ,GAAGnE,KAAH,IAAA,IAAA,GAAA,KAAA,CAAA,GAAGA,KAAK,CAAEY,KAAP,CAAawD,IAA9B,CAAA;EACA,IAAA,MAAMA,IAAI,GAAG1G,gBAAgB,CAACC,OAAD,EAAUwG,QAAV,CAA7B,CAAA;;EAEA,IAAA,IAAI,OAAOC,IAAP,KAAgB,WAApB,EAAiC;EAC/B,MAAA,OAAO3G,SAAP,CAAA;EACD,KAAA;;EAED,IAAA,MAAMyV,aAAa,GAAGhU,cAAc,CAACK,QAAD,CAApC,CAAA;EACA,IAAA,MAAMmU,gBAAgB,GAAG,IAAA,CAAK1E,mBAAL,CAAyBkE,aAAzB,CAAzB,CAAA;EACA,IAAA,OAAO,IAAKZ,CAAAA,UAAL,CACJzD,KADI,CACE,IADF,EACQ6E,gBADR,CAAA,CAEJvI,OAFI,CAEI/G,IAFJ,EAEU,EAAE,GAAG5D,OAAL;EAAc+K,MAAAA,MAAM,EAAE,IAAA;EAAtB,KAFV,CAAP,CAAA;EAGD,GAAA;;EAeD;EACF;EACA;EACEoI,EAAAA,cAAc,CACZL,iBADY,EAEZ3V,OAFY,EAGZ6C,OAHY,EAI4B;EACxC,IAAA,OAAOqJ,aAAa,CAACT,KAAd,CAAoB,MACzB,IAAKmK,CAAAA,aAAL,EACGlE,CAAAA,OADH,CACWiE,iBADX,CAEGE,CAAAA,GAFH,CAEO,CAAC;EAAEjU,MAAAA,QAAAA;EAAF,KAAD,KAAkB,CACrBA,QADqB,EAErB,KAAKkU,YAAL,CAAgClU,QAAhC,EAA0C5B,OAA1C,EAAmD6C,OAAnD,CAFqB,CAFzB,CADK,CAAP,CAAA;EAQD,GAAA;;EAEDoT,EAAAA,aAAa,CACXrU,QADW;EAEX;EACJ;EACA;EACIQ,EAAAA,OALW,EAMmC;EAAA,IAAA,IAAA,sBAAA,CAAA;;MAC9C,OAAO,CAAA,sBAAA,GAAA,IAAA,CAAKuS,UAAL,CAAgBnG,IAAhB,CAA2C5M,QAA3C,EAAqDQ,OAArD,CAAP,KAAO,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAA+Da,KAAtE,CAAA;EACD,GAAA;;EAUD;EACF;EACA;EACEiT,EAAAA,aAAa,CACX1U,IADW,EAEXC,IAFW,EAGL;MACN,MAAM,CAACW,OAAD,CAAYH,GAAAA,eAAe,CAACT,IAAD,EAAOC,IAAP,CAAjC,CAAA;MACA,MAAMkT,UAAU,GAAG,IAAA,CAAKA,UAAxB,CAAA;MACAzI,aAAa,CAACT,KAAd,CAAoB,MAAM;QACxBkJ,UAAU,CAACjD,OAAX,CAAmBtP,OAAnB,EAA4BkF,OAA5B,CAAqCjF,KAAD,IAAW;UAC7CsS,UAAU,CAACpH,MAAX,CAAkBlL,KAAlB,CAAA,CAAA;SADF,CAAA,CAAA;OADF,CAAA,CAAA;EAKD,GAAA;;EAcD;EACF;EACA;EACE8T,EAAAA,YAAY,CACV3U,IADU,EAEVC,IAFU,EAGVC,IAHU,EAIK;EACf,IAAA,MAAM,CAACU,OAAD,EAAUS,OAAV,CAAqBZ,GAAAA,eAAe,CAACT,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAA1C,CAAA;MACA,MAAMiT,UAAU,GAAG,IAAA,CAAKA,UAAxB,CAAA;EAEA,IAAA,MAAMyB,cAAmC,GAAG;EAC1C9T,MAAAA,IAAI,EAAE,QADoC;QAE1C,GAAGF,OAAAA;OAFL,CAAA;EAKA,IAAA,OAAO8J,aAAa,CAACT,KAAd,CAAoB,MAAM;QAC/BkJ,UAAU,CAACjD,OAAX,CAAmBtP,OAAnB,EAA4BkF,OAA5B,CAAqCjF,KAAD,IAAW;EAC7CA,QAAAA,KAAK,CAAC2L,KAAN,EAAA,CAAA;SADF,CAAA,CAAA;EAGA,MAAA,OAAO,KAAKqI,cAAL,CAAoBD,cAApB,EAAoCvT,OAApC,CAAP,CAAA;EACD,KALM,CAAP,CAAA;EAMD,GAAA;;EAWD;EACF;EACA;EACEyT,EAAAA,aAAa,CACX9U,IADW,EAEXC,IAFW,EAGXC,IAHW,EAII;EACf,IAAA,MAAM,CAACU,OAAD,EAAUqH,aAAa,GAAG,EAA1B,CAAA,GAAgCxH,eAAe,CAACT,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAArD,CAAA;;EAEA,IAAA,IAAI,OAAO+H,aAAa,CAACd,MAArB,KAAgC,WAApC,EAAiD;QAC/Cc,aAAa,CAACd,MAAd,GAAuB,IAAvB,CAAA;EACD,KAAA;;MAED,MAAM4N,QAAQ,GAAGrK,aAAa,CAACT,KAAd,CAAoB,MACnC,IAAA,CAAKkJ,UAAL,CACGjD,OADH,CACWtP,OADX,CAEGyT,CAAAA,GAFH,CAEQxT,KAAD,IAAWA,KAAK,CAACmH,MAAN,CAAaC,aAAb,CAFlB,CADe,CAAjB,CAAA;EAMA,IAAA,OAAO1D,OAAO,CAACyQ,GAAR,CAAYD,QAAZ,CAAA,CAAsBnQ,IAAtB,CAA2BvG,IAA3B,CAAA,CAAiC6K,KAAjC,CAAuC7K,IAAvC,CAAP,CAAA;EACD,GAAA;;EAcD;EACF;EACA;EACE4W,EAAAA,iBAAiB,CACfjV,IADe,EAEfC,IAFe,EAGfC,IAHe,EAIA;EACf,IAAA,MAAM,CAACU,OAAD,EAAUS,OAAV,CAAqBZ,GAAAA,eAAe,CAACT,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAA1C,CAAA;EAEA,IAAA,OAAOwK,aAAa,CAACT,KAAd,CAAoB,MAAM;EAAA,MAAA,IAAA,IAAA,EAAA,oBAAA,CAAA;;QAC/B,IAAKkJ,CAAAA,UAAL,CAAgBjD,OAAhB,CAAwBtP,OAAxB,CAAiCkF,CAAAA,OAAjC,CAA0CjF,KAAD,IAAW;EAClDA,QAAAA,KAAK,CAAC2M,UAAN,EAAA,CAAA;SADF,CAAA,CAAA;;EAIA,MAAA,IAAI5M,OAAO,CAACsU,WAAR,KAAwB,MAA5B,EAAoC;UAClC,OAAO3Q,OAAO,CAACC,OAAR,EAAP,CAAA;EACD,OAAA;;EACD,MAAA,MAAMoQ,cAAmC,GAAG,EAC1C,GAAGhU,OADuC;UAE1CE,IAAI,EAAA,CAAA,IAAA,GAAA,CAAA,oBAAA,GAAEF,OAAO,CAACsU,WAAV,mCAAyBtU,OAAO,CAACE,IAAjC,KAAyC,IAAA,GAAA,IAAA,GAAA,QAAA;SAF/C,CAAA;EAIA,MAAA,OAAO,KAAK+T,cAAL,CAAoBD,cAApB,EAAoCvT,OAApC,CAAP,CAAA;EACD,KAbM,CAAP,CAAA;EAcD,GAAA;;EAcD;EACF;EACA;EACEwT,EAAAA,cAAc,CACZ7U,IADY,EAEZC,IAFY,EAGZC,IAHY,EAIG;EACf,IAAA,MAAM,CAACU,OAAD,EAAUS,OAAV,CAAqBZ,GAAAA,eAAe,CAACT,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAA1C,CAAA;MAEA,MAAM6U,QAAQ,GAAGrK,aAAa,CAACT,KAAd,CAAoB,MACnC,IAAKkJ,CAAAA,UAAL,CACGjD,OADH,CACWtP,OADX,EAEG5B,MAFH,CAEW6B,KAAD,IAAW,CAACA,KAAK,CAAC8L,UAAN,EAFtB,CAAA,CAGG0H,GAHH,CAGQxT,KAAD,IAAA;EAAA,MAAA,IAAA,qBAAA,CAAA;;QAAA,OACHA,KAAK,CAAC4M,KAAN,CAAYnP,SAAZ,EAAuB,EACrB,GAAG+C,OADkB;EAErB8L,QAAAA,aAAa,2BAAE9L,OAAF,IAAA,IAAA,GAAA,KAAA,CAAA,GAAEA,OAAO,CAAE8L,aAAX,oCAA4B,IAFpB;EAGrBrB,QAAAA,IAAI,EAAE;YAAEyF,WAAW,EAAE3Q,OAAO,CAAC2Q,WAAAA;EAAvB,SAAA;EAHe,OAAvB,CADG,CAAA;EAAA,KAHP,CADe,CAAjB,CAAA;MAaA,IAAI1J,OAAO,GAAGtD,OAAO,CAACyQ,GAAR,CAAYD,QAAZ,CAAsBnQ,CAAAA,IAAtB,CAA2BvG,IAA3B,CAAd,CAAA;;EAEA,IAAA,IAAI,EAACgD,OAAD,IAAA,IAAA,IAACA,OAAO,CAAE8T,YAAV,CAAJ,EAA4B;EAC1BtN,MAAAA,OAAO,GAAGA,OAAO,CAACqB,KAAR,CAAc7K,IAAd,CAAV,CAAA;EACD,KAAA;;EAED,IAAA,OAAOwJ,OAAP,CAAA;EACD,GAAA;;EAyCD;EACF;EACA;EACEoM,EAAAA,UAAU,CAMRjU,IANQ,EAORC,IAPQ,EAaRC,IAbQ,EAiBQ;MAChB,MAAM6T,aAAa,GAAGhU,cAAc,CAACC,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAApC,CAAA;MACA,MAAMqU,gBAAgB,GAAG,IAAK1E,CAAAA,mBAAL,CAAyBkE,aAAzB,CAAzB,CAFgB;;EAKhB,IAAA,IAAI,OAAOQ,gBAAgB,CAACpL,KAAxB,KAAkC,WAAtC,EAAmD;QACjDoL,gBAAgB,CAACpL,KAAjB,GAAyB,KAAzB,CAAA;EACD,KAAA;;MAED,MAAMtI,KAAK,GAAG,IAAA,CAAKsS,UAAL,CAAgBzD,KAAhB,CAAsB,IAAtB,EAA4B6E,gBAA5B,CAAd,CAAA;MAEA,OAAO1T,KAAK,CAACkM,aAAN,CAAoBwH,gBAAgB,CAAC7U,SAArC,CAAA,GACHmB,KAAK,CAAC4M,KAAN,CAAY8G,gBAAZ,CADG,GAEHhQ,OAAO,CAACC,OAAR,CAAgB3D,KAAK,CAACY,KAAN,CAAYwD,IAA5B,CAFJ,CAAA;EAGD,GAAA;;EAyCD;EACF;EACA;EACEmQ,EAAAA,aAAa,CAMXpV,IANW,EAOXC,IAPW,EAaXC,IAbW,EAiBI;EACf,IAAA,OAAO,KAAK+T,UAAL,CAAgBjU,IAAhB,EAA6BC,IAA7B,EAA0CC,IAA1C,CACJ0E,CAAAA,IADI,CACCvG,IADD,CAAA,CAEJ6K,KAFI,CAEE7K,IAFF,CAAP,CAAA;EAGD,GAAA;;EAyCD;EACF;EACA;EACEgX,EAAAA,kBAAkB,CAMhBrV,IANgB,EAShBC,IATgB,EAehBC,IAfgB,EAmBc;MAC9B,MAAM6T,aAAa,GAAGhU,cAAc,CAACC,IAAD,EAAOC,IAAP,EAAaC,IAAb,CAApC,CAAA;EACA6T,IAAAA,aAAa,CAACzF,QAAd,GAAyBgD,qBAAqB,EAA9C,CAAA;EAKA,IAAA,OAAO,IAAK2C,CAAAA,UAAL,CAAgBF,aAAhB,CAAP,CAAA;EACD,GAAA;;EAyCD;EACF;EACA;EACEuB,EAAAA,qBAAqB,CAMnBtV,IANmB,EASnBC,IATmB,EAenBC,IAfmB,EAmBJ;EACf,IAAA,OAAO,KAAKmV,kBAAL,CAAwBrV,IAAxB,EAAqCC,IAArC,EAAkDC,IAAlD,CACJ0E,CAAAA,IADI,CACCvG,IADD,CAAA,CAEJ6K,KAFI,CAEE7K,IAFF,CAAP,CAAA;EAGD,GAAA;;EAED8S,EAAAA,qBAAqB,GAAqB;EACxC,IAAA,OAAO,IAAKd,CAAAA,aAAL,CAAmBc,qBAAnB,EAAP,CAAA;EACD,GAAA;;EAEDiD,EAAAA,aAAa,GAAe;EAC1B,IAAA,OAAO,KAAKjB,UAAZ,CAAA;EACD,GAAA;;EAEDoC,EAAAA,gBAAgB,GAAkB;EAChC,IAAA,OAAO,KAAKlF,aAAZ,CAAA;EACD,GAAA;;EAEDT,EAAAA,SAAS,GAAW;EAClB,IAAA,OAAO,KAAKjE,MAAZ,CAAA;EACD,GAAA;;EAED6J,EAAAA,iBAAiB,GAAmB;EAClC,IAAA,OAAO,KAAKjK,cAAZ,CAAA;EACD,GAAA;;IAEDkK,iBAAiB,CAACpU,OAAD,EAAgC;MAC/C,IAAKkK,CAAAA,cAAL,GAAsBlK,OAAtB,CAAA;EACD,GAAA;;EAEDqU,EAAAA,gBAAgB,CACdtV,QADc,EAEdiB,OAFc,EAGR;MACN,MAAMqB,MAAM,GAAG,IAAK0Q,CAAAA,aAAL,CAAmBpG,IAAnB,CACZ/N,CAAD,IAAO4C,YAAY,CAACzB,QAAD,CAAZ,KAA2ByB,YAAY,CAAC5C,CAAC,CAACmB,QAAH,CADjC,CAAf,CAAA;;EAGA,IAAA,IAAIsC,MAAJ,EAAY;QACVA,MAAM,CAAC6I,cAAP,GAAwBlK,OAAxB,CAAA;EACD,KAFD,MAEO;QACL,IAAK+R,CAAAA,aAAL,CAAmBhJ,IAAnB,CAAwB;UAAEhK,QAAF;EAAYmL,QAAAA,cAAc,EAAElK,OAAAA;SAApD,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAEDyO,gBAAgB,CACd1P,QADc,EAE6C;MAC3D,IAAI,CAACA,QAAL,EAAe;EACb,MAAA,OAAO9B,SAAP,CAAA;EACD,KAH0D;;;EAM3D,IAAA,MAAMqX,qBAAqB,GAAG,IAAA,CAAKvC,aAAL,CAAmBpG,IAAnB,CAAyB/N,CAAD,IACpDqC,eAAe,CAAClB,QAAD,EAAWnB,CAAC,CAACmB,QAAb,CADa,CAA9B,CAN2D;;EAW3D,IAA2C;EACzC;EACA,MAAA,MAAMwV,gBAAgB,GAAG,IAAA,CAAKxC,aAAL,CAAmBpU,MAAnB,CAA2BC,CAAD,IACjDqC,eAAe,CAAClB,QAAD,EAAWnB,CAAC,CAACmB,QAAb,CADQ,CAAzB,CAFyC;;EAMzC,MAAA,IAAIwV,gBAAgB,CAACzS,MAAjB,GAA0B,CAA9B,EAAiC;UAC/B,IAAKwI,CAAAA,MAAL,CAAY1C,KAAZ,CAAA,uDAAA,GAC0DhH,IAAI,CAACC,SAAL,CACtD9B,QADsD,CAD1D,GAAA,gNAAA,CAAA,CAAA;EAKD,OAAA;EACF,KAAA;;EAED,IAAA,OAAOuV,qBAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAOA,qBAAqB,CAAEpK,cAA9B,CAAA;EACD,GAAA;;EAEDsK,EAAAA,mBAAmB,CACjBtV,WADiB,EAEjBc,OAFiB,EAGX;MACN,MAAMqB,MAAM,GAAG,IAAK2Q,CAAAA,gBAAL,CAAsBrG,IAAtB,CACZ/N,CAAD,IAAO4C,YAAY,CAACtB,WAAD,CAAZ,KAA8BsB,YAAY,CAAC5C,CAAC,CAACsB,WAAH,CADpC,CAAf,CAAA;;EAGA,IAAA,IAAImC,MAAJ,EAAY;QACVA,MAAM,CAAC6I,cAAP,GAAwBlK,OAAxB,CAAA;EACD,KAFD,MAEO;QACL,IAAKgS,CAAAA,gBAAL,CAAsBjJ,IAAtB,CAA2B;UAAE7J,WAAF;EAAegL,QAAAA,cAAc,EAAElK,OAAAA;SAA1D,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAED6P,mBAAmB,CACjB3Q,WADiB,EAEwC;MACzD,IAAI,CAACA,WAAL,EAAkB;EAChB,MAAA,OAAOjC,SAAP,CAAA;EACD,KAHwD;;;EAMzD,IAAA,MAAMqX,qBAAqB,GAAG,IAAA,CAAKtC,gBAAL,CAAsBrG,IAAtB,CAA4B/N,CAAD,IACvDqC,eAAe,CAACf,WAAD,EAActB,CAAC,CAACsB,WAAhB,CADa,CAA9B,CANyD;;EAWzD,IAA2C;EACzC;EACA,MAAA,MAAMqV,gBAAgB,GAAG,IAAA,CAAKvC,gBAAL,CAAsBrU,MAAtB,CAA8BC,CAAD,IACpDqC,eAAe,CAACf,WAAD,EAActB,CAAC,CAACsB,WAAhB,CADQ,CAAzB,CAFyC;;EAMzC,MAAA,IAAIqV,gBAAgB,CAACzS,MAAjB,GAA0B,CAA9B,EAAiC;UAC/B,IAAKwI,CAAAA,MAAL,CAAY1C,KAAZ,CAAA,0DAAA,GAC6DhH,IAAI,CAACC,SAAL,CACzD3B,WADyD,CAD7D,GAAA,yNAAA,CAAA,CAAA;EAKD,OAAA;EACF,KAAA;;EAED,IAAA,OAAOoV,qBAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAOA,qBAAqB,CAAEpK,cAA9B,CAAA;EACD,GAAA;;IAEDsE,mBAAmB,CAOjBxO,OAPiB,EAsBjB;EACA,IAAA,IAAIA,OAAJ,IAAA,IAAA,IAAIA,OAAO,CAAEyU,UAAb,EAAyB;EACvB,MAAA,OAAOzU,OAAP,CAAA;EAOD,KAAA;;EAED,IAAA,MAAMkT,gBAAgB,GAAG,EACvB,GAAG,IAAKhJ,CAAAA,cAAL,CAAoBiE,OADA;QAEvB,GAAG,IAAA,CAAKM,gBAAL,CAAsBzO,OAAtB,oBAAsBA,OAAO,CAAEjB,QAA/B,CAFoB;EAGvB,MAAA,GAAGiB,OAHoB;EAIvByU,MAAAA,UAAU,EAAE,IAAA;OAJd,CAAA;;MAOA,IAAI,CAACvB,gBAAgB,CAACpT,SAAlB,IAA+BoT,gBAAgB,CAACnU,QAApD,EAA8D;QAC5DmU,gBAAgB,CAACpT,SAAjB,GAA6BC,qBAAqB,CAChDmT,gBAAgB,CAACnU,QAD+B,EAEhDmU,gBAFgD,CAAlD,CAAA;EAID,KAvBD;;;EA0BA,IAAA,IAAI,OAAOA,gBAAgB,CAACwB,kBAAxB,KAA+C,WAAnD,EAAgE;EAC9DxB,MAAAA,gBAAgB,CAACwB,kBAAjB,GACExB,gBAAgB,CAACtN,WAAjB,KAAiC,QADnC,CAAA;EAED,KAAA;;EACD,IAAA,IAAI,OAAOsN,gBAAgB,CAACyB,gBAAxB,KAA6C,WAAjD,EAA8D;EAC5DzB,MAAAA,gBAAgB,CAACyB,gBAAjB,GAAoC,CAAC,CAACzB,gBAAgB,CAAC0B,QAAvD,CAAA;EACD,KAAA;;EAED,IAAA,OAAO1B,gBAAP,CAAA;EAOD,GAAA;;IAEDtD,sBAAsB,CACpB5P,OADoB,EAEjB;EACH,IAAA,IAAIA,OAAJ,IAAA,IAAA,IAAIA,OAAO,CAAEyU,UAAb,EAAyB;EACvB,MAAA,OAAOzU,OAAP,CAAA;EACD,KAAA;;EACD,IAAA,OAAO,EACL,GAAG,IAAKkK,CAAAA,cAAL,CAAoByF,SADlB;QAEL,GAAG,IAAA,CAAKE,mBAAL,CAAyB7P,OAAzB,oBAAyBA,OAAO,CAAEd,WAAlC,CAFE;EAGL,MAAA,GAAGc,OAHE;EAILyU,MAAAA,UAAU,EAAE,IAAA;OAJd,CAAA;EAMD,GAAA;;EAED9F,EAAAA,KAAK,GAAS;MACZ,IAAKmD,CAAAA,UAAL,CAAgBnD,KAAhB,EAAA,CAAA;MACA,IAAKK,CAAAA,aAAL,CAAmBL,KAAnB,EAAA,CAAA;EACD,GAAA;;EAn4BsB;;EClBlB,MAAMkG,aAAN,SAMG7Y,YANH,CAMsD;EA8B3DC,EAAAA,WAAW,CACTqS,MADS,EAETtO,OAFS,EAST;EACA,IAAA,KAAA,EAAA,CAAA;MAEA,IAAKsO,CAAAA,MAAL,GAAcA,MAAd,CAAA;MACA,IAAKtO,CAAAA,OAAL,GAAeA,OAAf,CAAA;EACA,IAAA,IAAA,CAAK8U,YAAL,GAAoB,IAAI3Y,GAAJ,EAApB,CAAA;MACA,IAAK4Y,CAAAA,WAAL,GAAmB,IAAnB,CAAA;EACA,IAAA,IAAA,CAAKC,WAAL,EAAA,CAAA;MACA,IAAK7K,CAAAA,UAAL,CAAgBnK,OAAhB,CAAA,CAAA;EACD,GAAA;;EAESgV,EAAAA,WAAW,GAAS;MAC5B,IAAKtK,CAAAA,MAAL,GAAc,IAAKA,CAAAA,MAAL,CAAYrO,IAAZ,CAAiB,IAAjB,CAAd,CAAA;MACA,IAAKwP,CAAAA,OAAL,GAAe,IAAKA,CAAAA,OAAL,CAAaxP,IAAb,CAAkB,IAAlB,CAAf,CAAA;EACD,GAAA;;EAESI,EAAAA,WAAW,GAAS;EAC5B,IAAA,IAAI,KAAKP,SAAL,CAAeW,IAAf,KAAwB,CAA5B,EAA+B;EAC7B,MAAA,IAAA,CAAKoY,YAAL,CAAkBjJ,WAAlB,CAA8B,IAA9B,CAAA,CAAA;;QAEA,IAAIkJ,kBAAkB,CAAC,IAAKD,CAAAA,YAAN,EAAoB,IAAKjV,CAAAA,OAAzB,CAAtB,EAAyD;EACvD,QAAA,IAAA,CAAKmV,YAAL,EAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAA,CAAKC,YAAL,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAESzY,EAAAA,aAAa,GAAS;EAC9B,IAAA,IAAI,CAAC,IAAA,CAAKC,YAAL,EAAL,EAA0B;EACxB,MAAA,IAAA,CAAK2M,OAAL,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDwC,EAAAA,sBAAsB,GAAY;EAChC,IAAA,OAAOsJ,aAAa,CAClB,IAAKJ,CAAAA,YADa,EAElB,IAAA,CAAKjV,OAFa,EAGlB,IAAKA,CAAAA,OAAL,CAAa0U,kBAHK,CAApB,CAAA;EAKD,GAAA;;EAED9I,EAAAA,wBAAwB,GAAY;EAClC,IAAA,OAAOyJ,aAAa,CAClB,IAAKJ,CAAAA,YADa,EAElB,IAAA,CAAKjV,OAFa,EAGlB,IAAKA,CAAAA,OAAL,CAAasV,oBAHK,CAApB,CAAA;EAKD,GAAA;;EAED/L,EAAAA,OAAO,GAAS;EACd,IAAA,IAAA,CAAKrN,SAAL,GAAiB,IAAIC,GAAJ,EAAjB,CAAA;EACA,IAAA,IAAA,CAAKoZ,iBAAL,EAAA,CAAA;EACA,IAAA,IAAA,CAAKC,oBAAL,EAAA,CAAA;EACA,IAAA,IAAA,CAAKP,YAAL,CAAkB/I,cAAlB,CAAiC,IAAjC,CAAA,CAAA;EACD,GAAA;;EAED/B,EAAAA,UAAU,CACRnK,OADQ,EAQRyV,aARQ,EASF;MACN,MAAMC,WAAW,GAAG,IAAA,CAAK1V,OAAzB,CAAA;MACA,MAAM2V,SAAS,GAAG,IAAA,CAAKV,YAAvB,CAAA;MAEA,IAAKjV,CAAAA,OAAL,GAAe,IAAKsO,CAAAA,MAAL,CAAYE,mBAAZ,CAAgCxO,OAAhC,CAAf,CAAA;;EAEA,IAAA,IAEE,QAAOA,OAAP,oBAAOA,OAAO,CAAE6D,WAAhB,CAAA,KAAgC,WAFlC,EAGE;EACA,MAAA,IAAA,CAAKyK,MAAL,CACGC,SADH,EAAA,CAEG3G,KAFH,CAAA,wLAAA,CAAA,CAAA;EAKD,KAAA;;MAED,IAAI,CAACzF,mBAAmB,CAACuT,WAAD,EAAc,IAAK1V,CAAAA,OAAnB,CAAxB,EAAqD;EACnD,MAAA,IAAA,CAAKsO,MAAL,CAAYyE,aAAZ,EAAA,CAA4B9G,MAA5B,CAAmC;EACjCxM,QAAAA,IAAI,EAAE,wBAD2B;UAEjCD,KAAK,EAAE,KAAKyV,YAFqB;EAGjC7J,QAAAA,QAAQ,EAAE,IAAA;SAHZ,CAAA,CAAA;EAKD,KAAA;;EAED,IAAA,IACE,OAAO,IAAA,CAAKpL,OAAL,CAAaqL,OAApB,KAAgC,WAAhC,IACA,OAAO,KAAKrL,OAAL,CAAaqL,OAApB,KAAgC,SAFlC,EAGE;EACA,MAAA,MAAM,IAAItI,KAAJ,CAAU,kCAAV,CAAN,CAAA;EACD,KA9BK;;;EAiCN,IAAA,IAAI,CAAC,IAAA,CAAK/C,OAAL,CAAajB,QAAlB,EAA4B;EAC1B,MAAA,IAAA,CAAKiB,OAAL,CAAajB,QAAb,GAAwB2W,WAAW,CAAC3W,QAApC,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAK6W,WAAL,EAAA,CAAA;EAEA,IAAA,MAAMC,OAAO,GAAG,IAAA,CAAKjZ,YAAL,EAAhB,CAvCM;;EA0CN,IAAA,IACEiZ,OAAO,IACPC,qBAAqB,CACnB,KAAKb,YADc,EAEnBU,SAFmB,EAGnB,IAAK3V,CAAAA,OAHc,EAInB0V,WAJmB,CAFvB,EAQE;EACA,MAAA,IAAA,CAAKP,YAAL,EAAA,CAAA;EACD,KApDK;;;EAuDN,IAAA,IAAA,CAAKY,YAAL,CAAkBN,aAAlB,CAAA,CAvDM;;MA0DN,IACEI,OAAO,KACN,IAAA,CAAKZ,YAAL,KAAsBU,SAAtB,IACC,IAAA,CAAK3V,OAAL,CAAaqL,OAAb,KAAyBqK,WAAW,CAACrK,OADtC,IAEC,IAAA,CAAKrL,OAAL,CAAa3B,SAAb,KAA2BqX,WAAW,CAACrX,SAHlC,CADT,EAKE;EACA,MAAA,IAAA,CAAK2X,kBAAL,EAAA,CAAA;EACD,KAAA;;EAED,IAAA,MAAMC,mBAAmB,GAAG,IAAA,CAAKC,sBAAL,EAA5B,CAnEM;;MAsEN,IACEL,OAAO,KACN,IAAKZ,CAAAA,YAAL,KAAsBU,SAAtB,IACC,KAAK3V,OAAL,CAAaqL,OAAb,KAAyBqK,WAAW,CAACrK,OADtC,IAEC4K,mBAAmB,KAAK,IAAA,CAAKE,sBAHxB,CADT,EAKE;QACA,IAAKC,CAAAA,qBAAL,CAA2BH,mBAA3B,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAEDI,mBAAmB,CACjBrW,OADiB,EAQmB;EACpC,IAAA,MAAMR,KAAK,GAAG,IAAK8O,CAAAA,MAAL,CAAYyE,aAAZ,EAA4B1E,CAAAA,KAA5B,CAAkC,IAAA,CAAKC,MAAvC,EAA+CtO,OAA/C,CAAd,CAAA;MAEA,MAAMqB,MAAM,GAAG,IAAKiV,CAAAA,YAAL,CAAkB9W,KAAlB,EAAyBQ,OAAzB,CAAf,CAAA;;MAEA,IAAIuW,qCAAqC,CAAC,IAAD,EAAOlV,MAAP,EAAerB,OAAf,CAAzC,EAAkE;EAChE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;QACA,IAAKwW,CAAAA,aAAL,GAAqBnV,MAArB,CAAA;QACA,IAAKoV,CAAAA,oBAAL,GAA4B,IAAA,CAAKzW,OAAjC,CAAA;EACA,MAAA,IAAA,CAAK0W,kBAAL,GAA0B,IAAKzB,CAAAA,YAAL,CAAkB7U,KAA5C,CAAA;EACD,KAAA;;EACD,IAAA,OAAOiB,MAAP,CAAA;EACD,GAAA;;EAEDoK,EAAAA,gBAAgB,GAAuC;EACrD,IAAA,OAAO,KAAK+K,aAAZ,CAAA;EACD,GAAA;;IAEDG,WAAW,CACTtV,MADS,EAE2B;MACpC,MAAMuV,aAAa,GAAG,EAAtB,CAAA;MAEA3V,MAAM,CAACC,IAAP,CAAYG,MAAZ,EAAoBoD,OAApB,CAA6BnD,GAAD,IAAS;EACnCL,MAAAA,MAAM,CAAC0L,cAAP,CAAsBiK,aAAtB,EAAqCtV,GAArC,EAA0C;EACxCuV,QAAAA,YAAY,EAAE,KAD0B;EAExCjK,QAAAA,UAAU,EAAE,IAF4B;EAGxCC,QAAAA,GAAG,EAAE,MAAM;EACT,UAAA,IAAA,CAAKiI,YAAL,CAAkBtY,GAAlB,CAAsB8E,GAAtB,CAAA,CAAA;YACA,OAAOD,MAAM,CAACC,GAAD,CAAb,CAAA;EACD,SAAA;SANH,CAAA,CAAA;OADF,CAAA,CAAA;EAWA,IAAA,OAAOsV,aAAP,CAAA;EACD,GAAA;;EAEDE,EAAAA,eAAe,GAAuD;EACpE,IAAA,OAAO,KAAK7B,YAAZ,CAAA;EACD,GAAA;;EAEDvK,EAAAA,MAAM,GAAS;EACb,IAAA,IAAA,CAAK4D,MAAL,CAAYyE,aAAZ,GAA4BrI,MAA5B,CAAmC,KAAKuK,YAAxC,CAAA,CAAA;EACD,GAAA;;EAEDpJ,EAAAA,OAAO,CAAY;MACjBqE,WADiB;MAEjB,GAAGlQ,OAAAA;EAFc,GAAA,GAGiC,EAH7C,EAKL;EACA,IAAA,OAAO,IAAKoM,CAAAA,KAAL,CAAW,EAChB,GAAGpM,OADa;EAEhByK,MAAAA,IAAI,EAAE;EAAEyF,QAAAA,WAAAA;EAAF,OAAA;EAFU,KAAX,CAAP,CAAA;EAID,GAAA;;IAED6G,eAAe,CACb/W,OADa,EAQgC;MAC7C,MAAMkT,gBAAgB,GAAG,IAAK5E,CAAAA,MAAL,CAAYE,mBAAZ,CAAgCxO,OAAhC,CAAzB,CAAA;EAEA,IAAA,MAAMR,KAAK,GAAG,IAAK8O,CAAAA,MAAL,CACXyE,aADW,EAEX1E,CAAAA,KAFW,CAEL,IAAA,CAAKC,MAFA,EAEQ4E,gBAFR,CAAd,CAAA;MAGA1T,KAAK,CAAC8N,oBAAN,GAA6B,IAA7B,CAAA;EAEA,IAAA,OAAO9N,KAAK,CAAC4M,KAAN,EAAA,CAAc7I,IAAd,CAAmB,MAAM,IAAK+S,CAAAA,YAAL,CAAkB9W,KAAlB,EAAyB0T,gBAAzB,CAAzB,CAAP,CAAA;EACD,GAAA;;IAES9G,KAAK,CACbC,YADa,EAEgC;EAAA,IAAA,IAAA,qBAAA,CAAA;;EAC7C,IAAA,OAAO,IAAK8I,CAAAA,YAAL,CAAkB,EACvB,GAAG9I,YADoB;EAEvBP,MAAAA,aAAa,EAAEO,CAAAA,qBAAAA,GAAAA,YAAY,CAACP,aAAf,KAAgC,IAAA,GAAA,qBAAA,GAAA,IAAA;OAFxC,CAAA,CAGJvI,IAHI,CAGC,MAAM;EACZ,MAAA,IAAA,CAAKwS,YAAL,EAAA,CAAA;EACA,MAAA,OAAO,KAAKS,aAAZ,CAAA;EACD,KANM,CAAP,CAAA;EAOD,GAAA;;IAEOrB,YAAY,CAClB9I,YADkB,EAEe;EACjC;MACA,IAAKuJ,CAAAA,WAAL,GAFiC;;MAKjC,IAAIpP,OAAwC,GAAG,IAAA,CAAKyO,YAAL,CAAkB7I,KAAlB,CAC7C,IAAKpM,CAAAA,OADwC,EAE7CqM,YAF6C,CAA/C,CAAA;;EAKA,IAAA,IAAI,EAACA,YAAD,IAAA,IAAA,IAACA,YAAY,CAAEyH,YAAf,CAAJ,EAAiC;EAC/BtN,MAAAA,OAAO,GAAGA,OAAO,CAACqB,KAAR,CAAc7K,IAAd,CAAV,CAAA;EACD,KAAA;;EAED,IAAA,OAAOwJ,OAAP,CAAA;EACD,GAAA;;EAEOwP,EAAAA,kBAAkB,GAAS;EACjC,IAAA,IAAA,CAAKT,iBAAL,EAAA,CAAA;;EAEA,IAAA,IACEzY,QAAQ,IACR,IAAK0Z,CAAAA,aAAL,CAAmBrW,OADnB,IAEA,CAAC9C,cAAc,CAAC,IAAK2C,CAAAA,OAAL,CAAa3B,SAAd,CAHjB,EAIE;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,MAAM2Y,IAAI,GAAG7Y,cAAc,CACzB,KAAKqY,aAAL,CAAmB1L,aADM,EAEzB,KAAK9K,OAAL,CAAa3B,SAFY,CAA3B,CAXiC;EAiBjC;;EACA,IAAA,MAAM4E,OAAO,GAAG+T,IAAI,GAAG,CAAvB,CAAA;EAEA,IAAA,IAAA,CAAKC,cAAL,GAAsB7T,UAAU,CAAC,MAAM;EACrC,MAAA,IAAI,CAAC,IAAA,CAAKoT,aAAL,CAAmBrW,OAAxB,EAAiC;EAC/B,QAAA,IAAA,CAAK4V,YAAL,EAAA,CAAA;EACD,OAAA;OAH6B,EAI7B9S,OAJ6B,CAAhC,CAAA;EAKD,GAAA;;EAEOiT,EAAAA,sBAAsB,GAAG;EAAA,IAAA,IAAA,qBAAA,CAAA;;MAC/B,OAAO,OAAO,IAAKlW,CAAAA,OAAL,CAAakX,eAApB,KAAwC,UAAxC,GACH,IAAA,CAAKlX,OAAL,CAAakX,eAAb,CAA6B,IAAKV,CAAAA,aAAL,CAAmB5S,IAAhD,EAAsD,IAAA,CAAKqR,YAA3D,CADG,GAEH,CAAA,qBAAA,GAAA,IAAA,CAAKjV,OAAL,CAAakX,eAFV,KAAA,IAAA,GAAA,qBAAA,GAE6B,KAFpC,CAAA;EAGD,GAAA;;IAEOd,qBAAqB,CAACe,YAAD,EAAqC;EAChE,IAAA,IAAA,CAAK3B,oBAAL,EAAA,CAAA;MAEA,IAAKW,CAAAA,sBAAL,GAA8BgB,YAA9B,CAAA;;MAEA,IACEra,QAAQ,IACR,IAAKkD,CAAAA,OAAL,CAAaqL,OAAb,KAAyB,KADzB,IAEA,CAAChO,cAAc,CAAC,IAAA,CAAK8Y,sBAAN,CAFf,IAGA,KAAKA,sBAAL,KAAgC,CAJlC,EAKE;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKiB,iBAAL,GAAyBC,WAAW,CAAC,MAAM;QACzC,IACE,IAAA,CAAKrX,OAAL,CAAasX,2BAAb,IACAzS,YAAY,CAACH,SAAb,EAFF,EAGE;EACA,QAAA,IAAA,CAAKyQ,YAAL,EAAA,CAAA;EACD,OAAA;OANiC,EAOjC,IAAKgB,CAAAA,sBAP4B,CAApC,CAAA;EAQD,GAAA;;EAEOf,EAAAA,YAAY,GAAS;EAC3B,IAAA,IAAA,CAAKY,kBAAL,EAAA,CAAA;EACA,IAAA,IAAA,CAAKI,qBAAL,CAA2B,IAAKF,CAAAA,sBAAL,EAA3B,CAAA,CAAA;EACD,GAAA;;EAEOX,EAAAA,iBAAiB,GAAS;MAChC,IAAI,IAAA,CAAK0B,cAAT,EAAyB;QACvBlN,YAAY,CAAC,IAAKkN,CAAAA,cAAN,CAAZ,CAAA;QACA,IAAKA,CAAAA,cAAL,GAAsBha,SAAtB,CAAA;EACD,KAAA;EACF,GAAA;;EAEOuY,EAAAA,oBAAoB,GAAS;MACnC,IAAI,IAAA,CAAK4B,iBAAT,EAA4B;QAC1BG,aAAa,CAAC,IAAKH,CAAAA,iBAAN,CAAb,CAAA;QACA,IAAKA,CAAAA,iBAAL,GAAyBna,SAAzB,CAAA;EACD,KAAA;EACF,GAAA;;EAESqZ,EAAAA,YAAY,CACpB9W,KADoB,EAEpBQ,OAFoB,EASgB;MACpC,MAAM2V,SAAS,GAAG,IAAA,CAAKV,YAAvB,CAAA;MACA,MAAMS,WAAW,GAAG,IAAA,CAAK1V,OAAzB,CAAA;MACA,MAAMwX,UAAU,GAAG,IAAA,CAAKhB,aAAxB,CAAA;MAGA,MAAMiB,eAAe,GAAG,IAAA,CAAKf,kBAA7B,CAAA;MACA,MAAMgB,iBAAiB,GAAG,IAAA,CAAKjB,oBAA/B,CAAA;EACA,IAAA,MAAMkB,WAAW,GAAGnY,KAAK,KAAKmW,SAA9B,CAAA;MACA,MAAMiC,iBAAiB,GAAGD,WAAW,GACjCnY,KAAK,CAACY,KAD2B,GAEjC,IAAA,CAAKyX,wBAFT,CAAA;MAGA,MAAMC,eAAe,GAAGH,WAAW,GAC/B,KAAKnB,aAD0B,GAE/B,KAAKuB,mBAFT,CAAA;MAIA,MAAM;EAAE3X,MAAAA,KAAAA;EAAF,KAAA,GAAYZ,KAAlB,CAAA;MACA,IAAI;QAAEsL,aAAF;QAAiBlD,KAAjB;QAAwBiG,cAAxB;QAAwClO,WAAxC;EAAqDc,MAAAA,MAAAA;EAArD,KAAA,GAAgEL,KAApE,CAAA;MACA,IAAI4X,cAAc,GAAG,KAArB,CAAA;MACA,IAAIC,iBAAiB,GAAG,KAAxB,CAAA;MACA,IAAIrU,IAAJ,CApBoC;;MAuBpC,IAAI5D,OAAO,CAACkY,kBAAZ,EAAgC;EAC9B,MAAA,MAAMrC,OAAO,GAAG,IAAKjZ,CAAAA,YAAL,EAAhB,CAAA;QAEA,MAAMub,YAAY,GAAG,CAACtC,OAAD,IAAYX,kBAAkB,CAAC1V,KAAD,EAAQQ,OAAR,CAAnD,CAAA;EAEA,MAAA,MAAMoY,eAAe,GACnBvC,OAAO,IAAIC,qBAAqB,CAACtW,KAAD,EAAQmW,SAAR,EAAmB3V,OAAnB,EAA4B0V,WAA5B,CADlC,CAAA;;QAGA,IAAIyC,YAAY,IAAIC,eAApB,EAAqC;EACnCzY,QAAAA,WAAW,GAAGgG,QAAQ,CAACnG,KAAK,CAACQ,OAAN,CAAc4F,WAAf,CAAR,GACV,UADU,GAEV,QAFJ,CAAA;;UAGA,IAAI,CAACkF,aAAL,EAAoB;EAClBrK,UAAAA,MAAM,GAAG,SAAT,CAAA;EACD,SAAA;EACF,OAAA;;EACD,MAAA,IAAIT,OAAO,CAACkY,kBAAR,KAA+B,aAAnC,EAAkD;EAChDvY,QAAAA,WAAW,GAAG,MAAd,CAAA;EACD,OAAA;EACF,KA1CmC;;;EA6CpC,IAAA,IACEK,OAAO,CAACqY,gBAAR,IACA,CAACjY,KAAK,CAAC0K,aADP,IAEAgN,eAFA,IAAA,IAAA,IAEAA,eAAe,CAAEQ,SAFjB,IAGA7X,MAAM,KAAK,OAJb,EAKE;QACAmD,IAAI,GAAGkU,eAAe,CAAClU,IAAvB,CAAA;QACAkH,aAAa,GAAGgN,eAAe,CAAChN,aAAhC,CAAA;QACArK,MAAM,GAAGqX,eAAe,CAACrX,MAAzB,CAAA;EACAuX,MAAAA,cAAc,GAAG,IAAjB,CAAA;EACD,KAVD;WAYK,IAAIhY,OAAO,CAACuY,MAAR,IAAkB,OAAOnY,KAAK,CAACwD,IAAb,KAAsB,WAA5C,EAAyD;EAC5D;EACA,MAAA,IACE4T,UAAU,IACVpX,KAAK,CAACwD,IAAN,MAAe6T,eAAf,IAAeA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,eAAe,CAAE7T,IAAhC,CADA,IAEA5D,OAAO,CAACuY,MAAR,KAAmB,IAAA,CAAKC,QAH1B,EAIE;UACA5U,IAAI,GAAG,KAAK6U,YAAZ,CAAA;EACD,OAND,MAMO;UACL,IAAI;EACF,UAAA,IAAA,CAAKD,QAAL,GAAgBxY,OAAO,CAACuY,MAAxB,CAAA;YACA3U,IAAI,GAAG5D,OAAO,CAACuY,MAAR,CAAenY,KAAK,CAACwD,IAArB,CAAP,CAAA;EACAA,UAAAA,IAAI,GAAGF,WAAW,CAAC8T,UAAD,IAACA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,UAAU,CAAE5T,IAAb,EAAmBA,IAAnB,EAAyB5D,OAAzB,CAAlB,CAAA;YACA,IAAKyY,CAAAA,YAAL,GAAoB7U,IAApB,CAAA;YACA,IAAKmR,CAAAA,WAAL,GAAmB,IAAnB,CAAA;WALF,CAME,OAAOA,WAAP,EAAoB;EACpB,UAA2C;EACzC,YAAA,IAAA,CAAKzG,MAAL,CAAYC,SAAZ,EAAwB3G,CAAAA,KAAxB,CAA8BmN,WAA9B,CAAA,CAAA;EACD,WAAA;;YACD,IAAKA,CAAAA,WAAL,GAAmBA,WAAnB,CAAA;EACD,SAAA;EACF,OAAA;EACF,KAtBI;WAwBA;QACHnR,IAAI,GAAGxD,KAAK,CAACwD,IAAb,CAAA;EACD,KAnFmC;;;EAsFpC,IAAA,IACE,OAAO5D,OAAO,CAAC0Y,eAAf,KAAmC,WAAnC,IACA,OAAO9U,IAAP,KAAgB,WADhB,IAEAnD,MAAM,KAAK,SAHb,EAIE;QACA,IAAIiY,eAAJ,CADA;;EAIA,MAAA,IACElB,UAAU,IAAV,IAAA,IAAAA,UAAU,CAAES,iBAAZ,IACAjY,OAAO,CAAC0Y,eAAR,MAA4BhB,iBAA5B,IAAA,IAAA,GAAA,KAAA,CAAA,GAA4BA,iBAAiB,CAAEgB,eAA/C,CAFF,EAGE;UACAA,eAAe,GAAGlB,UAAU,CAAC5T,IAA7B,CAAA;EACD,OALD,MAKO;EACL8U,QAAAA,eAAe,GACb,OAAO1Y,OAAO,CAAC0Y,eAAf,KAAmC,UAAnC,GACK1Y,OAAO,CAAC0Y,eAAT,EADJ,GAEI1Y,OAAO,CAAC0Y,eAHd,CAAA;;UAIA,IAAI1Y,OAAO,CAACuY,MAAR,IAAkB,OAAOG,eAAP,KAA2B,WAAjD,EAA8D;YAC5D,IAAI;EACFA,YAAAA,eAAe,GAAG1Y,OAAO,CAACuY,MAAR,CAAeG,eAAf,CAAlB,CAAA;cACA,IAAK3D,CAAAA,WAAL,GAAmB,IAAnB,CAAA;aAFF,CAGE,OAAOA,WAAP,EAAoB;EACpB,YAA2C;EACzC,cAAA,IAAA,CAAKzG,MAAL,CAAYC,SAAZ,EAAwB3G,CAAAA,KAAxB,CAA8BmN,WAA9B,CAAA,CAAA;EACD,aAAA;;cACD,IAAKA,CAAAA,WAAL,GAAmBA,WAAnB,CAAA;EACD,WAAA;EACF,SAAA;EACF,OAAA;;EAED,MAAA,IAAI,OAAO2D,eAAP,KAA2B,WAA/B,EAA4C;EAC1CjY,QAAAA,MAAM,GAAG,SAAT,CAAA;EACAmD,QAAAA,IAAI,GAAGF,WAAW,CAAC8T,UAAD,IAACA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,UAAU,CAAE5T,IAAb,EAAmB8U,eAAnB,EAAoC1Y,OAApC,CAAlB,CAAA;EACAiY,QAAAA,iBAAiB,GAAG,IAApB,CAAA;EACD,OAAA;EACF,KAAA;;MAED,IAAI,IAAA,CAAKlD,WAAT,EAAsB;QACpBnN,KAAK,GAAG,KAAKmN,WAAb,CAAA;QACAnR,IAAI,GAAG,KAAK6U,YAAZ,CAAA;EACA5K,MAAAA,cAAc,GAAGrP,IAAI,CAACC,GAAL,EAAjB,CAAA;EACAgC,MAAAA,MAAM,GAAG,OAAT,CAAA;EACD,KAAA;;EAED,IAAA,MAAM6R,UAAU,GAAG3S,WAAW,KAAK,UAAnC,CAAA;EACA,IAAA,MAAMgZ,SAAS,GAAGlY,MAAM,KAAK,SAA7B,CAAA;EACA,IAAA,MAAMqC,OAAO,GAAGrC,MAAM,KAAK,OAA3B,CAAA;EAEA,IAAA,MAAMY,MAA8C,GAAG;QACrDZ,MADqD;QAErDd,WAFqD;QAGrDgZ,SAHqD;QAIrDL,SAAS,EAAE7X,MAAM,KAAK,SAJ+B;QAKrDqC,OALqD;QAMrD8V,gBAAgB,EAAED,SAAS,IAAIrG,UANsB;QAOrD1O,IAPqD;QAQrDkH,aARqD;QASrDlD,KATqD;QAUrDiG,cAVqD;QAWrDpI,YAAY,EAAErF,KAAK,CAACqN,iBAXiC;QAYrD8B,aAAa,EAAEnP,KAAK,CAACsN,kBAZgC;QAarDE,gBAAgB,EAAExN,KAAK,CAACwN,gBAb6B;QAcrDiL,SAAS,EAAEzY,KAAK,CAACuN,eAAN,GAAwB,CAAxB,IAA6BvN,KAAK,CAACwN,gBAAN,GAAyB,CAdZ;EAerDkL,MAAAA,mBAAmB,EACjB1Y,KAAK,CAACuN,eAAN,GAAwBiK,iBAAiB,CAACjK,eAA1C,IACAvN,KAAK,CAACwN,gBAAN,GAAyBgK,iBAAiB,CAAChK,gBAjBQ;QAkBrD0E,UAlBqD;EAmBrDyG,MAAAA,YAAY,EAAEzG,UAAU,IAAI,CAACqG,SAnBwB;EAoBrDK,MAAAA,cAAc,EAAElW,OAAO,IAAI1C,KAAK,CAAC0K,aAAN,KAAwB,CApBE;QAqBrD0E,QAAQ,EAAE7P,WAAW,KAAK,QArB2B;QAsBrDsY,iBAtBqD;QAuBrDD,cAvBqD;EAwBrDiB,MAAAA,cAAc,EAAEnW,OAAO,IAAI1C,KAAK,CAAC0K,aAAN,KAAwB,CAxBE;EAyBrD3K,MAAAA,OAAO,EAAEA,OAAO,CAACX,KAAD,EAAQQ,OAAR,CAzBqC;QA0BrD6L,OAAO,EAAE,KAAKA,OA1BuC;EA2BrDnB,MAAAA,MAAM,EAAE,IAAKA,CAAAA,MAAAA;OA3Bf,CAAA;EA8BA,IAAA,OAAOrJ,MAAP,CAAA;EACD,GAAA;;IAED0U,YAAY,CAACN,aAAD,EAAsC;MAChD,MAAM+B,UAAU,GAAG,IAAA,CAAKhB,aAAxB,CAAA;MAIA,MAAM0C,UAAU,GAAG,IAAA,CAAK5C,YAAL,CAAkB,KAAKrB,YAAvB,EAAqC,IAAKjV,CAAAA,OAA1C,CAAnB,CAAA;EACA,IAAA,IAAA,CAAK0W,kBAAL,GAA0B,IAAKzB,CAAAA,YAAL,CAAkB7U,KAA5C,CAAA;EACA,IAAA,IAAA,CAAKqW,oBAAL,GAA4B,IAAKzW,CAAAA,OAAjC,CAPgD;;EAUhD,IAAA,IAAImC,mBAAmB,CAAC+W,UAAD,EAAa1B,UAAb,CAAvB,EAAiD;EAC/C,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKhB,aAAL,GAAqB0C,UAArB,CAdgD;;EAiBhD,IAAA,MAAMC,oBAAmC,GAAG;EAAE9O,MAAAA,KAAK,EAAE,IAAA;OAArD,CAAA;;MAEA,MAAM+O,qBAAqB,GAAG,MAAe;QAC3C,IAAI,CAAC5B,UAAL,EAAiB;EACf,QAAA,OAAO,IAAP,CAAA;EACD,OAAA;;QAED,MAAM;EAAE6B,QAAAA,mBAAAA;EAAF,OAAA,GAA0B,KAAKrZ,OAArC,CAAA;QACA,MAAMsZ,wBAAwB,GAC5B,OAAOD,mBAAP,KAA+B,UAA/B,GACIA,mBAAmB,EADvB,GAEIA,mBAHN,CAAA;;EAKA,MAAA,IACEC,wBAAwB,KAAK,KAA7B,IACC,CAACA,wBAAD,IAA6B,CAAC,IAAKxE,CAAAA,YAAL,CAAkBjY,IAFnD,EAGE;EACA,QAAA,OAAO,IAAP,CAAA;EACD,OAAA;;QAED,MAAM0c,aAAa,GAAG,IAAIpd,GAAJ,CACpBmd,wBADoB,IAAA,IAAA,GACpBA,wBADoB,GACQ,IAAKxE,CAAAA,YADb,CAAtB,CAAA;;EAIA,MAAA,IAAI,IAAK9U,CAAAA,OAAL,CAAa2U,gBAAjB,EAAmC;UACjC4E,aAAa,CAAC/c,GAAd,CAAkB,OAAlB,CAAA,CAAA;EACD,OAAA;;QAED,OAAOyE,MAAM,CAACC,IAAP,CAAY,IAAA,CAAKsV,aAAjB,CAAgC9U,CAAAA,IAAhC,CAAsCJ,GAAD,IAAS;UACnD,MAAMkY,QAAQ,GAAGlY,GAAjB,CAAA;UACA,MAAMkD,OAAO,GAAG,IAAA,CAAKgS,aAAL,CAAmBgD,QAAnB,CAAiChC,KAAAA,UAAU,CAACgC,QAAD,CAA3D,CAAA;EACA,QAAA,OAAOhV,OAAO,IAAI+U,aAAa,CAACE,GAAd,CAAkBD,QAAlB,CAAlB,CAAA;EACD,OAJM,CAAP,CAAA;OA1BF,CAAA;;EAiCA,IAAA,IAAI,CAAA/D,aAAa,IAAb,IAAA,GAAA,KAAA,CAAA,GAAAA,aAAa,CAAEvZ,SAAf,MAA6B,KAA7B,IAAsCkd,qBAAqB,EAA/D,EAAmE;QACjED,oBAAoB,CAACjd,SAArB,GAAiC,IAAjC,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAK+P,MAAL,CAAY,EAAE,GAAGkN,oBAAL;QAA2B,GAAG1D,aAAAA;OAA1C,CAAA,CAAA;EACD,GAAA;;EAEOG,EAAAA,WAAW,GAAS;EAC1B,IAAA,MAAMpW,KAAK,GAAG,IAAK8O,CAAAA,MAAL,CAAYyE,aAAZ,EAAA,CAA4B1E,KAA5B,CAAkC,IAAKC,CAAAA,MAAvC,EAA+C,IAAA,CAAKtO,OAApD,CAAd,CAAA;;EAEA,IAAA,IAAIR,KAAK,KAAK,IAAKyV,CAAAA,YAAnB,EAAiC;EAC/B,MAAA,OAAA;EACD,KAAA;;MAED,MAAMU,SAAS,GAAG,IAAA,CAAKV,YAAvB,CAAA;MAGA,IAAKA,CAAAA,YAAL,GAAoBzV,KAApB,CAAA;EACA,IAAA,IAAA,CAAKqY,wBAAL,GAAgCrY,KAAK,CAACY,KAAtC,CAAA;MACA,IAAK2X,CAAAA,mBAAL,GAA2B,IAAA,CAAKvB,aAAhC,CAAA;;MAEA,IAAI,IAAA,CAAK5Z,YAAL,EAAJ,EAAyB;EACvB+Y,MAAAA,SAAS,QAAT,GAAAA,KAAAA,CAAAA,GAAAA,SAAS,CAAEzJ,cAAX,CAA0B,IAA1B,CAAA,CAAA;QACA1M,KAAK,CAACwM,WAAN,CAAkB,IAAlB,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAED8B,aAAa,CAACP,MAAD,EAAsC;MACjD,MAAMkI,aAA4B,GAAG,EAArC,CAAA;;EAEA,IAAA,IAAIlI,MAAM,CAAC9N,IAAP,KAAgB,SAApB,EAA+B;EAC7BgW,MAAAA,aAAa,CAACvO,SAAd,GAA0B,CAACqG,MAAM,CAACxC,MAAlC,CAAA;EACD,KAFD,MAEO,IAAIwC,MAAM,CAAC9N,IAAP,KAAgB,OAAhB,IAA2B,CAACuG,gBAAgB,CAACuH,MAAM,CAAC3F,KAAR,CAAhD,EAAgE;QACrE6N,aAAa,CAACtO,OAAd,GAAwB,IAAxB,CAAA;EACD,KAAA;;MAED,IAAK4O,CAAAA,YAAL,CAAkBN,aAAlB,CAAA,CAAA;;MAEA,IAAI,IAAA,CAAK7Y,YAAL,EAAJ,EAAyB;EACvB,MAAA,IAAA,CAAKwY,YAAL,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAEOnJ,MAAM,CAACwJ,aAAD,EAAqC;MACjDpM,aAAa,CAACT,KAAd,CAAoB,MAAM;EACxB;QACA,IAAI6M,aAAa,CAACvO,SAAlB,EAA6B;EAAA,QAAA,IAAA,qBAAA,EAAA,aAAA,EAAA,qBAAA,EAAA,cAAA,CAAA;;EAC3B,QAAA,CAAA,qBAAA,GAAA,CAAA,aAAA,GAAA,IAAA,CAAKlH,OAAL,EAAakH,SAAb,+DAAyB,IAAKsP,CAAAA,aAAL,CAAmB5S,IAA5C,CAAA,CAAA;UACA,CAAK5D,qBAAAA,GAAAA,CAAAA,cAAAA,GAAAA,IAAAA,CAAAA,OAAL,EAAaqN,SAAb,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,cAAA,EAAyB,KAAKmJ,aAAL,CAAmB5S,IAA5C,EAAmD,IAAnD,CAAA,CAAA;EACD,OAHD,MAGO,IAAI6R,aAAa,CAACtO,OAAlB,EAA2B;EAAA,QAAA,IAAA,qBAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,cAAA,CAAA;;EAChC,QAAA,CAAA,qBAAA,GAAA,CAAA,cAAA,GAAA,IAAA,CAAKnH,OAAL,EAAamH,OAAb,gEAAuB,IAAKqP,CAAAA,aAAL,CAAmB5O,KAA1C,CAAA,CAAA;UACA,CAAK5H,sBAAAA,GAAAA,CAAAA,cAAAA,GAAAA,IAAAA,CAAAA,OAAL,EAAaqN,SAAb,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,cAAA,EAAyBpQ,SAAzB,EAAoC,IAAA,CAAKuZ,aAAL,CAAmB5O,KAAvD,CAAA,CAAA;EACD,OARuB;;;QAWxB,IAAI6N,aAAa,CAACvZ,SAAlB,EAA6B;EAC3B,QAAA,IAAA,CAAKA,SAAL,CAAeuI,OAAf,CAAuB,CAAC;EAAEnI,UAAAA,QAAAA;EAAF,SAAD,KAAkB;YACvCA,QAAQ,CAAC,IAAKka,CAAAA,aAAN,CAAR,CAAA;WADF,CAAA,CAAA;EAGD,OAfuB;;;QAkBxB,IAAIf,aAAa,CAACpL,KAAlB,EAAyB;EACvB,QAAA,IAAA,CAAKiE,MAAL,CAAYyE,aAAZ,EAAA,CAA4B9G,MAA5B,CAAmC;YACjCzM,KAAK,EAAE,KAAKyV,YADqB;EAEjCxV,UAAAA,IAAI,EAAE,wBAAA;WAFR,CAAA,CAAA;EAID,OAAA;OAvBH,CAAA,CAAA;EAyBD,GAAA;;EAjrB0D,CAAA;;EAorB7D,SAASia,iBAAT,CACEla,KADF,EAEEQ,OAFF,EAGW;IACT,OACEA,OAAO,CAACqL,OAAR,KAAoB,KAApB,IACA,CAAC7L,KAAK,CAACY,KAAN,CAAY0K,aADb,IAEA,EAAEtL,KAAK,CAACY,KAAN,CAAYK,MAAZ,KAAuB,OAAvB,IAAkCT,OAAO,CAAC2Z,YAAR,KAAyB,KAA7D,CAHF,CAAA;EAKD,CAAA;;EAED,SAASzE,kBAAT,CACE1V,KADF,EAEEQ,OAFF,EAGW;IACT,OACE0Z,iBAAiB,CAACla,KAAD,EAAQQ,OAAR,CAAjB,IACCR,KAAK,CAACY,KAAN,CAAY0K,aAAZ,GAA4B,CAA5B,IACCuK,aAAa,CAAC7V,KAAD,EAAQQ,OAAR,EAAiBA,OAAO,CAAC4Z,cAAzB,CAHjB,CAAA;EAKD,CAAA;;EAED,SAASvE,aAAT,CACE7V,KADF,EAEEQ,OAFF,EAGE6Z,KAHF,EAME;EACA,EAAA,IAAI7Z,OAAO,CAACqL,OAAR,KAAoB,KAAxB,EAA+B;EAC7B,IAAA,MAAM/N,KAAK,GAAG,OAAOuc,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAACra,KAAD,CAAnC,GAA6Cqa,KAA3D,CAAA;EAEA,IAAA,OAAOvc,KAAK,KAAK,QAAV,IAAuBA,KAAK,KAAK,KAAV,IAAmB6C,OAAO,CAACX,KAAD,EAAQQ,OAAR,CAAxD,CAAA;EACD,GAAA;;EACD,EAAA,OAAO,KAAP,CAAA;EACD,CAAA;;EAED,SAAS8V,qBAAT,CACEtW,KADF,EAEEmW,SAFF,EAGE3V,OAHF,EAIE0V,WAJF,EAKW;EACT,EAAA,OACE1V,OAAO,CAACqL,OAAR,KAAoB,KAApB,KACC7L,KAAK,KAAKmW,SAAV,IAAuBD,WAAW,CAACrK,OAAZ,KAAwB,KADhD,CAEC,KAAA,CAACrL,OAAO,CAAC4U,QAAT,IAAqBpV,KAAK,CAACY,KAAN,CAAYK,MAAZ,KAAuB,OAF7C,KAGAN,OAAO,CAACX,KAAD,EAAQQ,OAAR,CAJT,CAAA;EAMD,CAAA;;EAED,SAASG,OAAT,CACEX,KADF,EAEEQ,OAFF,EAGW;EACT,EAAA,OAAOR,KAAK,CAACkM,aAAN,CAAoB1L,OAAO,CAAC3B,SAA5B,CAAP,CAAA;EACD;EAGD;;;EACA,SAASkY,qCAAT,CAOEnL,QAPF,EAQE0O,gBARF,EASE9Z,OATF,EAgBE;EACA;EACA;EACA;EACA;EACA;EACA;IACA,IAAIA,OAAO,CAACqY,gBAAZ,EAA8B;EAC5B,IAAA,OAAO,KAAP,CAAA;EACD,GATD;EAYA;;;EACA,EAAA,IAAIrY,OAAO,CAAC0Y,eAAR,KAA4Bzb,SAAhC,EAA2C;EACzC;EACA;EACA;MACA,OAAO6c,gBAAgB,CAAC7B,iBAAxB,CAAA;EACD,GAlBD;EAqBA;;;IACA,IAAI,CAAC9V,mBAAmB,CAACiJ,QAAQ,CAACK,gBAAT,EAAD,EAA8BqO,gBAA9B,CAAxB,EAAyE;EACvE,IAAA,OAAO,IAAP,CAAA;EACD,GAxBD;;;EA2BA,EAAA,OAAO,KAAP,CAAA;EACD;;EC5zBM,MAAMC,eAAN,SAA8B/d,YAA9B,CAAoE;EAOzEC,EAAAA,WAAW,CAACqS,MAAD,EAAsBH,OAAtB,EAAwD;EACjE,IAAA,KAAA,EAAA,CAAA;MAEA,IAAKG,CAAAA,MAAL,GAAcA,MAAd,CAAA;MACA,IAAKH,CAAAA,OAAL,GAAe,EAAf,CAAA;MACA,IAAK9M,CAAAA,MAAL,GAAc,EAAd,CAAA;MACA,IAAK+I,CAAAA,SAAL,GAAiB,EAAjB,CAAA;MACA,IAAK4P,CAAAA,YAAL,GAAoB,EAApB,CAAA;;EAEA,IAAA,IAAI7L,OAAJ,EAAa;QACX,IAAK8L,CAAAA,UAAL,CAAgB9L,OAAhB,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAES1R,EAAAA,WAAW,GAAS;EAC5B,IAAA,IAAI,KAAKP,SAAL,CAAeW,IAAf,KAAwB,CAA5B,EAA+B;EAC7B,MAAA,IAAA,CAAKuN,SAAL,CAAe3F,OAAf,CAAwB2G,QAAD,IAAc;EACnCA,QAAAA,QAAQ,CAAChP,SAAT,CAAoBiF,MAAD,IAAY;EAC7B,UAAA,IAAA,CAAK6Y,QAAL,CAAc9O,QAAd,EAAwB/J,MAAxB,CAAA,CAAA;WADF,CAAA,CAAA;SADF,CAAA,CAAA;EAKD,KAAA;EACF,GAAA;;EAES1E,EAAAA,aAAa,GAAS;EAC9B,IAAA,IAAI,CAAC,IAAA,CAAKT,SAAL,CAAeW,IAApB,EAA0B;EACxB,MAAA,IAAA,CAAK0M,OAAL,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEDA,EAAAA,OAAO,GAAS;EACd,IAAA,IAAA,CAAKrN,SAAL,GAAiB,IAAIC,GAAJ,EAAjB,CAAA;EACA,IAAA,IAAA,CAAKiO,SAAL,CAAe3F,OAAf,CAAwB2G,QAAD,IAAc;EACnCA,MAAAA,QAAQ,CAAC7B,OAAT,EAAA,CAAA;OADF,CAAA,CAAA;EAGD,GAAA;;EAED0Q,EAAAA,UAAU,CACR9L,OADQ,EAERsH,aAFQ,EAGF;MACN,IAAKtH,CAAAA,OAAL,GAAeA,OAAf,CAAA;MAEA9E,aAAa,CAACT,KAAd,CAAoB,MAAM;QACxB,MAAMuR,aAAa,GAAG,IAAA,CAAK/P,SAA3B,CAAA;QAEA,MAAMgQ,kBAAkB,GAAG,IAAKC,CAAAA,qBAAL,CAA2B,IAAKlM,CAAAA,OAAhC,CAA3B,CAHwB;;EAMxBiM,MAAAA,kBAAkB,CAAC3V,OAAnB,CAA4B6V,KAAD,IACzBA,KAAK,CAAClP,QAAN,CAAejB,UAAf,CAA0BmQ,KAAK,CAACC,qBAAhC,EAAuD9E,aAAvD,CADF,CAAA,CAAA;QAIA,MAAM+E,YAAY,GAAGJ,kBAAkB,CAACpH,GAAnB,CAAwBsH,KAAD,IAAWA,KAAK,CAAClP,QAAxC,CAArB,CAAA;QACA,MAAMqP,eAAe,GAAGxZ,MAAM,CAACyZ,WAAP,CACtBF,YAAY,CAACxH,GAAb,CAAkB5H,QAAD,IAAc,CAACA,QAAQ,CAACpL,OAAT,CAAiBF,SAAlB,EAA6BsL,QAA7B,CAA/B,CADsB,CAAxB,CAAA;EAGA,MAAA,MAAMuP,SAAS,GAAGH,YAAY,CAACxH,GAAb,CAAkB5H,QAAD,IACjCA,QAAQ,CAACK,gBAAT,EADgB,CAAlB,CAAA;EAIA,MAAA,MAAMmP,cAAc,GAAGJ,YAAY,CAAC9Y,IAAb,CACrB,CAAC0J,QAAD,EAAWpN,KAAX,KAAqBoN,QAAQ,KAAK+O,aAAa,CAACnc,KAAD,CAD1B,CAAvB,CAAA;;QAGA,IAAImc,aAAa,CAACrY,MAAd,KAAyB0Y,YAAY,CAAC1Y,MAAtC,IAAgD,CAAC8Y,cAArD,EAAqE;EACnE,QAAA,OAAA;EACD,OAAA;;QAED,IAAKxQ,CAAAA,SAAL,GAAiBoQ,YAAjB,CAAA;QACA,IAAKR,CAAAA,YAAL,GAAoBS,eAApB,CAAA;QACA,IAAKpZ,CAAAA,MAAL,GAAcsZ,SAAd,CAAA;;EAEA,MAAA,IAAI,CAAC,IAAA,CAAK/d,YAAL,EAAL,EAA0B;EACxB,QAAA,OAAA;EACD,OAAA;;QAEDY,UAAU,CAAC2c,aAAD,EAAgBK,YAAhB,CAAV,CAAwC/V,OAAxC,CAAiD2G,QAAD,IAAc;EAC5DA,QAAAA,QAAQ,CAAC7B,OAAT,EAAA,CAAA;SADF,CAAA,CAAA;QAIA/L,UAAU,CAACgd,YAAD,EAAeL,aAAf,CAAV,CAAwC1V,OAAxC,CAAiD2G,QAAD,IAAc;EAC5DA,QAAAA,QAAQ,CAAChP,SAAT,CAAoBiF,MAAD,IAAY;EAC7B,UAAA,IAAA,CAAK6Y,QAAL,CAAc9O,QAAd,EAAwB/J,MAAxB,CAAA,CAAA;WADF,CAAA,CAAA;SADF,CAAA,CAAA;EAMA,MAAA,IAAA,CAAK4K,MAAL,EAAA,CAAA;OA3CF,CAAA,CAAA;EA6CD,GAAA;;EAEDR,EAAAA,gBAAgB,GAA0B;EACxC,IAAA,OAAO,KAAKpK,MAAZ,CAAA;EACD,GAAA;;EAEDwZ,EAAAA,UAAU,GAAG;MACX,OAAO,IAAA,CAAKzQ,SAAL,CAAe4I,GAAf,CAAoB5H,QAAD,IAAcA,QAAQ,CAAC0L,eAAT,EAAjC,CAAP,CAAA;EACD,GAAA;;EAEDgE,EAAAA,YAAY,GAAG;EACb,IAAA,OAAO,KAAK1Q,SAAZ,CAAA;EACD,GAAA;;IAEDiM,mBAAmB,CAAClI,OAAD,EAAyD;EAC1E,IAAA,OAAO,KAAKkM,qBAAL,CAA2BlM,OAA3B,CAAoC6E,CAAAA,GAApC,CAAyCsH,KAAD,IAC7CA,KAAK,CAAClP,QAAN,CAAeiL,mBAAf,CAAmCiE,KAAK,CAACC,qBAAzC,CADK,CAAP,CAAA;EAGD,GAAA;;IAEOF,qBAAqB,CAC3BlM,OAD2B,EAEL;MACtB,MAAMgM,aAAa,GAAG,IAAA,CAAK/P,SAA3B,CAAA;MACA,MAAM2Q,gBAAgB,GAAG,IAAIC,GAAJ,CACvBb,aAAa,CAACnH,GAAd,CAAmB5H,QAAD,IAAc,CAACA,QAAQ,CAACpL,OAAT,CAAiBF,SAAlB,EAA6BsL,QAA7B,CAAhC,CADuB,CAAzB,CAAA;EAIA,IAAA,MAAMmP,qBAAqB,GAAGpM,OAAO,CAAC6E,GAAR,CAAahT,OAAD,IACxC,IAAA,CAAKsO,MAAL,CAAYE,mBAAZ,CAAgCxO,OAAhC,CAD4B,CAA9B,CAAA;EAIA,IAAA,MAAMib,iBAAuC,GAC3CV,qBAAqB,CAACW,OAAtB,CAA+BhI,gBAAD,IAAsB;QAClD,MAAMoH,KAAK,GAAGS,gBAAgB,CAAClO,GAAjB,CAAqBqG,gBAAgB,CAACpT,SAAtC,CAAd,CAAA;;QACA,IAAIwa,KAAK,IAAI,IAAb,EAAmB;EACjB,QAAA,OAAO,CAAC;EAAEC,UAAAA,qBAAqB,EAAErH,gBAAzB;EAA2C9H,UAAAA,QAAQ,EAAEkP,KAAAA;EAArD,SAAD,CAAP,CAAA;EACD,OAAA;;EACD,MAAA,OAAO,EAAP,CAAA;EACD,KAND,CADF,CAAA;EASA,IAAA,MAAMa,kBAAkB,GAAG,IAAIhf,GAAJ,CACzB8e,iBAAiB,CAACjI,GAAlB,CAAuBsH,KAAD,IAAWA,KAAK,CAACC,qBAAN,CAA4Bza,SAA7D,CADyB,CAA3B,CAAA;EAGA,IAAA,MAAMsb,gBAAgB,GAAGb,qBAAqB,CAAC5c,MAAtB,CACtBuV,gBAAD,IAAsB,CAACiI,kBAAkB,CAAC1B,GAAnB,CAAuBvG,gBAAgB,CAACpT,SAAxC,CADA,CAAzB,CAAA;EAIA,IAAA,MAAMub,oBAAoB,GAAG,IAAIlf,GAAJ,CAC3B8e,iBAAiB,CAACjI,GAAlB,CAAuBsH,KAAD,IAAWA,KAAK,CAAClP,QAAvC,CAD2B,CAA7B,CAAA;EAGA,IAAA,MAAMkQ,kBAAkB,GAAGnB,aAAa,CAACxc,MAAd,CACxB4d,YAAD,IAAkB,CAACF,oBAAoB,CAAC5B,GAArB,CAAyB8B,YAAzB,CADM,CAA3B,CAAA;;MAIA,MAAMC,WAAW,GAAIxb,OAAD,IAAkD;QACpE,MAAMkT,gBAAgB,GAAG,IAAK5E,CAAAA,MAAL,CAAYE,mBAAZ,CAAgCxO,OAAhC,CAAzB,CAAA;QACA,MAAMyb,eAAe,GAAG,IAAKzB,CAAAA,YAAL,CAAkB9G,gBAAgB,CAACpT,SAAnC,CAAxB,CAAA;QACA,OAAO2b,eAAP,IAAOA,IAAAA,GAAAA,eAAP,GAA0B,IAAI5G,aAAJ,CAAkB,IAAKvG,CAAAA,MAAvB,EAA+B4E,gBAA/B,CAA1B,CAAA;OAHF,CAAA;;MAMA,MAAMwI,oBAA0C,GAAGN,gBAAgB,CAACpI,GAAjB,CACjD,CAAChT,OAAD,EAAUhC,KAAV,KAAoB;QAClB,IAAIgC,OAAO,CAACqY,gBAAZ,EAA8B;EAC5B;EACA,QAAA,MAAMsD,sBAAsB,GAAGL,kBAAkB,CAACtd,KAAD,CAAjD,CAAA;;UACA,IAAI2d,sBAAsB,KAAK1e,SAA/B,EAA0C;YACxC,OAAO;EACLsd,YAAAA,qBAAqB,EAAEva,OADlB;EAELoL,YAAAA,QAAQ,EAAEuQ,sBAAAA;aAFZ,CAAA;EAID,SAAA;EACF,OAAA;;QACD,OAAO;EACLpB,QAAAA,qBAAqB,EAAEva,OADlB;UAELoL,QAAQ,EAAEoQ,WAAW,CAACxb,OAAD,CAAA;SAFvB,CAAA;EAID,KAhBgD,CAAnD,CAAA;;MAmBA,MAAM4b,2BAA2B,GAAG,CAClCra,CADkC,EAElCC,CAFkC,KAIlC+Y,qBAAqB,CAACsB,OAAtB,CAA8Bta,CAAC,CAACgZ,qBAAhC,IACAA,qBAAqB,CAACsB,OAAtB,CAA8Bra,CAAC,CAAC+Y,qBAAhC,CALF,CAAA;;MAOA,OAAOU,iBAAiB,CACrBa,MADI,CACGJ,oBADH,CAEJva,CAAAA,IAFI,CAECya,2BAFD,CAAP,CAAA;EAGD,GAAA;;EAEO1B,EAAAA,QAAQ,CAAC9O,QAAD,EAA0B/J,MAA1B,EAA6D;MAC3E,MAAMrD,KAAK,GAAG,IAAKoM,CAAAA,SAAL,CAAeyR,OAAf,CAAuBzQ,QAAvB,CAAd,CAAA;;EACA,IAAA,IAAIpN,KAAK,KAAK,CAAC,CAAf,EAAkB;QAChB,IAAKqD,CAAAA,MAAL,GAAcvD,SAAS,CAAC,IAAA,CAAKuD,MAAN,EAAcrD,KAAd,EAAqBqD,MAArB,CAAvB,CAAA;EACA,MAAA,IAAA,CAAK4K,MAAL,EAAA,CAAA;EACD,KAAA;EACF,GAAA;;EAEOA,EAAAA,MAAM,GAAS;MACrB5C,aAAa,CAACT,KAAd,CAAoB,MAAM;EACxB,MAAA,IAAA,CAAK1M,SAAL,CAAeuI,OAAf,CAAuB,CAAC;EAAEnI,QAAAA,QAAAA;EAAF,OAAD,KAAkB;UACvCA,QAAQ,CAAC,IAAK+E,CAAAA,MAAN,CAAR,CAAA;SADF,CAAA,CAAA;OADF,CAAA,CAAA;EAKD,GAAA;;EAzMwE;;ECSpE,MAAM0a,qBAAN,SAMGlH,aANH,CAYL;EACA;EAKA;EAGA;EAKA;EACA5Y,EAAAA,WAAW,CACTqS,MADS,EAETtO,OAFS,EAST;MACA,KAAMsO,CAAAA,MAAN,EAActO,OAAd,CAAA,CAAA;EACD,GAAA;;EAESgV,EAAAA,WAAW,GAAS;EAC5B,IAAA,KAAA,CAAMA,WAAN,EAAA,CAAA;MACA,IAAKgH,CAAAA,aAAL,GAAqB,IAAKA,CAAAA,aAAL,CAAmB3f,IAAnB,CAAwB,IAAxB,CAArB,CAAA;MACA,IAAK4f,CAAAA,iBAAL,GAAyB,IAAKA,CAAAA,iBAAL,CAAuB5f,IAAvB,CAA4B,IAA5B,CAAzB,CAAA;EACD,GAAA;;EAED8N,EAAAA,UAAU,CACRnK,OADQ,EAQRyV,aARQ,EASF;EACN,IAAA,KAAA,CAAMtL,UAAN,CACE,EACE,GAAGnK,OADL;EAEEiN,MAAAA,QAAQ,EAAEgD,qBAAqB,EAAA;EAFjC,KADF,EAKEwF,aALF,CAAA,CAAA;EAOD,GAAA;;IAEDY,mBAAmB,CACjBrW,OADiB,EAQ2B;EAC5CA,IAAAA,OAAO,CAACiN,QAAR,GAAmBgD,qBAAqB,EAAxC,CAAA;EACA,IAAA,OAAO,KAAMoG,CAAAA,mBAAN,CAA0BrW,OAA1B,CAAP,CAAA;EAID,GAAA;;EAEDgc,EAAAA,aAAa,CAAC;MAAExP,SAAF;MAAa,GAAGxM,OAAAA;EAAhB,GAAA,GAAkD,EAAnD,EAEX;EACA,IAAA,OAAO,IAAKoM,CAAAA,KAAL,CAAW,EAChB,GAAGpM,OADa;EAEhByK,MAAAA,IAAI,EAAE;EACJ0F,QAAAA,SAAS,EAAE;EAAEE,UAAAA,SAAS,EAAE,SAAb;EAAwB7D,UAAAA,SAAAA;EAAxB,SAAA;EADP,OAAA;EAFU,KAAX,CAAP,CAAA;EAMD,GAAA;;EAEDyP,EAAAA,iBAAiB,CAAC;MAChBzP,SADgB;MAEhB,GAAGxM,OAAAA;EAFa,GAAA,GAGY,EAHb,EAKf;EACA,IAAA,OAAO,IAAKoM,CAAAA,KAAL,CAAW,EAChB,GAAGpM,OADa;EAEhByK,MAAAA,IAAI,EAAE;EACJ0F,QAAAA,SAAS,EAAE;EAAEE,UAAAA,SAAS,EAAE,UAAb;EAAyB7D,UAAAA,SAAAA;EAAzB,SAAA;EADP,OAAA;EAFU,KAAX,CAAP,CAAA;EAMD,GAAA;;EAES8J,EAAAA,YAAY,CACpB9W,KADoB,EAEpBQ,OAFoB,EASwB;EAAA,IAAA,IAAA,gBAAA,EAAA,qBAAA,EAAA,iBAAA,EAAA,qBAAA,EAAA,WAAA,EAAA,YAAA,CAAA;;MAC5C,MAAM;EAAEI,MAAAA,KAAAA;EAAF,KAAA,GAAYZ,KAAlB,CAAA;MACA,MAAM6B,MAAM,GAAG,KAAMiV,CAAAA,YAAN,CAAmB9W,KAAnB,EAA0BQ,OAA1B,CAAf,CAAA;MAEA,MAAM;QAAEsS,UAAF;EAAcyG,MAAAA,YAAAA;EAAd,KAAA,GAA+B1X,MAArC,CAAA;EAEA,IAAA,MAAM+O,kBAAkB,GACtBkC,UAAU,IAAI,CAAAlS,CAAAA,gBAAAA,GAAAA,KAAK,CAACgN,SAAN,KAAiB+C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,gBAAAA,CAAAA,SAAjB,KAA4BE,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAAA,CAAAA,SAA5B,MAA0C,SAD1D,CAAA;EAGA,IAAA,MAAMC,sBAAsB,GAC1BgC,UAAU,IAAI,CAAAlS,CAAAA,iBAAAA,GAAAA,KAAK,CAACgN,SAAN,KAAiB+C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,iBAAAA,CAAAA,SAAjB,KAA4BE,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,qBAAAA,CAAAA,SAA5B,MAA0C,UAD1D,CAAA;MAGA,OAAO,EACL,GAAGhP,MADE;QAEL2a,aAAa,EAAE,KAAKA,aAFf;QAGLC,iBAAiB,EAAE,KAAKA,iBAHnB;QAILxK,WAAW,EAAEA,WAAW,CAACzR,OAAD,EAAA,CAAA,WAAA,GAAUI,KAAK,CAACwD,IAAhB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAU,WAAY4M,CAAAA,KAAtB,CAJnB;QAKLmB,eAAe,EAAEA,eAAe,CAAC3R,OAAD,EAAA,CAAA,YAAA,GAAUI,KAAK,CAACwD,IAAhB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAU,YAAY4M,CAAAA,KAAtB,CAL3B;QAMLJ,kBANK;QAOLE,sBAPK;EAQLyI,MAAAA,YAAY,EACVA,YAAY,IAAI,CAAC3I,kBAAjB,IAAuC,CAACE,sBAAAA;OAT5C,CAAA;EAWD,GAAA;;EA9HD;;ECVF;EAEO,MAAM4L,gBAAN,SAKGlgB,YALH,CAOL;EAaAC,EAAAA,WAAW,CACTqS,MADS,EAETtO,OAFS,EAGT;EACA,IAAA,KAAA,EAAA,CAAA;MAEA,IAAKsO,CAAAA,MAAL,GAAcA,MAAd,CAAA;MACA,IAAKnE,CAAAA,UAAL,CAAgBnK,OAAhB,CAAA,CAAA;EACA,IAAA,IAAA,CAAKgV,WAAL,EAAA,CAAA;EACA,IAAA,IAAA,CAAKe,YAAL,EAAA,CAAA;EACD,GAAA;;EAESf,EAAAA,WAAW,GAAS;MAC5B,IAAKmH,CAAAA,MAAL,GAAc,IAAKA,CAAAA,MAAL,CAAY9f,IAAZ,CAAiB,IAAjB,CAAd,CAAA;MACA,IAAK8O,CAAAA,KAAL,GAAa,IAAKA,CAAAA,KAAL,CAAW9O,IAAX,CAAgB,IAAhB,CAAb,CAAA;EACD,GAAA;;IAED8N,UAAU,CACRnK,OADQ,EAER;EAAA,IAAA,IAAA,qBAAA,CAAA;;MACA,MAAM0V,WAAW,GAAG,IAAA,CAAK1V,OAAzB,CAAA;MACA,IAAKA,CAAAA,OAAL,GAAe,IAAKsO,CAAAA,MAAL,CAAYsB,sBAAZ,CAAmC5P,OAAnC,CAAf,CAAA;;MACA,IAAI,CAACmC,mBAAmB,CAACuT,WAAD,EAAc,IAAK1V,CAAAA,OAAnB,CAAxB,EAAqD;EACnD,MAAA,IAAA,CAAKsO,MAAL,CAAY4F,gBAAZ,EAAA,CAA+BjI,MAA/B,CAAsC;EACpCxM,QAAAA,IAAI,EAAE,wBAD8B;UAEpCa,QAAQ,EAAE,KAAK8b,eAFqB;EAGpChR,QAAAA,QAAQ,EAAE,IAAA;SAHZ,CAAA,CAAA;EAKD,KAAA;;EACD,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAKgR,eAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAsBjS,UAAtB,CAAiC,KAAKnK,OAAtC,CAAA,CAAA;EACD,GAAA;;EAESrD,EAAAA,aAAa,GAAS;EAC9B,IAAA,IAAI,CAAC,IAAA,CAAKC,YAAL,EAAL,EAA0B;EAAA,MAAA,IAAA,sBAAA,CAAA;;EACxB,MAAA,CAAA,sBAAA,GAAA,IAAA,CAAKwf,eAAL,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAsBlQ,cAAtB,CAAqC,IAArC,CAAA,CAAA;EACD,KAAA;EACF,GAAA;;IAEDuD,gBAAgB,CAAClC,MAAD,EAA4D;MAC1E,IAAKwI,CAAAA,YAAL,GAD0E;;EAI1E,IAAA,MAAMN,aAA4B,GAAG;EACnCvZ,MAAAA,SAAS,EAAE,IAAA;OADb,CAAA;;EAIA,IAAA,IAAIqR,MAAM,CAAC9N,IAAP,KAAgB,SAApB,EAA+B;QAC7BgW,aAAa,CAACvO,SAAd,GAA0B,IAA1B,CAAA;EACD,KAFD,MAEO,IAAIqG,MAAM,CAAC9N,IAAP,KAAgB,OAApB,EAA6B;QAClCgW,aAAa,CAACtO,OAAd,GAAwB,IAAxB,CAAA;EACD,KAAA;;MAED,IAAK8E,CAAAA,MAAL,CAAYwJ,aAAZ,CAAA,CAAA;EACD,GAAA;;EAEDhK,EAAAA,gBAAgB,GAKd;EACA,IAAA,OAAO,KAAK+K,aAAZ,CAAA;EACD,GAAA;;EAEDrL,EAAAA,KAAK,GAAS;MACZ,IAAKiR,CAAAA,eAAL,GAAuBnf,SAAvB,CAAA;EACA,IAAA,IAAA,CAAK8Y,YAAL,EAAA,CAAA;EACA,IAAA,IAAA,CAAK9J,MAAL,CAAY;EAAE/P,MAAAA,SAAS,EAAE,IAAA;OAAzB,CAAA,CAAA;EACD,GAAA;;EAEDigB,EAAAA,MAAM,CACJhN,SADI,EAEJnP,OAFI,EAGY;MAChB,IAAKqc,CAAAA,aAAL,GAAqBrc,OAArB,CAAA;;MAEA,IAAI,IAAA,CAAKoc,eAAT,EAA0B;EACxB,MAAA,IAAA,CAAKA,eAAL,CAAqBlQ,cAArB,CAAoC,IAApC,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKkQ,eAAL,GAAuB,IAAK9N,CAAAA,MAAL,CAAY4F,gBAAZ,EAAA,CAA+B7F,KAA/B,CAAqC,KAAKC,MAA1C,EAAkD,EACvE,GAAG,KAAKtO,OAD+D;QAEvEmP,SAAS,EACP,OAAOA,SAAP,KAAqB,WAArB,GAAmCA,SAAnC,GAA+C,IAAKnP,CAAAA,OAAL,CAAamP,SAAAA;EAHS,KAAlD,CAAvB,CAAA;EAMA,IAAA,IAAA,CAAKiN,eAAL,CAAqBpQ,WAArB,CAAiC,IAAjC,CAAA,CAAA;EAEA,IAAA,OAAO,IAAKoQ,CAAAA,eAAL,CAAqBnN,OAArB,EAAP,CAAA;EACD,GAAA;;EAEO8G,EAAAA,YAAY,GAAS;MAC3B,MAAM3V,KAAK,GAAG,IAAA,CAAKgc,eAAL,GACV,IAAKA,CAAAA,eAAL,CAAqBhc,KADX,GAEVoK,eAAe,EAFnB,CAAA;EAIA,IAAA,MAAMmO,SAAS,GAAGvY,KAAK,CAACK,MAAN,KAAiB,SAAnC,CAAA;EACA,IAAA,MAAMY,MAKL,GAAG,EACF,GAAGjB,KADD;QAEFuY,SAFE;EAGF2D,MAAAA,SAAS,EAAE3D,SAHT;EAIFL,MAAAA,SAAS,EAAElY,KAAK,CAACK,MAAN,KAAiB,SAJ1B;EAKFqC,MAAAA,OAAO,EAAE1C,KAAK,CAACK,MAAN,KAAiB,OALxB;EAMF8b,MAAAA,MAAM,EAAEnc,KAAK,CAACK,MAAN,KAAiB,MANvB;QAOF0b,MAAM,EAAE,KAAKA,MAPX;EAQFhR,MAAAA,KAAK,EAAE,IAAKA,CAAAA,KAAAA;OAbd,CAAA;MAgBA,IAAKqL,CAAAA,aAAL,GAAqBnV,MAArB,CAAA;EAMD,GAAA;;IAEO4K,MAAM,CAACjM,OAAD,EAAyB;MACrCqJ,aAAa,CAACT,KAAd,CAAoB,MAAM;EACxB;EACA,MAAA,IAAI,KAAKyT,aAAL,IAAsB,IAAKzf,CAAAA,YAAL,EAA1B,EAA+C;UAC7C,IAAIoD,OAAO,CAACkH,SAAZ,EAAuB;EAAA,UAAA,IAAA,qBAAA,EAAA,mBAAA,EAAA,sBAAA,EAAA,oBAAA,CAAA;;EACrB,UAAA,CAAA,qBAAA,GAAA,CAAA,mBAAA,GAAA,IAAA,CAAKmV,aAAL,EAAmBnV,SAAnB,KACE,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAA,IAAA,CAAA,mBAAA,EAAA,IAAA,CAAKsP,aAAL,CAAmB5S,IADrB,EAEE,IAAA,CAAK4S,aAAL,CAAmBrH,SAFrB,EAGE,IAAKqH,CAAAA,aAAL,CAAmBxJ,OAHrB,CAAA,CAAA;EAKA,UAAA,CAAA,sBAAA,GAAA,CAAA,oBAAA,GAAA,IAAA,CAAKqP,aAAL,EAAmBhP,SAAnB,uEACE,IAAKmJ,CAAAA,aAAL,CAAmB5S,IADrB,EAEE,IAFF,EAGE,IAAA,CAAK4S,aAAL,CAAmBrH,SAHrB,EAIE,IAAKqH,CAAAA,aAAL,CAAmBxJ,OAJrB,CAAA,CAAA;EAMD,SAZD,MAYO,IAAIhN,OAAO,CAACmH,OAAZ,EAAqB;EAAA,UAAA,IAAA,sBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,oBAAA,CAAA;;EAC1B,UAAA,CAAA,sBAAA,GAAA,CAAA,oBAAA,GAAA,IAAA,CAAKkV,aAAL,EAAmBlV,OAAnB,KACE,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAA,IAAA,CAAA,oBAAA,EAAA,IAAA,CAAKqP,aAAL,CAAmB5O,KADrB,EAEE,IAAA,CAAK4O,aAAL,CAAmBrH,SAFrB,EAGE,IAAKqH,CAAAA,aAAL,CAAmBxJ,OAHrB,CAAA,CAAA;EAKA,UAAA,CAAA,sBAAA,GAAA,CAAA,oBAAA,GAAA,IAAA,CAAKqP,aAAL,EAAmBhP,SAAnB,uEACEpQ,SADF,EAEE,KAAKuZ,aAAL,CAAmB5O,KAFrB,EAGE,IAAA,CAAK4O,aAAL,CAAmBrH,SAHrB,EAIE,IAAKqH,CAAAA,aAAL,CAAmBxJ,OAJrB,CAAA,CAAA;EAMD,SAAA;EACF,OA5BuB;;;QA+BxB,IAAIhN,OAAO,CAAC9D,SAAZ,EAAuB;EACrB,QAAA,IAAA,CAAKA,SAAL,CAAeuI,OAAf,CAAuB,CAAC;EAAEnI,UAAAA,QAAAA;EAAF,SAAD,KAAkB;YACvCA,QAAQ,CAAC,IAAKka,CAAAA,aAAN,CAAR,CAAA;WADF,CAAA,CAAA;EAGD,OAAA;OAnCH,CAAA,CAAA;EAqCD,GAAA;;EA5KD;;ECxBF;EAoCA;EAEA,SAASgG,iBAAT,CAA2Blc,QAA3B,EAAmE;IACjE,OAAO;EACLpB,IAAAA,WAAW,EAAEoB,QAAQ,CAACN,OAAT,CAAiBd,WADzB;MAELkB,KAAK,EAAEE,QAAQ,CAACF,KAAAA;KAFlB,CAAA;EAID;EAGD;EACA;EACA;;;EACA,SAASqc,cAAT,CAAwBjd,KAAxB,EAAuD;IACrD,OAAO;MACLY,KAAK,EAAEZ,KAAK,CAACY,KADR;MAELrB,QAAQ,EAAES,KAAK,CAACT,QAFX;MAGLe,SAAS,EAAEN,KAAK,CAACM,SAAAA;KAHnB,CAAA;EAKD,CAAA;;EAEM,SAAS4c,8BAAT,CAAwCpc,QAAxC,EAA4D;EACjE,EAAA,OAAOA,QAAQ,CAACF,KAAT,CAAeoP,QAAtB,CAAA;EACD,CAAA;EAEM,SAASmN,2BAAT,CAAqCnd,KAArC,EAAmD;EACxD,EAAA,OAAOA,KAAK,CAACY,KAAN,CAAYK,MAAZ,KAAuB,SAA9B,CAAA;EACD,CAAA;EAEM,SAASmc,SAAT,CACLtO,MADK,EAELtO,OAAyB,GAAG,EAFvB,EAGY;IACjB,MAAM2P,SAA+B,GAAG,EAAxC,CAAA;IACA,MAAMxB,OAA0B,GAAG,EAAnC,CAAA;;EAEA,EAAA,IAAInO,OAAO,CAAC6c,kBAAR,KAA+B,KAAnC,EAA0C;EACxC,IAAA,MAAMC,uBAAuB,GAC3B9c,OAAO,CAAC8c,uBAAR,IAAmCJ,8BADrC,CAAA;MAGApO,MAAM,CACH4F,gBADH,EAEGtF,CAAAA,MAFH,GAGGnK,OAHH,CAGYnE,QAAD,IAAc;EACrB,MAAA,IAAIwc,uBAAuB,CAACxc,QAAD,CAA3B,EAAuC;EACrCqP,QAAAA,SAAS,CAAC5G,IAAV,CAAeyT,iBAAiB,CAAClc,QAAD,CAAhC,CAAA,CAAA;EACD,OAAA;OANL,CAAA,CAAA;EAQD,GAAA;;EAED,EAAA,IAAIN,OAAO,CAAC+c,gBAAR,KAA6B,KAAjC,EAAwC;EACtC,IAAA,MAAMC,oBAAoB,GACxBhd,OAAO,CAACgd,oBAAR,IAAgCL,2BADlC,CAAA;MAGArO,MAAM,CACHyE,aADH,EAEGnE,CAAAA,MAFH,GAGGnK,OAHH,CAGYjF,KAAD,IAAW;EAClB,MAAA,IAAIwd,oBAAoB,CAACxd,KAAD,CAAxB,EAAiC;EAC/B2O,QAAAA,OAAO,CAACpF,IAAR,CAAa0T,cAAc,CAACjd,KAAD,CAA3B,CAAA,CAAA;EACD,OAAA;OANL,CAAA,CAAA;EAQD,GAAA;;IAED,OAAO;MAAEmQ,SAAF;EAAaxB,IAAAA,OAAAA;KAApB,CAAA;EACD,CAAA;EAEM,SAAS8O,OAAT,CACL3O,MADK,EAEL4O,eAFK,EAGLld,OAHK,EAIC;IACN,IAAI,OAAOkd,eAAP,KAA2B,QAA3B,IAAuCA,eAAe,KAAK,IAA/D,EAAqE;EACnE,IAAA,OAAA;EACD,GAAA;;EAED,EAAA,MAAMlO,aAAa,GAAGV,MAAM,CAAC4F,gBAAP,EAAtB,CAAA;EACA,EAAA,MAAMpC,UAAU,GAAGxD,MAAM,CAACyE,aAAP,EAAnB,CANM;;IASN,MAAMpD,SAAS,GAAIuN,eAAD,CAAqCvN,SAArC,IAAkD,EAApE,CATM;;EAWN,EAAA,MAAMxB,OAAO,GAAI+O,eAAD,CAAqC/O,OAArC,IAAgD,EAAhE,CAAA;EAEAwB,EAAAA,SAAS,CAAClL,OAAV,CAAmB0Y,kBAAD,IAAwB;EAAA,IAAA,IAAA,qBAAA,CAAA;;EACxCnO,IAAAA,aAAa,CAACX,KAAd,CACEC,MADF,EAEE,EACE,IAAGtO,OAAH,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,qBAAA,GAAGA,OAAO,CAAEkK,cAAZ,KAAG,IAAA,GAAA,KAAA,CAAA,GAAA,qBAAA,CAAyByF,SAA5B,CADF;QAEEzQ,WAAW,EAAEie,kBAAkB,CAACje,WAAAA;OAJpC,EAMEie,kBAAkB,CAAC/c,KANrB,CAAA,CAAA;KADF,CAAA,CAAA;IAWA+N,OAAO,CAAC1J,OAAR,CAAgB,CAAC;MAAE1F,QAAF;MAAYqB,KAAZ;EAAmBN,IAAAA,SAAAA;EAAnB,GAAD,KAAoC;EAAA,IAAA,IAAA,sBAAA,CAAA;;MAClD,MAAMN,KAAK,GAAGsS,UAAU,CAACjF,GAAX,CAAe/M,SAAf,CAAd,CADkD;;EAIlD,IAAA,IAAIN,KAAJ,EAAW;QACT,IAAIA,KAAK,CAACY,KAAN,CAAY0K,aAAZ,GAA4B1K,KAAK,CAAC0K,aAAtC,EAAqD;EACnD;EACA;UACA,MAAM;EAAEnL,UAAAA,WAAW,EAAEyd,QAAf;YAAyB,GAAGC,oBAAAA;EAA5B,SAAA,GAAqDjd,KAA3D,CAAA;UACAZ,KAAK,CAACwL,QAAN,CAAeqS,oBAAf,CAAA,CAAA;EACD,OAAA;;EACD,MAAA,OAAA;EACD,KAZiD;;;EAelDvL,IAAAA,UAAU,CAACzD,KAAX,CACEC,MADF,EAEE,EACE,IAAGtO,OAAH,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,sBAAA,GAAGA,OAAO,CAAEkK,cAAZ,KAAG,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAyBiE,OAA5B,CADF;QAEEpP,QAFF;EAGEe,MAAAA,SAAAA;EAHF,KAFF;EAQE;EACA,IAAA,EACE,GAAGM,KADL;EAEET,MAAAA,WAAW,EAAE,MAAA;OAXjB,CAAA,CAAA;KAfF,CAAA,CAAA;EA8BD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}