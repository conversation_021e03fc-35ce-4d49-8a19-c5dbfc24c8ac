{"ast": null, "code": "'use client';\n\nexport { default } from './Checkbox';\nexport { default as checkboxClasses } from './checkboxClasses';\nexport * from './checkboxClasses';", "map": {"version": 3, "names": ["default", "checkboxClasses"], "sources": ["C:/GPT4_PROJECTS/DENTAL_LAB_ARCHITECTURE_PATTERNS/dentflow/frontend/node_modules/@mui/material/Checkbox/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Checkbox';\nexport { default as checkboxClasses } from './checkboxClasses';\nexport * from './checkboxClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASA,OAAO,IAAIC,eAAe,QAAQ,mBAAmB;AAC9D,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}