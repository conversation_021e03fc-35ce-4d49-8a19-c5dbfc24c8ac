"""
DentFlow Bootstrap System
Central dependency injection and system initialization
Connects all layers together with proper separation of concerns
"""

from typing import Dict, Optional, Any
from dataclasses import dataclass
import logging
from django.conf import settings

from domain import messagebus, events, commands
from services import handlers, unit_of_work
from infrastructure.repository import DjangoCaseRepository, FakeCaseRepository


logger = logging.getLogger(__name__)


@dataclass
class Dependencies:
    """Container for all system dependencies"""
    uow: unit_of_work.AbstractUnitOfWork
    message_bus: messagebus.MessageBus


class Bootstrap:
    """
    Bootstrap system for DentFlow
    Handles dependency injection and system configuration
    """
    
    def __init__(self, tenant_id: Optional[str] = None, testing: bool = False):
        self.tenant_id = tenant_id
        self.testing = testing
        self._dependencies: Optional[Dependencies] = None
    
    def get_dependencies(self) -> Dependencies:
        """Get or create dependencies (singleton pattern)"""
        if self._dependencies is None:
            self._dependencies = self._build_dependencies()
        return self._dependencies
    
    def _build_dependencies(self) -> Dependencies:
        """Build complete dependency graph"""
        
        # 1. Build Unit of Work
        if self.testing:
            uow = unit_of_work.FakeUnitOfWork(tenant_id=self.tenant_id)
        else:
            uow = unit_of_work.DjangoUnitOfWork(tenant_id=self.tenant_id)
        
        # 2. Build message bus with handlers
        message_bus = self._build_message_bus(uow)
        
        return Dependencies(
            uow=uow,
            message_bus=message_bus
        )
    
    def _build_message_bus(self, uow: unit_of_work.AbstractUnitOfWork) -> messagebus.MessageBus:
        """Build message bus with dependency-injected handlers"""
        
        # Event handlers (side effects)
        event_handlers = {
            events.CaseCreated: [
                lambda e: self._send_case_confirmation_email(e),
                lambda e: handlers.auto_assign_technician(e, uow),
            ],
            events.CaseStatusChanged: [
                lambda e: self._notify_status_change(e),
                lambda e: handlers.update_sla_tracking(e, uow),
            ],
            events.TechnicianAssigned: [
                lambda e: self._notify_technician_assignment(e),
            ],
            events.FileUploaded: [
                lambda e: self._process_file_upload(e),
            ],
        }
        
        # Command handlers (state changes)
        command_handlers = {
            commands.CreateCase: lambda c: handlers.create_case(c, uow),
            commands.AdvanceCase: lambda c: handlers.advance_case(c, uow),
            commands.AssignTechnician: lambda c: handlers.assign_technician(c, uow),
            # commands.UploadFile: lambda c: handlers.upload_file(c, file_storage, uow),
        }
        
        return messagebus.MessageBus(
            event_handlers=event_handlers,
            command_handlers=command_handlers,
            uow=uow
        )
    
    # Event handler implementations (these would use injected services)
    def _send_case_confirmation_email(self, event: events.CaseCreated):
        """Send case confirmation email"""
        try:
            # This would use an injected email service
            logger.info(f"Sending confirmation email for case {event.case_id}")
            # email_service.send_case_confirmation(event)
        except Exception as e:
            logger.error(f"Failed to send confirmation email: {e}")
    
    def _notify_status_change(self, event: events.CaseStatusChanged):
        """Notify relevant parties of status change"""
        try:
            logger.info(f"Notifying status change for case {event.case_id}: {event.from_status} -> {event.to_status}")
            # notification_service.send_status_notification(event)
        except Exception as e:
            logger.error(f"Failed to send status notification: {e}")
    
    def _notify_technician_assignment(self, event: events.TechnicianAssigned):
        """Notify technician of assignment"""
        try:
            logger.info(f"Notifying technician {event.technician_id} of assignment to case {event.case_id}")
            # notification_service.send_assignment_notification(event)
        except Exception as e:
            logger.error(f"Failed to send assignment notification: {e}")
    
    def _process_file_upload(self, event: events.FileUploaded):
        """Process uploaded file (extract metadata, virus scan, etc.)"""
        try:
            logger.info(f"Processing uploaded file {event.filename} for case {event.case_id}")
            # file_processor.process_file(event)
        except Exception as e:
            logger.error(f"Failed to process file upload: {e}")


# Global bootstrap instances (one per tenant)
_bootstrap_instances: Dict[str, Bootstrap] = {}


def get_bootstrap(tenant_id: Optional[str] = None, testing: bool = False) -> Bootstrap:
    """Get or create bootstrap instance for tenant"""
    key = f"{tenant_id}:{testing}"
    
    if key not in _bootstrap_instances:
        _bootstrap_instances[key] = Bootstrap(tenant_id, testing)
    
    return _bootstrap_instances[key]


def clear_bootstrap_cache():
    """Clear bootstrap cache - useful for testing"""
    global _bootstrap_instances
    _bootstrap_instances.clear()


def get_message_bus(tenant_id: Optional[str] = None, testing: bool = False) -> messagebus.MessageBus:
    """Convenience function to get message bus"""
    bootstrap = get_bootstrap(tenant_id, testing)
    dependencies = bootstrap.get_dependencies()
    return dependencies.message_bus


def get_uow(tenant_id: Optional[str] = None, testing: bool = False) -> unit_of_work.AbstractUnitOfWork:
    """Convenience function to get Unit of Work"""
    bootstrap = get_bootstrap(tenant_id, testing)
    dependencies = bootstrap.get_dependencies()
    return dependencies.uow


# Django integration
class DjangoBootstrap:
    """
    Django-specific bootstrap integration
    Integrates with Django request/response cycle
    """
    
    @staticmethod
    def get_message_bus_for_request(request) -> messagebus.MessageBus:
        """Get message bus for Django request"""
        tenant_id = getattr(request, 'tenant_id', None)
        testing = getattr(settings, 'TESTING', False)
        
        return get_message_bus(tenant_id, testing)
    
    @staticmethod
    def get_uow_for_request(request) -> unit_of_work.AbstractUnitOfWork:
        """Get Unit of Work for Django request"""
        tenant_id = getattr(request, 'tenant_id', None)
        testing = getattr(settings, 'TESTING', False)
        
        return get_uow(tenant_id, testing)