"""
Django management command for workflow automation
Processes workflow transitions, SLA monitoring, and auto-assignments
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.cases.models import Case as DjangoCase, Tenant
from bootstrap import get_message_bus
from domain import commands
from datetime import datetime, timedelta
import logging


logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Run workflow automation tasks'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--tenant-id',
            type=str,
            help='Process workflows for specific tenant only'
        )
        parser.add_argument(
            '--auto-assign',
            action='store_true',
            help='Run auto-assignment for unassigned cases'
        )
        parser.add_argument(
            '--sla-check',
            action='store_true',
            help='Check for SLA violations and send alerts'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes'
        )
    
    def handle(self, *args, **options):
        """Run workflow automation tasks"""
        
        tenants = self._get_tenants(options.get('tenant_id'))
        
        for tenant in tenants:
            self.stdout.write(f'Processing workflows for tenant: {tenant.name}')
            
            if options.get('auto_assign'):
                self._auto_assign_technicians(tenant, options.get('dry_run', False))
            
            if options.get('sla_check'):
                self._check_sla_violations(tenant, options.get('dry_run', False))
            
            # Always run workflow progression check
            self._check_workflow_progression(tenant, options.get('dry_run', False))
    
    def _get_tenants(self, tenant_id=None):
        """Get tenants to process"""
        if tenant_id:
            return [Tenant.objects.get(id=tenant_id, is_active=True)]
        return Tenant.objects.filter(is_active=True)
    
    def _auto_assign_technicians(self, tenant, dry_run=False):
        """Auto-assign technicians to unassigned cases"""
        unassigned_cases = DjangoCase.objects.filter(
            tenant=tenant,
            assigned_technician_id__isnull=True,
            status__in=['received', 'design', 'milling', 'sintering'],
            deleted_at__isnull=True
        ).order_by('priority', 'created_at')
        
        self.stdout.write(f'  Found {unassigned_cases.count()} unassigned cases')
        
        assigned_count = 0
        message_bus = get_message_bus(tenant_id=str(tenant.id))
        
        for django_case in unassigned_cases:
            try:
                # Convert to domain case to get current stage
                domain_case = django_case.to_domain()
                
                if not domain_case.workflow_stages:
                    continue
                
                current_stage = domain_case.workflow_stages[domain_case.current_stage_index]
                
                # Find available technician for this stage
                technician_id = self._find_available_technician(
                    current_stage.department, 
                    str(tenant.id)
                )
                
                if technician_id:
                    if not dry_run:
                        command = commands.AssignTechnician(
                            case_id=django_case.id,
                            technician_id=technician_id,
                            stage=current_stage.name,
                            tenant_id=str(tenant.id)
                        )
                        
                        message_bus.handle(command)
                        assigned_count += 1
                    
                    self.stdout.write(
                        f'    {"[DRY RUN] " if dry_run else ""}Assigned {technician_id} '
                        f'to case {django_case.id} ({current_stage.name})'
                    )
                
            except Exception as e:
                logger.error(f'Failed to auto-assign case {django_case.id}: {e}')
                continue
        
        if not dry_run:
            self.stdout.write(f'  Auto-assigned {assigned_count} cases')
    
    def _check_sla_violations(self, tenant, dry_run=False):
        """Check for SLA violations and send alerts"""
        now = timezone.now()
        
        # Cases approaching due date (within 2 hours)
        approaching_due = DjangoCase.objects.filter(
            tenant=tenant,
            due_date__lte=now + timedelta(hours=2),
            due_date__gt=now,
            status__in=['received', 'design', 'milling', 'sintering', 'qc'],
            deleted_at__isnull=True
        )
        
        # Overdue cases
        overdue_cases = DjangoCase.objects.filter(
            tenant=tenant,
            due_date__lt=now,
            status__in=['received', 'design', 'milling', 'sintering', 'qc'],
            deleted_at__isnull=True
        )
        
        self.stdout.write(f'  Found {approaching_due.count()} cases approaching due date')
        self.stdout.write(f'  Found {overdue_cases.count()} overdue cases')
        
        # Send alerts (in real implementation)
        for case in approaching_due:
            hours_until_due = (case.due_date - now).total_seconds() / 3600
            self.stdout.write(
                f'    {"[DRY RUN] " if dry_run else ""}ALERT: Case {case.id} '
                f'due in {hours_until_due:.1f} hours'
            )
        
        for case in overdue_cases:
            hours_overdue = (now - case.due_date).total_seconds() / 3600
            self.stdout.write(
                f'    {"[DRY RUN] " if dry_run else ""}OVERDUE: Case {case.id} '
                f'overdue by {hours_overdue:.1f} hours'
            )
    
    def _check_workflow_progression(self, tenant, dry_run=False):
        """Check for cases that can be automatically progressed"""
        # This would check for automated workflow transitions
        # For example, cases where sintering time has completed
        
        stalled_cases = DjangoCase.objects.filter(
            tenant=tenant,
            status='sintering',
            updated_at__lt=timezone.now() - timedelta(hours=8),  # 8+ hours in sintering
            deleted_at__isnull=True
        )
        
        self.stdout.write(f'  Found {stalled_cases.count()} potentially stalled cases')
        
        for case in stalled_cases:
            self.stdout.write(
                f'    {"[DRY RUN] " if dry_run else ""}Case {case.id} '
                f'has been in sintering for {(timezone.now() - case.updated_at).days} days'
            )
    
    def _find_available_technician(self, department, tenant_id):
        """Find available technician for department"""
        # This would integrate with a real technician scheduling system
        # For now, return mock technician IDs
        mock_technicians = {
            'CAD': 'tech_001',
            'CAM': 'tech_002',
            'Furnace': 'tech_003',
            'Printing': 'tech_004',
            'QC': 'tech_005'
        }
        
        return mock_technicians.get(department)